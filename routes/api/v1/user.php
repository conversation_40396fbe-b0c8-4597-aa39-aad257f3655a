<?php

use Illuminate\Support\Facades\Route;

Route::group(['namespace' => 'Api\\v1', 'prefix' => 'v1'], function () {

    Route::get('/products', 'ProductController@index');

    Route::group(['prefix' => '/product'], function () {
        Route::get('/{slug}', 'ProductController@show');
        Route::post('/review', 'ProductController@review');
        Route::get('/{productId}/comments', 'ProductController@comments');
        Route::post('/comment/create', 'ProductController@comment_create')->name('product.comment.create');
    });

    Route::get('/categories', 'ProductController@categories');

    Route::group(['prefix' => '/cart'], function () {
        Route::post('/review', 'CartController@review');
    });

    Route::get('/articles', 'ArticleController@index');
    Route::group(['prefix' => '/article'], function () {
        Route::get('/categories', 'ArticleController@categories');
        Route::get('/{slug}', 'ArticleController@show');
        Route::get('/{productId}/comments', 'ArticleController@comments');
        Route::post('/comment/create', 'ArticleController@comment_create')->name('article.comment.create');
    });

    Route::get('/delivery-location', 'CheckoutController@deliveryLocation')->name('checkout.delivery-location');
    Route::post('/checkout', 'CheckoutController@payment')->name('checkout.payment');
    Route::get('/verfication', 'CheckoutController@verfication')->name('checkout.verfication');
    Route::get('/invoice', 'InvoiceController@index')->name('invoice.index');
    Route::get('/{invoiceId}/show', 'InvoiceController@index')->name('invoice.show');


    Route::get('/orders', 'OrdersController@index')->name('orders.index');
    Route::get('/order/{orderId}', 'OrdersController@show')->name('orders.show');

    // Route::post('/product/view', 'ProductController@views');

});
