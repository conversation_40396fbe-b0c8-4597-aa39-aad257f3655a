<?php

use App\Http\Controllers\ExportController;

use App\Http\Middleware\UserActivity;
use App\Jobs\CacheProductDataJob;
use App\Jobs\UpdateAmountProductsJob;
use App\Models\Factor;
use App\Models\FactorOrderEmpty;
use App\Models\Setting;
use App\Models\User;
use Dedoc\Scramble\Scramble;
use Illuminate\Support\Facades\DB;
// Route::get('/sitemap.xml', 'SitemapController@index');

use Illuminate\Support\Facades\Route;

// Route::view('ocr', 'ocr');


Route::get('/p/{productId}', function ($productId) {
    $product = DB::table('product_details_view')->where('product_detail_id', unHashId($productId))
        ->firstOrFail();
    $setting = Setting::whereIn('type', [
        'card_number',
        'fullname_card_number',
        'sheba_card_number'
    ])->pluck('body', 'type')->toArray();
    return view('fronend-shop.index', compact('product', 'setting'));
});


Route::get('/survey/{orderId}', function ($orderId) {
    return view('survey.index', compact('orderId'));
});

Route::get('/ip-debug', function (Illuminate\Http\Request $request) {
    return response()->json([
        'request_ip' => $request->ip(),
        'forwarded_for' => $request->header('X-Forwarded-For'),
        'ips' => $request->ips(),
    ]);
});

Route::get('p/{productId}/{detailId}/{userId}', function ($productId, $detailId, $userId) {

    return view('factor.product', compact('productId', 'detailId', 'userId'));
})->name('show-product-user');

// Route::post('/generate/payment', 'Admin\PaymentController@orderGenerate')->name('generate-payemnt');

Route::get('/payment/factor/verfication', 'Admin\PaymentController@verfication')->name('factor-payment-verfication');

Route::get('/', 'HomeController@index');

Route::get('/factor/{factorId}', 'Admin\FactorController@show_user_factor')->name('show-order-user-factor');

Route::get('/factor/{factorId}/payment', 'Admin\PaymentController@payment')->name('factor-payment');

Route::get('/payment/verfication', 'Admin\FactorController@payment_verfication');

Route::get('/f/{request}', 'Admin\ShortLinkController@index');

Route::get('/factor-sort-factor', function () {
    $chunkSize = 1000;
    $factorId = 1;

    FactorOrderEmpty::chunkById($chunkSize, function ($factors) use (&$factorId) {
        foreach ($factors as $item) {
            $item->update([
                'factor_number' => $factorId,
            ]);
            $factorId++;
        }
    });

    dd('finsh factors');
});

Route::group(['middleware' => ['auth:web', UserActivity::class]], function () {

    // if (app()->environment('local')) {
    Route::get('/test', 'TestController@index');
    Route::get('/order-test', 'TestController@order_latest');
    // }

    Route::view('/', 'index');

    Route::get('/export/products', [ExportController::class, 'exportProducts'])->name('export-product');
    Route::get('/export/product-details', [ExportController::class, 'exportProductDetails'])->name('export-product-with-details');

    Route::get('/products-update', function () {
        UpdateAmountProductsJob::dispatch();

        return dd('run job ManagePendingOrdersJob success!');
    });

    Scramble::registerUiRoute('docs/v1');
    Scramble::registerJsonSpecificationRoute('api.json');

    // Route::view('/docs/api', 'api.document');

    Route::get('/products-cache', function () {
        CacheProductDataJob::dispatch();
    });

    Route::get('/api/document', function () {
        return view('api.document');
    });

    // Route::get('/payment/factor/verfication', 'Admin\PaymentController@verfication')->name('factor-payment-verfication');

    // Route::get('/', 'HomeController@index');
    // Route::get('/factor/{factorId}', 'Admin\FactorController@show_user_factor')->name('show-order-user-factor');
    // Route::get('/factor/{factorId}/payment', 'Admin\PaymentController@payment')->name('factor-payment');

    // Route::get('/payment/verfication', 'Admin\FactorController@payment_verfication');

    Route::get('/gold18K', function () {
        return getGold18k();
    });

    Route::group(['prefix' => 'orders'], function () {
        Route::get('/', 'Admin\OrdersController@index')->name('admin-dashboard-orders');
        Route::get('/{category}/create', 'Admin\OrdersController@create')->name('admin-dashboard-create-order');
        Route::get('/{orderId}/show', 'Admin\OrdersController@show')->name('admin-dashboard-show-order');
        Route::get('/{orderId}/print', 'Admin\OrdersController@print')->name('admin-dashboard-print-order');
        Route::get('/prints', 'Admin\OrdersController@prints')->name('admin-dashboard-prints-order');
        Route::get('/{orderId}/factor/print', 'Admin\OrdersController@factor_print')->name('admin-dashboard-factor-print-order');
        Route::get('/market', 'Admin\OrdersController@market')->name('admin-dashboard-market');
    });

    Route::get('/factor', 'Admin\FactorController@index')->name('admin-dashboard-factor');
    Route::get('/factor/print', 'Admin\FactorController@print')->name('admin-dashboard-factor-print');
    Route::get('/factors', 'Admin\FactorController@factors')->name('admin-dashboard-factors');

    Route::group(['prefix' => 'users'], function () {
        Route::get('/', 'Admin\UsersController@index')->name('admin-dashboard-users');
        Route::view('/role-and-permission', 'dashboard.admin.users.role-and-permission.role-and-permission-index')->name('admin-dashboard-role-and-permission');
    })->can('show-users');

    Route::group(['prefix' => 'subscribers'], function () {
        Route::get('/', 'Admin\SubscribersController@index')->name('admin-dashboard-subscribers');
    });

    Route::group(['prefix' => 'report'], function () {
        Route::view('/sms', 'dashboard.admin.reports.sms')->name('admin-dashboard-report-sms');
        Route::view('/commission', 'dashboard.admin.reports.commission')->name('admin-dashboard-report-commission');
        Route::view('/survey', 'dashboard.admin.reports.survey')->name('admin-dashboard-report-survey');
        Route::view('/financial-funds', 'dashboard.admin.reports.financial-funds.index')->name('admin-dashboard-report-financial-funds');
        Route::get('/financial-funds/{financialId}/transaction', 'Admin\FinancialController@index')->name('admin-dashboard-report-financial-transactions-funds');
    });

    Route::group(['prefix' => 'shop'], function () {
        Route::get('/', 'Admin\ShopController@index')->name('admin-dashboard-shop');
        Route::get('/{productId}/show', 'Admin\ShopController@show')->name('admin-dashboard-shop-show');
        Route::get('/transactions', 'Admin\ShopController@transactions')->name('admin-dashboard-shop-transactions');
        Route::get('/transactions/deposit', 'Admin\ShopController@deposits')
            ->name('admin-dashboard-shop-order-deposits');

    });

    Route::group(['prefix' => 'product'], function () {
        Route::get('/create', 'Admin\ProductController@create')->name('admin-dashboard-product-create');
        Route::get('{productId}/show', 'Admin\ProductController@show')->name('admin-dashboard-product-show');
        Route::get('{productId}/without-fee/show', 'Admin\ProductController@withoutـfeeـshow')->name('admin-dashboard-product-without-fee-show');
        Route::get('/{productId}/details/show', 'Admin\ProductDetailsController@show')->name('admin-dashboard-product-details-show');
    });

    Route::group(['prefix' => 'products'], function () {
        Route::get('/', 'Admin\ProductController@index')->name('admin-dashboard-products');
        // Route::get('/without-fee', 'Admin\ProductController@without_fee_list')->name('admin-dashboard-products-without-fee');
    });

    Route::group(['prefix' => 'without-fee\products'], function () {
        // Route::get('/', 'Admin\ProductController@index')->name('admin-dashboard-products');
        Route::get('/', 'Admin\ProductWithoutFeeController@index')->name('admin-dashboard-products-without-fee');
        Route::get('/create', 'Admin\ProductWithoutFeeController@create')->name('admin-dashboard-product-without-fee-create');
    });

    Route::prefix('setting')->name('admin.setting.')->group(function () {
        Route::get('/', 'Admin\SettingController@index')->name('index');
        Route::view('/document/api', 'dashboard.admin.api-document.api-document-index')->name('document-api');
    });

    Route::view('/order-online', 'dashboard.admin.order-online.index')->name('admin-dashboard-order-online');
    Route::view('/transactions', 'dashboard.admin.transactions.index')->name('admin-dashboard-transactions');

});

require __DIR__ . '/auth.php';
require __DIR__ . '/dashboard/admin.php';
require __DIR__ . '/dashboard/user.php';
