# Stage 1: Build frontend with Vite
FROM node:23-alpine AS frontend

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build


# Stage 2: PHP with nginx
FROM webdevops/php-nginx:8.4-alpine

ENV WEB_DOCUMENT_ROOT=/app/public
ENV PHP_DATE_TIMEZONE="Asia/Tehran"

WORKDIR /app

# 🟡 اول پروژه رو کامل کپی کن (شامل public خام)
COPY --chown=application:application . .

# ✅ حالا فقط خروجی نهایی build رو از مرحله اول جایگزین کن
COPY --from=frontend /app/public/build ./public/build

# نصب پکیج‌های PHP
RUN composer install --no-dev --optimize-autoloader

# نصب tesseract و زبان فارسی در آلپاین
RUN apk add --no-cache tesseract-ocr tesseract-ocr-lang-fas

# دسترسی‌ها
RUN chown -R application:application /app
