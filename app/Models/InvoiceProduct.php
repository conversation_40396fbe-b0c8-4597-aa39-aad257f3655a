<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class InvoiceProduct extends Model
{
    protected $fillable = [
        'invoice_id', 'product_id', 'title_fa', 'title_eng', 'category_id', 'for_id', 'description', 'slug', 'count',
    ];

    public function productDetails()
    {
        return $this->hasMany(InvoiceProductDetail::class, 'invoice_id', 'invoice_id');
    }

    public function detail()
    {
        return $this->belongsTo(InvoiceProductDetail::class, 'id', 'product_id');
    }

    public function parent()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id', 'id');
    }

    public function gallery()
    {
        return $this->hasMany(ProductGallery::class, 'product_id', 'product_id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }


}
