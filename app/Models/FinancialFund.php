<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FinancialFund extends Model
{

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'status',
        'terminal_id',
    ];

    public function transactions()
    {
        return $this->hasMany(FinancialTransaction::class);
    }

    public function transactionsSumByPrimary()
    {
        return $this->transactions()
            ->where('primary', true)
            ->sum('amount');
    }

    public function transactionsSumByType($type)
    {
        return $this->transactions()
            ->where('type', $type)
            ->sum('amount');
    }

    public function transactionsSum()
    {
        try {
            $deposit = $this->transactions()->where('type', 'deposit')->sum('amount');
            $withdrawal = $this->transactions()->where('type', 'withdrawal')->sum('amount');

            $deposit = is_numeric($deposit) ? $deposit : 0;
            $withdrawal = is_numeric($withdrawal) ? $withdrawal : 0;

            return $deposit - $withdrawal;
        } catch (\Exception $e) {
            // اگر خطایی رخ داد، می‌تونی لاگ بگیری یا صفر برگردونی
            // Log::error("Transaction sum error: " . $e->getMessage());
            return 0;
        }
    }


}
