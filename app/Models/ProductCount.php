<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductCount extends Model
{
    protected $fillable = [
        'product_id', 'product_detail_id', 'count', 'reserved_count', 'amount',
    ];

    // رزرو موجودی
    public function reserveStock($productDetailId, $count)
    {

        $productDetailCount = $this->where('product_detail_id', $productDetailId)->first();

        if ($productDetailCount && $productDetailCount->count >= $count) {
            $productDetailCount->update([
                'count' => $productDetailCount->count - $count,
            ]);

            return true;
        }

        return false;
    }

    // کسر موجودی در صورت پرداخت موفق
    public function deductStock($productDetailId, $count)
    {
        $productDetailCount = $this->where('product_detail_id', $productDetailId)->first();

        if ($productDetailCount) {
            $productDetailCount->update([
                'count' => $productDetailCount->count - $count,
            ]);
        }
    }

    // بازگرداندن موجودی در صورت لغو سفارش
    public function returnStock($productDetailId, $count)
    {
        $productDetailCount = $this->where('product_detail_id', $productDetailId)->first();

        if ($productDetailCount) {
            $productDetailCount->update([
                'count' => $productDetailCount->count + $count,
            ]);
        }
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function detail()
    {
        return $this->belongsTo(ProductDetail::class, 'product_detail_id');
    }
}
