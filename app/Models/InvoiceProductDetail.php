<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class InvoiceProductDetail extends Model
{
    protected $fillable = [
        'invoice_id', 'product_id', 'product_detail_id', 'eticket', 'weight', 'cutie', 'profit', 'color_id',
        'construction_wages', 'branch_id', 'count', 'amount',
        'chain_size', 'pluck', 'model', 'tax', 'fixed_amount',
    ];

    public function invoice()
    {
        return $this->belongsTo(Invoice::class, 'id', 'invoice_id');
    }

    public function invoiceOne()
    {
        return $this->hasOne(Invoice::class, 'invoice_id');
    }

    public function gallery()
    {
        return $this->hasMany(ProductGallery::class, 'product_id', 'product_id');
    }

    public function product()
    {
        return $this->belongsTo(InvoiceProduct::class, 'product_id', 'id');
    }

    public function getWeightAttribute($value)
    {
        return (float) $value;
    }

    public function color()
    {
        return $this->belongsTo(Optional::class, 'color_id', 'id');
    }
}
