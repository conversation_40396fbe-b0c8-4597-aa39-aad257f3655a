<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StatusHistory extends Model
{
    protected $fillable = [
        'user_id', 'sefaresh_id', 'last_status', 'date_last_status', 'manufacturer_id',
    ];

    public function manufacturer()
    {
        return $this->hasOne(Setting::class, 'id', 'manufacturer_id');
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
}
