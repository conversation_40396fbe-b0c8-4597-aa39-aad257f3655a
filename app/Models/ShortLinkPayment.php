<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShortLinkPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'factor_id', 'order_id', 'link', 'short', 'phone', 'fullname', 'status'
	];

    public function factor()
    {
        return $this->hasOne(FactorOrderEmpty::class, 'id', 'factor_id');
    }

}
