<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FactorOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'subscribe_id',
        'order_id',
        'fullname',
        'phone',
        'phone_r',
        'fullname_r',
        'gift',
        'factor_number',
        'factor_create_date',
        'subscribe_code',
        'factor_total',
        'factor_deposit',
        'factor_discount',
        'total',
        'countWeight',
        'post',
        'user_attach',
        'description'
    ];

}
