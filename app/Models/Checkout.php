<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Checkout extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'product_id', 'count', 'weight_id',
    ];

    public function product()
    {
        return $this->hasOne(Product::class, 'id', 'product_id');
    }

    public function detail()
    {
        return $this->hasOne(ProductDetail::class, 'id', 'weight_id');
    }

    public function details()
    {
        return $this->hasOne(ProductDetail::class, 'id', 'weight_id');
    }
}
