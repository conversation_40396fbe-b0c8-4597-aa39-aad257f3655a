<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductWithoutFee extends Model
{
    protected $fillable = [
        'user_id',
        'title',
        'fullname_customer',
        'phone',
        'national_number',
        'weight',
        'work_rate',
        'payer_details',
        'description',
        'status',
        'bank',
        'product_id',
    ];

    public function gallery()
    {
        return $this->hasMany(ProductGallery::class, 'product_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }
}
