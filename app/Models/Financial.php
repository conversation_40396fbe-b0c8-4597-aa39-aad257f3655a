<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Financial extends Model
{
    protected $fillable = [
        'user_id', 'sefaresh_id', 'receiver_order', 'packing', 'package_amount', 'amount_ready', 'designing', 'sender', 'total_amount',
        'profit', 'tipsGold', 'chain', 'commission',
    ];

    public function sefaresh()
    {
        return $this->hasOne(Sefaresh::class, 'sefaresh_id');
    }
}
