<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transfer extends Model
{
    use HasFactory;
	
	 protected $fillable = [
        'user_id','fullname_sefaresh','fullname_card','sefaresh_id','sefaresh_code','checkbox','file','card',
		'shaba','tips','img_subscribe1','img_subscribe2','img_subscribe3',
		'img_factor1','img_factor2','img_factor3','img_varizi1','img_varizi2','img_varizi3','lock','price','mobile'
    ];
}
