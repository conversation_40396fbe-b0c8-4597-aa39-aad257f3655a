<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CartProduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'product_id', 'orderNumber', 'code', 'name', 'group', 'weight', 'hire', 'profit', 'tax', 'location_id', 'count', 'total', 'tips', 'photo', 'money', 'expire_date',
    ];

    public function location()
    {
        return $this->belongsTo(Setting::class);
    }

    public function scopeCurrentUser($query)
    {
        return $query->where('user_id', auth()->user()->id);
    }

    public function payment()
    {
        return $this->belongsTo(Payment::class, 'orderNumber', 'paymentNumber');
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
}
