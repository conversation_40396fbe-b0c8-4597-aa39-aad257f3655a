<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SurveyResponse extends Model
{
    protected $fillable = [
        'user_id', 'survey_id', 'answers',
    ];

    public function answerItems()
    {
        return $this->hasMany(SurveyAnswer::class, 'response_id', 'id');
    }

    public function getAnswersAttribute($value)
    {
        return json_decode($value, true);
    }

    public function order()
    {
        return $this->belongsTo(Sefaresh::class, 'survey_id', 'id');
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function userRecipient()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
}
