<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserAddress extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'address',
        'district',
        'plaque',
        'unit',
        'is_receiver',
        'state_id',
        'city_id',
        'zipcode',
        'fullname',
        'phone',
        'lat',
        'lng',
    ];

    public function state()
    {
        return 'خوزستان';
    }

    public function city()
    {
        return 'اهواز';
    }
}
