<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FactorOrderEmpty extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'order_id',
        'subscribe_id',
        'fullname',
        'phone',
        'phone_r',
        'fullname_r',
        'gift',
        'factor_number',
        'factor_create_date',
        'subscribe_code',
        'factor_total',
        'factor_deposit',
        'factor_discount',
        'total',
        'countWeight',
        'post',
        'user_attach',
        'description'
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function order()
    {
        return $this->hasOne(Sefaresh::class, 'id', 'order_id');
    }

    public function factorItem()
    {
        return $this->hasOne(FactorItemEmpty::class, 'order_id', 'order_id');
    }
}
