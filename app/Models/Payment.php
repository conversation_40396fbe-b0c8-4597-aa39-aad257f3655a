<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'paymentNumber', 'url',  'amount', 'merchantid', 'description', 'fullname', 'mobile', 'codepost', 'address', 'status', 'expire_date',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
