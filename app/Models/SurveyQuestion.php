<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SurveyQuestion extends Model
{
    protected $fillable = [
        'question_text',
        'question_type',
        'is_required',
    ];

    /**
     * رابطهٔ یک به چند با مدل SurveyOption
     */
    public function options(): HasMany
    {
        return $this->hasMany(SurveyOption::class, 'question_id');
    }
}
