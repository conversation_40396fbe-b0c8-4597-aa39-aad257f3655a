<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FinancialTransaction extends Model
{
    protected $fillable = [
        'user_id',
        'financial_fund_id',
        'primary',
        'type',             // deposit یا withdrawal
        'amount',
        'description',
        'subscriber_id',
        'order_id',
    ];

    public function fund()
    {
        return $this->belongsTo(FinancialFund::class, 'financial_fund_id', 'id');
    }

    public function order()
    {
        return $this->belongsTo(Sefaresh::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function customer()
    {
        return $this->belongsTo(Subscribe::class);
    }
}
