<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    protected $fillable = [
        'user_id', 'title_fa',
        'title_eng', 'category_id', 'for_id',
        'slug', 'meta_description',
        'meta_keywords', 'description', 'fixed_amount', 'fixed_commission', 'reserved_count', 'out_stock',
    ];

    public function detail()
    {
        return $this->hasOne(ProductDetail::class, 'product_id', 'id');
    }

    public function details()
    {
        return $this->hasMany(ProductDetail::class);
    }

    public function location()
    {
        return $this->belongsTo(Setting::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function cart()
    {
        return $this->belongsTo(CartProduct::class, 'code', 'code');
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function scopeCurrentUser($query)
    {
        return $query->where('user_id', auth()->user()->id);
    }

    public function gallery()
    {
        return $this->hasMany(ProductGallery::class, 'product_id', 'id');
    }

    public function comments()
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    public function productCount()
    {
        return $this->hasMany(ProductCount::class, 'id', 'product_id');
    }

    public function productCountItem()
    {
        return $this->belongsTo(ProductCount::class, 'id', 'product_id');
    }

    public function for()
    {
        return $this->belongsTo(Optional::class);
    }

    public function color()
    {
        return $this->belongsTo(Optional::class);
    }

    public function getProductCount()
    {
        $countItem = $this->productCountItem;

        $count = $this->productCountItem?->count - (int) ($this->reserved_count ?? 0);

        return $count;
    }

    public function getAvailableCountAttribute()
    {
        return $this->productCountItem ? $this->productCountItem->count - $this->reserved_count : 0;
    }

    public function counts()
    {
        return $this->hasMany(ProductCount::class, 'product_id');
    }
}
