<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ZinbalTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'paidAt',
        'cardNumber',
        'status',
        'amount',
        'refNumber',
        'description',
        'orderId',
        'result',
        'message',
        'trackId',
        'factorId',
    ];

    public function order()
    {
        return $this->hasOne(Sefaresh::class, 'id', 'orderId');
    }

    public function invoice()
    {
        return $this->hasOne(Invoice::class, 'id', 'orderId');
    }
}
