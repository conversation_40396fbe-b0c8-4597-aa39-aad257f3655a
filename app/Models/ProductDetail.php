<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductDetail extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'eticket',
        'weight',
        'cutie',
        'profit',
        'construction_wages',
        'color_id',
        'branch_id',
        'amount',
        'amount_after_offer',
        'tax',
        'stone_price',
        'chain_size',
        'pluck',
        'model',
        'reserved_count',
        'gold18k',
        'out_stock',
        'status',
    ];

    public function InvoiceProductDetail()
    {
        return $this->hasOne(InvoiceProductDetail::class, 'product_detail_id');
    }

    public function product()
    {
        return $this->hasOne(Product::class, 'id', 'product_id');
    }

    public function ProductCount()
    {
        return $this->hasOne(ProductCount::class, 'product_detail_id', 'id');
    }

    public function ProductCounts()
    {
        return $this->hasMany(ProductCount::class, 'product_detail_id', 'id');
    }

    public function productCountItem()
    {
        return $this->belongsTo(ProductCount::class, 'id', 'product_detail_id');
    }

    public function getTotalCountAttribute()
    {
        return $this->productCounts()->sum('count');
    }

    public function gallery()
    {
        return $this->hasMany(ProductGallery::class, 'product_id', 'product_id');
    }

    public function branch()
    {
        return $this->belongsTo(Optional::class, 'branch_id', 'id');
    }

    public function color()
    {
        return $this->belongsTo(Optional::class);
    }

    public function getProductDetailCount()
    {

        $count = (int) $this->productCountItem?->count - (int) $this->reserved_count;

        return $count;

    }

    public function getAvailableCountAttribute()
    {
        return $this->productCountItem ? $this->productCountItem->count - $this->reserved_count : 0;
    }

    public function counts()
    {
        return $this->hasMany(ProductCount::class, 'product_detail_id');
    }
}
