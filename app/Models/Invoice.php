<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Invoice extends Model
{
    protected $fillable = [
        'user_id', 'total_factor',  'discount', 'total_payment', 'fullname',
        'phone', 'zipcode', 'address', 'description', 'sender', 'status', 'gold18k', 'post', 'money_post',
        'order_status', 'image', 'post_type',
    ];

    public function order()
    {
        return $this->belongsTo(Sefaresh::class, 'invoice_id');
    }

    public function orderOne()
    {
        return $this->hasOne(Sefaresh::class, 'invoice_id', 'id');
    }

    public function products()
    {
        return $this->hasMany(InvoiceProduct::class);
    }

    public function productDetails()
    {
        return $this->hasMany(InvoiceProductDetail::class, 'invoice_id', 'id');
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function transaction()
    {
        return $this->belongsTo(ZinbalTransaction::class, 'id', 'orderId');
    }

    public function updateStatus($status)
    {
        DB::beginTransaction();

        try {
            $this->update(['status' => $status]);

            foreach ($this->details as $detail) {
                $productDetailCount = new ProductDetailCount;

                if ($status === 'success') {
                    // کسر موجودی
                    $productDetailCount->deductStock($detail->product_detail_id, $detail->count);
                } elseif ($status === 'reject') {
                    // بازگرداندن موجودی
                    $productDetailCount->returnStock($detail->product_detail_id, $detail->count);
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
