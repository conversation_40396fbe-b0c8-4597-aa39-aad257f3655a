<?php

// برای محصولات

namespace App\Exports;

use App\Models\Product;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ProductsExport implements FromCollection, WithHeadings
{
    public function collection()
    {
        return Product::whereNotNull('fixed_amount')
            ->get()
            ->map(function ($product) {
                return [
                    'title_fa' => $product->title_fa,
                    'fixed_amount' => $product->fixed_amount,
                    'eticket' => $product?->detail?->eticket,
                    'available_count' => $product->getProductCount() ?? 0,
                ];
            });
    }

    public function headings(): array
    {
        return ['عنوان فارسی', 'مقدار ثابت',  'اتیکت', 'موجودی'];
    }
}
