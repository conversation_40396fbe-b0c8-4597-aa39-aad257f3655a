<?php

namespace App\Exports;

use App\Models\ProductDetail;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ProductDetailsExport implements FromCollection, WithHeadings
{
    public function collection()
    {
        return ProductDetail::with('product')
            ->get()
            ->map(function ($detail) {
                return [
                    'title_fa' => $detail->product->title_fa,
                    'eticket' => $detail->eticket,
                    'weight' => $detail->weight,
                    'profit' => $detail->profit,
                    'construction_wages' => $detail->construction_wages,
                    'available_count' => $detail->available_count,
                    'branch' => $detail?->branch?->value,
                ];
            });
    }

    public function headings(): array
    {
        return [
            'عنوان فارسی',
            'ای‌تیکت',
            'وزن',
            'سود',
            'دستمزد ساخت',
            'موجودی',
            'شعبه'];
    }
}
