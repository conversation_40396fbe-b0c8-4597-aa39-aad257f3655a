<?php

namespace App\Exports;

use App\Models\Financial;
use App\Models\Sefaresh;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class SefareshsExportUser implements FromView
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function __construct($MonthlyOne, $MonthlyTow, $year, $user_id)
    {

        $this->MonthlyOne = $MonthlyOne;
        $this->MonthlyTow = $MonthlyTow;
        $this->year = $year;
        $this->user_id = $user_id;
    }

    public function view(): View
    {
        $orders = [];

        if (auth()->user()->level == 'admin') {
            $orders = Sefaresh::whereBetween('month', [$this->MonthlyOne, $this->MonthlyTow])
                ->where('year', $this->year)
                ->where('user_id', $this->user_id)
                ->with(['financial', 'userRecipient']) // هر چی ریلیشن لازمه
                ->get();
        } else {
            $orders = Sefaresh::where('user_id', auth()->user()->id)
                ->whereBetween('month', [$this->MonthlyOne, $this->MonthlyTow])
                ->where('year', $this->year)

                ->with(['financial', 'userRecipient'])
                ->get();
        }

        // تبدیل به آرایه با مقادیر صریح
        $data = $orders->map(function ($item) {
            $profit = $this->getFinancial($item->id)?->profit ? $this->getFinancial($item->id)?->profit : null;
            $commission = $this->getFinancial($item->id)?->commission ? $this->getFinancial($item->id)?->commission : null;

            if ($item->last_status == 'cancel') {
                $profit = 0;
                $commission = 0;
            }

            return [
                'code' => $item->code,
                'fullname' => $item->fullname,
                'phone' => $item->phone,
                'type' => $item->iType(),
                'gram' => $item->gram,
                'name_pluck' => $item->name_pluck,
                'model' => $item->model,
                'sex' => $item->sex,
                'color' => $item->color,
                'type_construction' => $item->type_construction,
                'chain' => $item->chain,
                'size' => $item->size,
                'post_type' => $item->post_type,
                'user_fullname' => optional($item->userRecipient)->fullname ?? $item->user_id,
                'user_mobile' => optional($item->userRecipient)->mobile ?? '',
                'user_username' => optional($item->userRecipient)->username ?? '',
                'status' => $item->iLastStatus()['message'] ?? '-',
                'total' => $item->sefaresh_total,
                'deposit1' => $item->deposit1,
                'deposit2' => $item->deposit2,
                'profit' => $profit,
                'commission' => $commission,
                'month' => $item->month,
                'year' => $item->year,
                'created_at' => shamsiDate($item->created_at),
                'last_status' => $item->last_status,
                'last_status_date' => $item->date_last_status,
            ];
        });

        return view('dashboard.admin.orders.export.orders-export-excel-all', [
            'orders' => $data,
        ]);
    }

    public function getFinancial($orderId)
    {
        $financial = Financial::where('sefaresh_id', $orderId)->first();
        if (isset($financial) && $financial != null) {
            return $financial;
        }
    }
}
