<?php

namespace App\Service\Actions\Order;

use App\Jobs\SendPayamakTransactionAdminJob;
use App\Jobs\SendPayamakTransactionJob;
use App\Models\Checkout;
use App\Models\Invoice;
use App\Models\InvoiceProduct;
use App\Models\InvoiceProductDetail;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Models\ShortLinkPayment;
use App\Models\Subscribe;
use App\Service\CacheClear;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class SetOnlineOrderAction
{
    public static function handle(array $data, int $userId, string $userMobile, string $userFullname, $baseUrlProduction): array
    {

        try {

            DB::beginTransaction();

            $checkouts = Checkout::with('product', 'details', 'product.gallery')->
                where('user_id', $userId)->get();

            $uniqueImages = $checkouts->map(function ($item) {
                return $item->product->gallery?->pluck('url')->first();
            })->filter()->unique()->values();

            $dataInvoice = [
                'product_count' => $checkouts->count(),
                // 'color' => getItemValue($checkouts->first()->detail?->color_id),
                'color' => 1,
                'product_image_1' => '/' . $uniqueImages->get(0) ?? null,
                'product_image_2' => '/' . $uniqueImages->get(1) ?? null,
                'product_image_3' => '/' . $uniqueImages->get(2) ?? null,
                'product_title' => $checkouts->count() == 1
                    ? $checkouts->first()->product->title_fa
                    : $checkouts->pluck('product.title_fa')->unique()->join(' + '),
            ];

            // foreach ($checkouts as $item) {
            //     $data['totalFactor'] += str_replace(',', '', getMoney($item->details));
            //     if ($item->product->fixed_amount != null) {
            //         $data['totalFactor'] += str_replace(',', '', $item->product->fixed_amount) * $item->count;
            //     }
            //     $data['totalWeight'] += $item->details?->weight;
            // }

            if (empty($data['totalFactor'])) {
                $data['complate'] = false;
                $data['getUserDeatail'] = false;
            }

            $totalFactor = str_replace(',', '', $data['totalFactor']);
            $totalPost = str_replace(',', '', $data['MoneyPostOnline']);
            $totalFactorWithPost = $totalFactor + $totalPost;

            $discount = (float) str_replace(',', '', $data['discount']);
            $total = $totalFactor - $discount;
            $totalBeforePost = $totalFactor - $discount;
            // dd($total);
            $invoice = Invoice::create([
                'user_id' => $userId,
                // 'discount' => $data['discount'],
                'total_factor' => $totalFactor - $totalPost,
                'total_payment' => $total,
                'fullname' => $data['fullname'],
                'phone' => $data['phone'],
                'zipcode' => $data['codepost'],
                'address' => $data['address'],
                'description' => $data['description'],
                'post' => $data['paymentOnlineChecked'],
                'post_type' => $data['post'],
                'money_post' => $totalPost,
                'gold18k' => (float) str_replace(',', '', optional($checkouts->first()->details)->gold18k
                    ?? optional($checkouts->get(1)?->details)->gold18k),

                'discount' => $discount,
                'image' => $dataInvoice['product_image_1'],
            ]);

            foreach ($checkouts as $item) {

                // if ($item->product->fixed_amount != null) {

                //     // dd((int) $item->count + (int) $item->product->reserved_count, (int) $item->count, (int) $item->product->reserved_count);
                //     // Product::whereId($item->product->id)->update([
                //     //     'reserved_count' => (int) $item->count + (int) $item->product->reserved_count,
                //     // ]);
                //     $product = Product::whereId($item->product->id)->first();
                //     $product->reserved_count = ((int) ($product->reserved_count ?? 0)) + (int) $item->count;
                //     $product->save();

                //     // CacheClear::clearKeys(['product_details_'.$item->product_id]);
                //     // Artisan::call('optimize:clear');
                // } else {

                // dd((int) $item->count + (int) $item->details->reserved_count, (int) $item->count, (int) $item->details->reserved_count);
                // ProductDetail::where('product_id', $item->product->id)->whereId($item->weight_id)->update([
                //     'reserved_count' => 1,
                // ]);
                $product = ProductDetail::whereId($item->details->id)->first();
                $product->reserved_count = (int) $product->reserved_count + (int) $item->count;
                $product->save();

                // CacheClear::clearKeys(['product_details_'.$item->product_id]);
                // Artisan::call('optimize:clear');
                // }

                $product = InvoiceProduct::create([
                    'invoice_id' => $invoice->id,
                    'product_id' => $item->product_id,
                    'title_fa' => $item->product->title_fa,
                    'title_eng' => $item->product->title_eng,
                    'category_id' => $item->product->category_id,
                    'for_id' => $item->product->for_id,
                    'description' => $item->product->description,
                    'slug' => $item->product->slug,
                    'count' => $item->count,

                ]);

                $amount = 0;
                if ($item->product->fixed_amount != null) {
                    $amount = str_replace(',', '', $item->product->fixed_amount) * $item->count;
                } else {
                    $amount = $item->details?->amount ?? getMoney($item->details, null, false);
                }

                InvoiceProductDetail::create([
                    'invoice_id' => $invoice->id,
                    'product_id' => $product->id,
                    'product_detail_id' => $item->details->id,
                    'eticket' => $item->details?->eticket,
                    'weight' => $item->details?->weight,
                    'cutie' => $item->details?->cutie,
                    'profit' => $item->details?->profit,
                    'color_id' => $item->details?->color_id,
                    'construction_wages' => $item->details?->construction_wages,
                    'branch_id' => $item->details?->branch_id,
                    'count' => $item->count,
                    'amount' => str_replace(',', '', $amount),
                    // 'chain_size' => $item->details?->chain_size,
                    'pluck' => $item->details?->pluck,
                    'model' => $item->details?->model,
                    'tax' => $item->details?->tax,
                    'fixed_amount' => $item->product->fixed_amount,
                    'chain_size' => str_replace(',', '', $item->details?->chain_size),
                    'stone_price' => str_replace(',', '', $item->details?->stone_price),
                ]);

            }

            if (auth()->check()) {
                Checkout::where('user_id', auth()->id())->delete();
            }

            $url = $baseUrlProduction . 'factor/' . hashId($invoice->id);

            $sortLink = generateUniqueShortLink(5);
            ShortLinkPayment::create([
                'user_id' => $userId,
                'order_id' => $invoice->id,
                'link' => $url,
                'short' => $sortLink,
                'phone' => $data['phone'],
                'fullname' => $data['fullname'],
                'status' => 1,
            ]);

            $link = $baseUrlProduction . 'f/' . $sortLink;
            $data['shortLink'] = $link;
            $time = date('H:i', time());

            if (app()->environment('production')) {
                SendPayamakTransactionJob::dispatch($data['phone'], $data['fullname'], $link);

                SendPayamakTransactionAdminJob::dispatch(
                    $userMobile,
                    $userFullname,
                    todays(),
                    $time,
                    $data['fullname'],
                    $data['phone'],
                    $link
                );
            }

            self::createSubscriber($data);

            DB::commit();

            $HashInvoiceId = hashId($invoice->id);

            return [
                'status' => true,
                'invoiceId' => $invoice->id,
                'HashInvoiceId' => $HashInvoiceId,
                'complate' => true,
                'shotLinkUrl' => $link,
            ];

        } catch (\Exception $e) {

            DB::Rollback();

            return [
                'status' => false,
                'message' => $e->getMessage(),
            ];

        }
    }

    private static function createSubscriber($data)
    {
        try {
            $first = Subscribe::whereMobile($data['phone'])->first();
            if (!isset($first)) {
                $latest = Subscribe::latest()->first();
                $code = '';
                if (isset($latest) && $latest->count() > 0) {
                    $code = $latest->code += 1;
                } else {
                    $code = 1;
                }
                $rCodePost = str_replace('-', '', $data['codepost']);
                Subscribe::Create([
                    'code' => $code,
                    'fullname' => $data['fullname'],
                    'codepost' => $rCodePost,
                    'mobile' => $data['phone'],
                    'address' => $data['address'],
                ]);

            }
        } catch (\Exception $e) {
        }

    }
}
