<?php

namespace App\Service\Actions;

use App\Jobs\SendPayamakTransactionAdminJob;
use App\Jobs\SendPayamakTransactionJob;
use App\Models\Checkout;
use App\Models\Invoice;
use App\Models\InvoiceProduct;
use App\Models\InvoiceProductDetail;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Models\ShortLinkPayment;
use App\Service\CacheClear;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class SetOnlineShopOrderAction
{
    public static function handle(array $data)
    {

        $responses = $data['responses'];

        $productIds = [];
        $productWeightIds = [];
        $duplicates = [];

        foreach ($responses as $response) {
            $productId = $response['productId'];
            $weightId = $response['weightId'] ?? null;
            $productWeightId = $productId.'-'.($weightId ?? 'null');

            if (in_array($productId, $productIds)) {
                $duplicates[] = [
                    'productId' => $productId,
                    'weightId' => null,
                ];
            } else {
                $productIds[] = $productId;
            }

            if (in_array($productWeightId, $productWeightIds)) {
                $duplicates[] = [
                    'productId' => $productId,
                    'weightId' => $weightId,
                ];
            } else {
                $productWeightIds[] = $productWeightId;
            }
        }

        if (! empty($duplicates)) {
            $response = [
                'success' => false,
                'message' => 'محصولات ارسالی شما تکراری دارد. لطفا اصلاح کنید دوباره درخواست خود را ارسال کنید',
                'data' => [
                    'errors' => [
                        'duplicates' => $duplicates,
                    ],
                ],
                'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
            ];

            return response()->json($response, Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        return [

            'info' => [
                'fullname' => $data['address']['fullname'],
                'phone' => $data['address']['phone'],
                'address' => $data['address']['address'],
            ],

            'invoice' => [
                'code' => rand(10000, 99999),
                'createAt' => todays(),
            ],

            'totalCheckout' => [
                'amount' => $data['totalCheckout'],
                'unit' => 'تومان',
            ],

            'post' => [
                'amount' => (float) str_replace(',', '', $data['post']['base_amount']),
                'unit' => 'تومان',
                'title' => $data['post']['title'],
            ],

            'totalFactor' => [
                'amount' => $data['totalFactor'],
                'unit' => 'تومان',
            ],

            'products' => $data['products'],

            'payment' => [
                'amount' => $data['totalFactor'],
                'status' => 'pending',
                'statusTitle' => 'درحال بررسی',
                'paymentUrl' => 'shop.tidamode.ir/f/'.generateUniqueShortLink(5),
            ],

            // 'address' => $data['address'],
        ];

        // return $data['responses'];

        // try {

        //     DB::beginTransaction();

        //     // $checkouts = Checkout::with('product', 'details', 'product.gallery')->where('user_id', $userId)->get();
        //     // $checkouts = $data['responses'];
        //     // $data['totalFactor'] = 0;
        //     // foreach ($checkouts as $item) {
        //     //     $data['totalFactor'] += str_replace(',', '', getMoney($item->details));
        //     //     if ($item->product->fixed_amount != null) {
        //     //         $data['totalFactor'] += str_replace(',', '', $item->product->fixed_amount) * $item->count;
        //     //     }
        //     //     $data['totalWeight'] += $item->details?->weight;
        //     // }

        //     // if (empty($data['totalFactor'])) {
        //     //     $data['complate'] = false;
        //     //     $data['getUserDeatail'] = false;
        //     // }

        //     return [

        //         'info' => [
        //             'fullname' => $data['address']['fullname'],
        //             'phone' => $data['address']['phone'],
        //             'address' => $data['address']['address'],
        //         ],

        //         'invoice' => [
        //             'code' => rand(10000, 99999),
        //             'createAt' => todays(),
        //         ],

        //         'totalCheckout' => [
        //             'amount' => $data['totalCheckout'],
        //             'unit' => 'تومان',
        //         ],

        //         'post' => [
        //             'amount' => (float) str_replace(',', '', $data['post']['amount']),
        //             'unit' => 'تومان',
        //             'title' => $data['post']['title'],
        //         ],

        //         'totalFactor' => [
        //             'amount' => $data['totalFactor'],
        //             'unit' => 'تومان',
        //         ],

        //         'products' => $data['products'],

        //         'payment' => [
        //             'amount' => $data['totalFactor'],
        //             'status' => 'pending',
        //             'statusTitle' => 'درحال بررسی',
        //             'paymentUrl' => 'shop.tidamode.ir/f/'.generateUniqueShortLink(5),
        //         ],

        //         // 'address' => $data['address'],
        //     ];

        //     $totalFactor = (float) str_replace(',', '', $data['totalCheckout']);
        //     $totalPost = (float) str_replace(',', '', $data['post']['amount']);

        //     $invoice = Invoice::create([
        //         'user_id' => $data['userId'],
        //         'total_factor' => $totalFactor,
        //         'total_payment' => $data['totalFactor'],
        //         'fullname' => $data['fullname'],
        //         'phone' => $data['phone'],
        //         'description' => 'ثبت سفارش از طریق فروشگاه',
        //         'post' => true,
        //         'money_post' => $totalPost,
        //         'gold18k' => getGold18k(),
        //     ]);

        //     foreach ($checkouts as $item) {

        //         if ($item->product->fixed_amount != null) {

        //             Product::whereId($item->product_id)->update([
        //                 'reserved_count' => (int) $item->count + (int) $item->product->reserved_count,
        //             ]);

        //             // CacheClear::clearKeys(['product_details_'.$item->product_id]);
        //             // Artisan::call('optimize:clear');
        //         } else {

        //             ProductDetail::where('product_id', $item->product_id)->whereId($item->weight_id)->update([
        //                 'reserved_count' => (int) $item->count + (int) $item->details->reserved_count,
        //             ]);

        //             // CacheClear::clearKeys(['product_details_'.$item->product_id]);
        //             // Artisan::call('optimize:clear');
        //         }

        //         $product = InvoiceProduct::create([
        //             'invoice_id' => $invoice->id,
        //             'product_id' => $item->product_id,
        //             'title_fa' => $item->product->title_fa,
        //             'title_eng' => $item->product->title_eng,
        //             'category_id' => $item->product->category_id,
        //             'for_id' => $item->product->for_id,
        //             'description' => $item->product->description,
        //             'slug' => $item->product->slug,
        //             'count' => $item->count,

        //         ]);

        //         $amount = 0;
        //         if ($item->product->fixed_amount != null) {
        //             $amount = str_replace(',', '', $item->product->fixed_amount) * $item->count;
        //         } else {
        //             $amount = $item->details?->amount ?? getMoney($item->details, null, false);
        //         }

        //         InvoiceProductDetail::create([
        //             'invoice_id' => $invoice->id,
        //             'product_id' => $product->id,
        //             'eticket' => $item->details?->eticket,
        //             'weight' => $item->details?->weight,
        //             'cutie' => $item->details?->cutie,
        //             'profit' => $item->details?->profit,
        //             'color_id' => $item->details?->color_id,
        //             'construction_wages' => $item->details?->construction_wages,
        //             'branch_id' => $item->details?->branch_id,
        //             'count' => $item->count,
        //             'amount' => str_replace(',', '', $amount),
        //             'chain_size' => $item->details?->chain_size,
        //             'pluck' => $item->details?->pluck,
        //             'model' => $item->details?->model,
        //             'chain_size' => str_replace(',', '', $item->details?->chain_size),
        //         ]);

        //     }

        //     Checkout::where('user_id', auth()->id())->delete();

        //     $url = $baseUrlProduction.'factor/'.hashId($invoice->id);

        //     $sortLink = generateUniqueShortLink(5);
        //     ShortLinkPayment::create([
        //         'user_id' => $userId,
        //         'order_id' => $invoice->id,
        //         'link' => $url,
        //         'short' => $sortLink,
        //         'phone' => $data['phone'],
        //         'fullname' => $data['fullname'],
        //         'status' => 1,
        //     ]);

        //     $link = $baseUrlProduction.'f/'.$sortLink;
        //     $data['shortLink'] = $link;
        //     $time = date('H:i', time());

        //     if (app()->environment('production')) {
        //         SendPayamakTransactionJob::dispatch($data['phone'], $data['fullname'], $link);

        //         SendPayamakTransactionAdminJob::dispatch(
        //             $userMobile,
        //             $userFullname,
        //             todays(),
        //             $time,
        //             $data['fullname'],
        //             $data['phone'],
        //             $link
        //         );
        //     }

        //     DB::commit();

        //     Artisan::call('optimize:clear');

        //     $HashInvoiceId = hashId($invoice->id);

        //     return [

        //         'info' => [
        //             'fullname' => $data['address']['fullname'],
        //             'phone' => $data['address']['phone'],
        //             'address' => $data['address']['address'],
        //         ],

        //         'invoice' => [
        //             'code' => rand(10000, 99999),
        //             'createAt' => todays(),
        //         ],

        //         'totalCheckout' => [
        //             'amount' => $data['totalCheckout'],
        //             'unit' => 'تومان',
        //         ],

        //         'post' => [
        //             'amount' => (float) str_replace(',', '', $data['post']['base_amount']),
        //             'unit' => 'تومان',
        //             'title' => $data['post']['title'],
        //         ],

        //         'totalFactor' => [
        //             'amount' => $data['totalFactor'],
        //             'unit' => 'تومان',
        //         ],

        //         'products' => $data['products'],

        //         'payment' => [
        //             'amount' => $data['totalFactor'],
        //             'status' => 'pending',
        //             'statusTitle' => 'درحال بررسی',
        //             'paymentUrl' => 'shop.tidamode.ir/f/'.generateUniqueShortLink(5),
        //         ],

        //         // 'address' => $data['address'],
        //     ];
        //     // return [
        //     //     'status' => true,
        //     //     'HashInvoiceId' => $HashInvoiceId,
        //     //     'complate' => true,
        //     //     'shotLinkUrl' => $link,
        //     // ];

        // } catch (\Exception $e) {

        //     DB::Rollback();

        //     return [
        //         'status' => false,
        //         'message' => $e->getMessage(),
        //     ];

        // }

    }
}
