<?php

namespace App\Service\Payamak;

class SmsService
{
    protected SmsProviderInterface $provider;

    public function __construct()
    {
        $provider = env('SMS_PROVIDER', 'ghasedak');

        $this->provider = match ($provider) {
            'ghasedak' => new GhasedakProvider,
            'meli' => new MeliPayamakProvider,
            default => throw new \Exception('SMS Provider is not valid'),
        };
    }

    public function send(string $type, array $data): array
    {
        return $this->provider->send($type, $data);
    }
}
