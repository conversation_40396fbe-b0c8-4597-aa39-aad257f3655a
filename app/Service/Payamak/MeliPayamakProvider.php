<?php

namespace App\Service\Payamak;

use App\Models\ExceptionLog;
use App\Models\PayamakServicelog;
use SoapClient;

class MeliPayamakProvider implements SmsProviderInterface
{
    protected $username;

    protected $password;

    public function __construct()
    {
        $this->username = env('MELI_PAYAMAK_USERNAME');
        $this->password = env('MELI_PAYAMAK_PASSWORD');
    }

    public function send(string $type, array $data): array
    {
        // dd($type, $data);

        return match ($type) {
            'tasvie' => $this->sendByPattern($data['phone'], [$data['fullname'], $data['code'], $data['status']], '345106'),
            'order_send' => $this->sendByPattern($data['phone'], [$data['code']], '347328'),
            'change_order_status' => $this->sendByPattern($data['phone'], [$data['fullname'], $data['code'], $data['status']], '347332'),
            'tracking_post' => $this->sendByPattern($data['phone'], [$data['code']], '347333'),
            'tracking_tipax' => $this->sendByPattern($data['phone'], [$data['code']], '347336'),
            'login' => $this->sendByPattern($data['phone'], [$data['code'], $data['fullname']], '347388'),
            default => ['error' => 'نوع پیامک نامعتبر است'],
        };
    }

    private function sendByPattern(string $to, array $params, string $bodyId): array
    {
        try {
            ini_set('soap.wsdl_cache_enabled', '0');
            $sms = new SoapClient('https://api.payamak-panel.com/post/send.asmx?wsdl', ['encoding' => 'UTF-8']);

            $data = [
                'username' => $this->username,
                'password' => $this->password,
                'text' => $params,
                'to' => faTOen($to),
                'bodyId' => $bodyId,
            ];

            $payamak = PayamakServicelog::create([
                'user_id' => auth()?->user()?->id ?? null,
                'service' => 'melipayak',
                'pattern' => $bodyId,
                'data' => json_encode($data),
            ]);

            $result = $sms->SendByBaseNumber($data)->SendByBaseNumberResult;

            $payamak->result = json_encode($result);
            $payamak->save();

            if ($result > 0) {
                return ['success' => 'پیامک با موفقیت ارسال شد'];
            } else {
                return ['error' => 'خطا در ارسال پیامک'];
            }

        } catch (\Throwable $e) {
            ExceptionLog::create([
                'file' => 'MeliPayamakProvider',
                'method' => 'sendByPattern',
                'line' => __LINE__,
                'description' => $e->getMessage(),
            ]);

            return ['error' => ['خطا در ارسال پیامک', $e->getMessage()]];
        }
    }
}
