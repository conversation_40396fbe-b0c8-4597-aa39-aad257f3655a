<?php

namespace App\Service\Payamak;

use App\Models\ExceptionLog;
use Ghasedak\Exceptions\ApiException;
use Ghasedak\Exceptions\HttpException;
use Ghasedak\GhasedakApi;

class GhasedakProvider implements SmsProviderInterface
{
    public function send(string $type, array $data): array
    {
        return match ($type) {
            'tasvie' => $this->sendVerify('TasviehMoshtari', $data, ['fullname', 'code', 'status']),
            'order_send' => $this->sendVerify('ErsalShodPayk', $data, ['code']),
            'change_order_status' => $this->sendVerify('StatusProduct', $data, ['fullname', 'code', 'status']),
            'tracking_post' => $this->sendVerify('Rahgiri', $data, ['code']),
            'tracking_tipax' => $this->sendVerify('Tippax', $data, ['code']),
            'login' => $this->sendVerify('ForgetPassword', $data, ['code', 'fullname']),
            default => ['error' => 'نوع پیامک نامعتبر است'],
        };
    }

    private function sendVerify(string $template, array $data, array $requiredKeys): array
    {

        try {
            $receptor = faTOen($data['phone']);
            $params = [];

            foreach ($requiredKeys as $key) {
                $params[] = $data[$key] ?? '';
            }

            $api = new GhasedakApi(env('GHASEDAK_API'));
            $api->Verify($receptor, $template, ...$params);

            return ['success' => 'پیامک با موفقیت ارسال شد'];

        } catch (ApiException | HttpException $e) {

            ExceptionLog::create([
                'file' => 'GhasedakProvider',
                'method' => 'sendVerify',
                'line' => __LINE__,
                'description' => $e->errorMessage(),
            ]);

            throw new \Exception("خطا در ارسال پیامک: " . $e->errorMessage());
        }
    }
}
