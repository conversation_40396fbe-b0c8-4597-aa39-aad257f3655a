<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendPayamakTransactionAdminJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public $phone,
        public $fullname,
        public $date,
        public $time,
        public $fullname_subscribe,
        public $phone_subscribe,
        public $link
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $api = new \Ghasedak\GhasedakApi(env('GHASEDAK_API'));
        $api->Verify(
            $this->phone,
            'urluser',
            $this->fullname,
            $this->date,
            $this->time,
            $this->fullname_subscribe,
            $this->phone_subscribe,
            $this->link);
    }
}
