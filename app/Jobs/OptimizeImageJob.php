<?php

namespace App\Jobs;

use App\Models\ProductGallery;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;
use Spatie\ImageOptimizer\OptimizerChainFactory;

class OptimizeImageJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $galleryId;

    public function __construct(int $galleryId)
    {
        $this->galleryId = $galleryId;
    }

    public function handle(): void
    {
        $gallery = ProductGallery::find($this->galleryId);

        if (! $gallery) {
            \Log::warning('Gallery not found: '.$this->galleryId);

            return;
        }

        $originalPath = 'https://tidamode.iran.liara.run/'.$gallery->url;
        $tempPath = storage_path('app/temp_'.Str::random(8).'.jpg');
        file_put_contents($tempPath, file_get_contents($originalPath));

        $image = Image::make($tempPath)
            ->resize(300, null, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });

        $newFilename = 'optimized_'.Str::random(10).'.jpg';
        $newPath = storage_path("app/public/optimized/{$newFilename}");
        $image->save($newPath, 80);

        $optimizerChain = OptimizerChainFactory::create();
        $optimizerChain->optimize($newPath);

        $gallery->optimized_url = 'optimized/'.$newFilename;
        $gallery->save();

        // حذف فایل موقت
        unlink($tempPath);
    }
}
