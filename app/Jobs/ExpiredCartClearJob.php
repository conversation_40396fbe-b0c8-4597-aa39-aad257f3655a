<?php

namespace App\Jobs;

use App\Models\Checkout;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Artisan;

class ExpiredCartClearJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $timeLimit = Carbon::now()->subMinutes(30);
        Checkout::where('created_at', '<', $timeLimit)->delete();
        Artisan::call('optimize:clear');

    }
}
