<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Http;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

class SitemapJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // ارسال درخواست به API و دریافت داده‌ها
        $response = Http::get('https://tidamode.iran.liara.run/api/v1/products');

        // بررسی وضعیت پاسخ
        if ($response->successful()) {
            // فرض کنید که داده‌های API لیستی از محصولات باشد
            $products = $response->json();

            // ساخت یک شیء Sitemap
            $sitemap = Sitemap::create();

            // اضافه کردن محصولات به سایت مپ
            foreach ($products['data']['products'] as $product) {
                // فرض کنید که تاریخ آخرین تغییر را از API دریافت می‌کنید (در اینجا فرضی است)
                // اگر تاریخ تغییرات محصولات در API موجود باشد، آن را استفاده کنید.
                $lastModified = Carbon::now(); // اینجا فرض می‌کنیم که تاریخ فعلی به عنوان تاریخ آخرین تغییر قرار داده می‌شود.

                // اضافه کردن هر محصول به سایت مپ همراه با تاریخ آخرین تغییر
                $sitemap->add(
                    Url::create("https://shop.tidamode.ir/product/{$product['slug']}")
                        ->setLastModificationDate($lastModified) // افزودن تاریخ آخرین تغییر
                );
            }

            // ذخیره سایت مپ در فایل
            $sitemap->writeToFile(storage_path('app/public/sitemap.xml'));

            // ارسال فایل به کاربر
            // $filePath = storage_path('app/public/sitemap.xml');

            // return response()->file($filePath);
        }

        // در صورت عدم موفقیت در دریافت داده‌ها از API
        // return response()->json(['error' => 'Failed to fetch products from API'], 500);
    }
}
