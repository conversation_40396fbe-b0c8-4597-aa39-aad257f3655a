<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendPaymentR<PERSON>inder<PERSON>ob implements ShouldQueue
{
    use Queueable;

    public $data;

    /**
     * Create a new job instance.
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        $api = new \Ghasedak\GhasedakApi(env('GHASEDAK_API'));
        $api->Verify($this->data['phone'], 'swing', $this->data['link']);
        // $api->Verify($this->data['phone'], 'PaymentReminderJob', $this->data['fullname'], $this->data['orderId'], $this->data['link']);
    }
}
