<?php

namespace App\Jobs;

use App\Models\FactorOrderEmpty;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class UpdateFactorNumberJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $chunkSize = 1000;
        $factorId = 1;

        FactorOrderEmpty::chunkById($chunkSize, function ($factors) use (&$factorId) {
            foreach ($factors as $item) {
                $item->update([
                    'factor_number' => $factorId,
                ]);
                $factorId++;
            }
        });
    }
}
