<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendPayamakTransactionJob implements ShouldQueue
{
    use Queueable;

    public $phone, $fullname, $link;
    /**
     * Create a new job instance.
     */
    public function __construct($phone, $fullname, $link)
    {
        $this->phone = $phone;
        $this->fullname = $fullname;
        $this->link = $link;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $api = new \Ghasedak\GhasedakApi(env('GHASEDAK_API'));
        //$api->Verify(auth()->user()->mobile,'urluser', auth()->user()->fullname, $this->fullname, $this->link);
        $api->Verify($this->phone,'urlsubscriber', $this->fullname, $this->link);
    }
}
