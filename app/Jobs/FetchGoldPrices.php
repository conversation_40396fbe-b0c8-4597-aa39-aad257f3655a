<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class FetchGoldPrices implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $response = Http::get('https://www.tala.ir/ajax/price/talair');

        if ($response->successful()) {
            $data = $response->json();

            // ذخیره در کش برای ۲۰ دقیقه
            Cache::put('gold_prices', $data, now()->addMinutes(20));
        }
    }
}
