<?php

namespace App\Jobs;

use App\Service\Payamak\SmsService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class AuthSMSJob implements ShouldQueue
{
    use Queueable;

    public $phone;

    public $code;

    public $username;

    /**
     * Create a new job instance.
     */
    public function __construct($phone, $code, $username)
    {
        $this->phone = $phone;
        $this->code = $code;
        $this->username = $username;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $smsService = new SmsService;
        $smsService->send('login', [
            'phone' => $this->phone,
            'fullname' => $this->username,
            'code' => $this->code,
        ]);
        // $api = new \Ghasedak\GhasedakApi(env('GHASEDAK_API'));
        // $api->Verify($this->phone,'ForgetPassword', $this->username, $this->code);
    }
}
