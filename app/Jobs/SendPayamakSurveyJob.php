<?php

namespace App\Jobs;

use App\Models\ExceptionLog;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendPayamakSurveyJob implements ShouldQueue
{
    use Queueable;

    public $data;

    /**
     * Create a new job instance.
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $api = new \Ghasedak\GhasedakApi(config('services.ghasedak.api_key'));
        $result = $api->Verify($this->data['phone'], 'survey', $this->data['fullname'], $this->data['link']);

        // ExceptionLog::create([
        //     'file' => 'SendPayamakSurveyJob',
        //     'method' => 'handle',
        //     'line' => __LINE__,
        //     'description' => $result,
        // ]);
    }
}
