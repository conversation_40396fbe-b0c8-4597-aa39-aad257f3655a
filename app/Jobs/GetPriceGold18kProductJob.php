<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class GetPriceGold18kProductJob implements ShouldQueue
{
    use Queueable;

    public $productId;

    /**
     * Create a new job instance.
     */
    public function __construct() {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // $amount = getMoney($this->productId);
    }
}
