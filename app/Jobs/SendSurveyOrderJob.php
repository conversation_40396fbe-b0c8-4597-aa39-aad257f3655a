<?php

namespace App\Jobs;

use App\Models\ExceptionLog;
use App\Models\Sefaresh;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendSurveyOrderJob implements ShouldQueue
{
    use Queueable;

    public $baseUrlProduction;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->baseUrlProduction = env('APP_TIDAMODE_SHOP_URL', 'https://shop.tidamode.ir/');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $chunkSize = 1000;

        // تعیین بازه زمانی از سه روز پیش تا امروز
        $from = Carbon::now()->subDays(10)->startOfDay();
        $to = Carbon::now()->endOfDay();

        Sefaresh::whereBetween('created_at', [$from, $to])
            ->whereNull('survey')
            ->whereNull('survey_sms')
            ->where('last_status', 'send')->chunkById($chunkSize, function ($products) {
                foreach ($products as $item) {

                    $item->survey_sms = 'send';
                    $item->save();

                    $link = $this->baseUrlProduction . 'survey/' . hashId($item->id);

                    $data = [
                        'fullname' => $item->fullname,
                        'phone' => $item->phone,
                        'orderId' => hashId($item->id),
                        'link' => $link,
                    ];

                    // ExceptionLog::create([
                    //     'file' => 'SendSurveyOrderJob',
                    //     'method' => 'handle',
                    //     'line' => __LINE__,
                    //     'description' => $data,
                    // ]);

                    SendPayamakSurveyJob::dispatch($data);
                }
            });

    }
}
