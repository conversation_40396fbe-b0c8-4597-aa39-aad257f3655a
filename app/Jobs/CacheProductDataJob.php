<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class CacheProductDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * اجرای Job
     */
    public function handle()
    {
        $filterCombinations = [
            ['sortBy' => 'newest_available', 'minPrice' => 0, 'maxPrice' => 999999999],
            ['sortBy' => 'most_expensive', 'minPrice' => 0, 'maxPrice' => 999999999],
            ['sortBy' => 'cheapest', 'minPrice' => 0, 'maxPrice' => 999999999],
        ];

        foreach ($filterCombinations as $params) {
            $cacheParams = [
                'page' => 1,
                'sortBy' => $params['sortBy'],
                'minPrice' => $params['minPrice'],
                'maxPrice' => $params['maxPrice'],
                'selectedCategories' => [],
                'title_fa' => '',
                'eticket' => '',
                'inweight' => 0,
                'outweight' => null,
                'on_page' => 10,
            ];

            $cacheKey = 'products_available_page_'.md5(json_encode($cacheParams));
            Cache::put($cacheKey, $this->getAvailableProducts($cacheParams), now()->addHours(1));
        }

        Cache::put('products_not_available_page_1', $this->getNonexistentProducts(), now()->addMinutes(10));
    }

    /**
     * دریافت محصولات موجود
     */
    private function getAvailableProducts($cacheParams)
    {
        return DB::table('product_details_view')
            ->whereRaw("CAST(REPLACE(amount, ',', '') AS UNSIGNED) BETWEEN ? AND ?", [$cacheParams['minPrice'], $cacheParams['maxPrice']])
            ->when(! empty($cacheParams['selectedCategories']), function ($query) use ($cacheParams) {
                return $query->whereIn('category_id', $cacheParams['selectedCategories']);
            })
            ->paginate($cacheParams['on_page']);
    }

    /**
     * دریافت محصولات ناموجود
     */
    private function getNonexistentProducts()
    {
        return DB::table('product_details_view')
            ->where('available_stock', 0)
            ->paginate(10);
    }
}
