<?php

namespace App\Livewire\Auth;

use <PERSON><PERSON><PERSON><PERSON>\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use <PERSON><PERSON><PERSON><PERSON>\LivewireRateLimiting\WithRateLimiting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Validate;
use Livewire\Component;

class Verfication extends Component
{
    use LivewireAlert;
    use WithRateLimiting;

    public $exError;

    public $phone;

    #[Validate('required')]
    public $token;

    public function verfication(Request $req)
    {
        try {

            $this->exError = '';
            $this->rateLimit(5);
            $this->validate();

            $token = \App\Models\Token::wherePhone($this->phone)->whereToken($this->token)->whereStatus(0)->latest()->first();
            if (isset($token) && $token->count() > 0) {
                $currentTime = now();
                $tokenTime = $token->created_at;

                \App\Models\Token::wherePhone($this->phone)->update([
                    'status' => 2,
                ]);

                if ($currentTime->diffInMinutes($tokenTime) > 5) {

                    $this->token = '';

                    return $this->exError = 'کد پیامکی اشتب5ه است یا منقضی شده، مدت اعتبار کدپیامکی 3 دقیقه می باشد';
                }

                setUserLog($req, $this->phone, 'verfication');
                $user = \App\Models\User::whereMobile($this->phone)->first();
                Auth::login($user, true);
                $this->alert('success', 'با موفقیت وارد سامانه شدید', [
                    'position' => 'top-start',
                ]);

                return $this->redirect('/');
            } else {
                $this->token = '';

                return $this->exError = 'کد پیامکی اشتباه است';
            }

        } catch (TooManyRequestsException $exception) {
            $this->exError = 'تلاش های زیادی برای ورود به سیستم انجام شده. لطفا چند دقیقه دیگر دوباره اقدام به ورود کنید';
            throw ValidationException::withMessages([
                'message' => "تلاش های زیادی برای ورود انجام شده، لطفاً  {$exception->secondsUntilAvailable} ثانیه صبر کنید و سپس دوباره اقدام به ورود کنید",
            ]);
        }
    }

    public function render()
    {
        return view('livewire.auth.verfication');
    }
}
