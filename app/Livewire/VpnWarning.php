<?php

namespace App\Livewire;

use Illuminate\Support\Facades\Http;
use Livewire\Component;

class VpnWarning extends Component
{
    public $showWarning = false;

    public $ip = '';

    public $country = '';

    public function mount()
    {
        $ips = request()->ips();

        $this->ip = $ips[1] ?? request()->ip();

        $response = Http::get("http://ip-api.com/json/{$this->ip}");

        if ($response->successful()) {
            $data = $response->json();
            $this->country = $data['countryCode'] ?? '';

            if ($this->country !== 'IR') {
                $this->showWarning = true;
            }
        }
    }

    public function render()
    {
        return view('livewire.vpn-warning');
    }
}
