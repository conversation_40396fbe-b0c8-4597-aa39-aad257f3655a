<?php

namespace App\Livewire\Dashboard\Factor;

use App\Models\FactorOrderEmpty;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    #[Url]
    public $phone;

    public array $data = [
        'fullname' => null,
        'phone' => null,
        'fullname_r' => null,
        'phone_r' => null,
        'category' => null,
        'code_order' => null,
        'factorId' => null,
        'last_status' => null,
        'eticket' => null,
        'productName' => null,
        'date_start' => null,
        'date_end' => null,
    ];

    public function fillter() {}

    public function ClearFillter()
    {
        $this->data = [
            'fullname' => null,
            'phone' => null,
            'fullname_r' => null,
            'phone_r' => null,
            'category' => null,
            'code_order' => null,
            'factorId' => null,
            'last_status' => null,
            'eticket' => null,
            'productName' => null,
            'date_start' => null,
            'date_end' => null,
        ];
        $this->phone = null;
    }

    public function render()
    {

        $query = FactorOrderEmpty::with('order', 'user', 'factorItem');

        if (auth()->user()->level != 'admin') {
            $query->where('user_id', auth()->user()->id);
        }

        if (! is_null($this->phone)) {
            $this->data['phone'] = $this->phone;
            // $query->where('phone', $this->phone);
        }

        if ($this->data['factorId'] != null) {
            $query->where('factor_number', 'like', '%'.$this->data['factorId'].'%');
        }

        if ($this->data['fullname'] != null) {
            $query->where('fullname', 'like', '%'.$this->data['fullname'].'%');
        }

        if ($this->data['fullname_r'] != null) {
            $query->where('fullname_r', 'like', '%'.$this->data['fullname_r'].'%');
        }

        if ($this->data['phone'] != null) {
            $query->where('phone', 'like', '%'.$this->data['phone'].'%');
        }

        if ($this->data['phone_r'] != null) {
            $query->where('phone_r', 'like', '%'.$this->data['phone_r'].'%');
        }

        if ($this->data['last_status'] != null) {
            $query->whereHas('order', function ($query) {
                $query->where('last_status', $this->data['last_status']);
            });
        }

        if ($this->data['eticket'] != null) {
            $eticket = $this->data['eticket'];

            $query->where(function ($q) use ($eticket) {
                $q->whereHas('factorItem', function ($q) use ($eticket) {
                    $q->where('eticket', $eticket);
                })->orWhereHas('order.invoice.productDetails', function ($q) use ($eticket) {
                    $q->where('eticket', $eticket);
                });
            });
        }

        if ($this->data['productName'] != null) {
            $query->whereHas('order.invoice.products', function ($query) {
                $query->where('title_fa', 'like', '%'.$this->data['productName'].'%');
            });
        }

        if (! is_null($this->data['date_start']) && ! is_null($this->data['date_end'])) {
            $query->whereBetween('factor_create_date', [$this->data['date_start'], $this->data['date_end']]);
        }

        if (! is_null($this->data['date_start']) && is_null($this->data['date_end'])) {
            $query->where('factor_create_date', '>=', $this->data['date_start']);
        }

        if (is_null($this->data['date_start']) && ! is_null($this->data['date_end'])) {
            $query->where('factor_create_date', '<=', $this->data['date_end']);
        }

        $factors = $query->latest()->paginate(20);

        return view('livewire.dashboard.factor.index', [
            'factors' => $factors,
        ]);

    }
}
