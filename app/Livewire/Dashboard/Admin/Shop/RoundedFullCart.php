<?php

namespace App\Livewire\Dashboard\Admin\Shop;

use Livewire\Attributes\On;
use Livewire\Component;

class RoundedFullCart extends Component
{
    public int $cartCount = 0;

    #[On('show-factor')]
    public function loadData()
    {
        $this->cartCount = cache()->remember(
            'checkout_cart_count_'.auth()->user()->id,
            now()->addMinutes(10),
            function () {
                return \App\Models\Checkout::where('user_id', auth()->user()->id)->count();
            },
        );
    }

    public function render()
    {
        $this->loadData();

        return view('livewire.dashboard.admin.shop.rounded-full-cart');
    }
}
