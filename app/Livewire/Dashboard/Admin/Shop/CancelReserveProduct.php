<?php

namespace App\Livewire\Dashboard\Admin\Shop;

use App\Models\Invoice;
use App\Models\InvoiceProductDetail;
use App\Models\Product;
use App\Models\ProductDetail;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class CancelReserveProduct extends Component
{
    use LivewireAlert;

    public $productId;

    public $productDetailId;

    public function cancel()
    {
        // dd($this->productDetailId, $this->productId);
        $product = InvoiceProductDetail::where('product_detail_id', $this->productDetailId)->first();
        if ($product) {
            $invoice = Invoice::where('id', $product->invoice_id)->first();
            if (isset($invoice) && $invoice != null && $invoice->status != 'reject') {
                Invoice::where('id', $product->invoice_id)->update([
                    'status' => 'reject',
                ]);

                $invoice = Invoice::whereId($product->invoice_id)->first();
                $invoiceDetails = InvoiceProductDetail::where('invoice_id', $product->invoice_id)->get();

                foreach ($invoiceDetails as $item) {

                    if ($item?->product?->product?->fixed_amount != null) {

                        Product::whereId($item->product->product->id)->update([
                            'reserved_count' => $item?->product?->product?->reserved_count - $item->count,
                            'out_stock' => $item?->product?->product?->out_stock - $item->count,
                        ]);

                    } else {

                        ProductDetail::where('product_id', $item->product->product->id)->where('weight', $item->weight)->update([
                            'reserved_count' => null,
                            'out_stock' => null,
                        ]);
                    }

                }

                $this->alert('success', 'کنسل سفارش انجام شد', [
                    'position' => 'top-start',
                ]);

                return;
            }

            $this->alert('error', 'این سفارش قبلا کنسل شده است', [
                'position' => 'top-start',
            ]);

            return;
        }

        $this->alert('error', 'خطا در ثبت', [
            'position' => 'center',
            'timer' => 30000,
            'toast' => false,
            'text' => 'متاسفانه در کنسل کردن سفارش مرتبط با این محصول سیستم به خطا خورده است',
            'timerProgressBar' => true,
            'showDenyButton' => true,
            'onDenied' => '',
            'denyButtonText' => 'بسیار خب متوجه شدم',
        ]);

    }

    public function render()
    {
        return view('livewire.dashboard.admin.shop.cancel-reserve-product');
    }
}
