<?php

namespace App\Livewire\Dashboard\Admin\Shop;

use Illuminate\Support\Facades\DB;
use Livewire\Component;

class SearchProduct extends Component
{
    public $title;

    public function render()
    {
        $products = [];

        if (!empty($this->title)) {

            $settings = \App\Models\Setting::where('type', 'profit_offer')
                ->pluck('body', 'type')
                ->toArray();
            $originalProfit = (float) ($settings['profit_offer'] ?? 0);

            $products = DB::table('product_details_view')
                ->select([
                    DB::raw('MIN(product_detail_id) as product_detail_id'),
                    'product_id',
                    'product_slug',
                    'amount',
                    'amount_after_offer',
                    'profit',
                    DB::raw('MIN(amount_after_offer) as min_amount_after_offer'),
                    DB::raw('MAX(status) as status'),
                    DB::raw('MAX(updated_at) as updated_at'),
                    DB::raw('MIN(amount) as min_amount'),
                    DB::raw('MAX(amount) as max_amount'),
                    DB::raw('MAX(product_image_url) as product_image_url'),
                    DB::raw('MAX(category_title_fa) as category_title_fa'),
                    DB::raw('MAX(chain_size) as chain_size'),
                    DB::raw('MAX(product_title_fa) as product_title_fa'),
                    DB::raw('COUNT(available_stock) as available_stock'),
                    DB::raw('COUNT(p_out_stock) as p_out_stock'),
                    DB::raw('COUNT(pd_out_stock) as pd_out_stock'),
                    DB::raw('MAX(category_id) as category_id'),
                    // DB::raw('MAX(eticket) as eticket'),
                    DB::raw('MAX(weight) as weight'),
                    DB::raw('MAX(branch_id) as branch_id'),
                    DB::raw('MAX(fixed_amount) as fixed_amount'),
                    DB::raw('MAX(construction_wages) as construction_wages'),
                    DB::raw('MAX(created_at) as created_at'),
                    DB::raw('GROUP_CONCAT(DISTINCT weight ORDER BY weight ASC SEPARATOR ",") as weights'),
                    DB::raw("CASE
                    WHEN profit > 0 AND profit > {$originalProfit}
                        THEN ((profit - {$originalProfit}) / profit) * 100
                    ELSE 0
                END as discount_percent"),

                ])
                ->where('status', 'active')
                ->where('tax', '!=', '0')
                ->groupBy('product_id', 'amount_after_offer', 'amount', 'profit')
                ->when(!empty($this->title), function ($query) {
                    $words = explode(' ', trim($this->title));
                    foreach ($words as $word) {
                        $query->where('product_title_fa', 'LIKE', '%' . $word . '%');
                    }
                })
                ->limit(20)
                ->get();
        }

        return view('livewire.dashboard.admin.shop.search-product', [
            'products' => $products,
        ]);
    }

}
