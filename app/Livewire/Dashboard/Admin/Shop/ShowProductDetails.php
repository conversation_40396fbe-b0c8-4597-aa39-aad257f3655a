<?php

namespace App\Livewire\Dashboard\Admin\Shop;

use App\Models\ProductDetail;
use App\Models\Setting;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;

class ShowProductDetails extends Component
{
    use LivewireAlert;

    public $detail;

    public $image_server_url;

    #[On('show-product-details')]
    public function showProduct($productId)
    {
        $this->image_server_url = env('APP_ASSET_URL');
        // $this->detail = ProductDetail::with('product')->whereId($productId)->first();
        $this->detail = DB::table('product_details_view')->where('product_detail_id', $productId)->first();

    }

    public function render()
    {
        return view('livewire.dashboard.admin.shop.show-product-details', [
            'setting' => Setting::whereIn('type', [
                'card_number',
                'fullname_card_number',
                'sheba_card_number'
            ])->pluck('body', 'type')->toArray()
        ]);
    }
}
