<?php

namespace App\Livewire\Dashboard\Admin\Shop;

use App\Models\ProductDetail;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\On;
use Livewire\Component;

class ShowProductsList extends Component
{
    public $productId;

    public string $image_server_url;

    #[On('show-products')]
    public function showProducts($productId)
    {

        $this->productId = $productId;
    }

    public function render()
    {
        $this->image_server_url = env('APP_ASSET_URL');

        $cacheKey = "product_details_{$this->productId}";
        $products = DB::table('product_details_view')
            ->where('product_id', $this->productId)
            ->orderByRaw('(available_stock - pd_out_stock > 0) ASC')
            ->get();

        // $products = cache()->remember($cacheKey, now()->addWeek(), function () {
        // $products = ProductDetail::select(['id', 'product_id', 'weight', 'eticket', 'stone_price', 'chain_size', 'reserved_count', 'amount'])
        //     ->with([
        //         'product:id,title_fa,category_id',
        //         'product.category:id,title_fa',
        //         'gallery:id,product_id,url',
        //         'color:id,description',
        //         'branch:id,description',
        //     ])
        //     ->where('product_id', $this->productId)
        //     ->orderBy('weight', 'desc')
        //     ->get();
        // });

        return view('livewire.dashboard.admin.shop.show-products-list', [
            'products' => $products,
        ]);
    }
}
