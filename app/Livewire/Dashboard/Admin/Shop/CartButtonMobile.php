<?php

namespace App\Livewire\Dashboard\Admin\Shop;

use App\Models\Checkout;
use Livewire\Attributes\On;
use Livewire\Component;

class CartButtonMobile extends Component
{
    public $count = 0;

    public function mount()
    {
        $this->loadCount();
    }

    #[On('show-factor')]
    #[On('cart-button')]
    public function getCartCount()
    {
        $this->loadCount();
    }

    private function loadCount()
    {
        $this->count = cache()->remember('checkout_cart_count_'.auth()->user()->id, now()->addMinutes(10), function () {
            return Checkout::where('user_id', auth()->user()->id)->count();
        });

    }

    public function render()
    {
        return view('livewire.dashboard.admin.shop.cart-button-mobile');
    }
}
