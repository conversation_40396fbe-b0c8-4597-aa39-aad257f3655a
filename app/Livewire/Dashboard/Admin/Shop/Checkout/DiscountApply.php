<?php

namespace App\Livewire\Dashboard\Admin\Shop\Checkout;

use App\Models\Discount;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;

class DiscountApply extends Component
{
    public $code;

    public $percentage;

    use LivewireAlert;

    public function mount()
    {

        $this->code = null;
    }

    #[On('show-factor')]
    public function clearCode()
    {

        $this->code = null;
    }

    public function store()
    {
        if (! empty($this->percentage)) {
            $this->percentage = null;
            $this->dispatch('set-discount-percentage', 0);
            $this->code = null;

            return;
        }

        if ($this->code == null) {
            $this->alert('error', 'خطا در اعمال کد تخفیف', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'کد تخفیف را وارد کنید',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

            return;
        }

        $now = verta();
        $discount = Discount::where('code', $this->code)
            // ->where('start_date', '<=', $now)
            // ->where('expiry_date', '>=', $now)
            // ->where('status', 'active')
            // ->where('total_uses', '=>', 1)
            ->first();

        if ($discount) {
            $this->percentage = $discount->percentage;
            $this->dispatch('set-discount-percentage', $discount->percentage);
            // $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
            //     'position' => 'top-start',
            // ]);

        } else {
            $this->alert('error', 'خطا در اعمال کد تخفیف', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'کد تخفیف معتبر نمی باشد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }
    }

    public function render()
    {
        return view('livewire.dashboard.admin.shop.checkout.discount-apply');
    }
}
