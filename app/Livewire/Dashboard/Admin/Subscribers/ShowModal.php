<?php

namespace App\Livewire\Dashboard\Admin\Subscribers;

use Livewire\Component;
use Livewire\Attributes\On; 
use Illuminate\Support\Facades\DB;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
use App\Models\Subscribe;

class ShowModal extends Component
{
    use LivewireAlert;

    public $userId;
    public $code, $fullname, $phone, $zipcode, $address;

    #[On('set-subscriber')]
    public function getSubscriber($userId){
        $this->userId = $userId;
        $subscriber = Subscribe::whereId($userId)->firstorfail();
        $this->code = $subscriber->code;
        $this->fullname = $subscriber->fullname;
        $this->phone = $subscriber->mobile;
        $this->zipcode = $subscriber->codepost;
        $this->address = $subscriber->address;
    }

    public function rules() 
    {
        return [ 
            'fullname' => 'required',
            'phone' => 'required',
            'zipcode' => 'required',
            'address' => 'required'
        ];
    }
 
    public function messages() 
    {
        return [
            'fullname.required' => 'پر کردن فیلد الزامیست',
            'phone.required' => 'پر کردن فیلد الزامیست',
            'zipcode.required' => 'پر کردن فیلد الزامیست',
            'address.required' => 'پر کردن فیلد الزامیست',
        ];
    }

    public function save(){

        $this->validate();

        try{

            DB::beginTransaction();
            
            Subscribe::whereId($this->userId)->update([
                'fullname' => $this->fullname,
                'mobile' => $this->phone,
                'codepost' => $this->zipcode,
                'address' => $this->address,
            ]);
            

            DB::commit();

           $this->alert('success', 'بروزرسانی اطلاعات موفقیت آمیز بود', [
                'position' => 'top-start',
            ]);
            
            $this->dispatch('subscribers-reload');

        }catch(\Exception $e){

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'سیستم با خطا مواجه شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }

    }

    public function render()
    {
        return view('livewire.dashboard.admin.subscribers.show-modal');
    }
}
