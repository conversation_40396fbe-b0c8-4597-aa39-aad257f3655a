<?php

namespace App\Livewire\Dashboard\Admin\Subscribers;

use App\Models\Subscribe;
use Illuminate\Support\Facades\DB;
use Jan<PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class CreateModal extends Component
{
    use LivewireAlert;

    public $userId;

    public $code;

    public $fullname;

    public $phone;

    public $zipcode;

    public $address;

    public function rules()
    {
        return [
            'fullname' => 'required',
            'phone' => 'required',
            'zipcode' => 'required',
            'address' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'fullname.required' => 'پر کردن فیلد الزامیست',
            'phone.required' => 'پر کردن فیلد الزامیست',
            'zipcode.required' => 'پر کردن فیلد الزامیست',
            'address.required' => 'پر کردن فیلد الزامیست',
        ];
    }

    public function save()
    {

        $this->validate();

        try {

            DB::beginTransaction();

            $subscribe = Subscribe::latest()->first();

            Subscribe::create([
                'code' => $subscribe->code + 1,
                'fullname' => $this->fullname,
                'mobile' => $this->phone,
                'codepost' => $this->zipcode,
                'address' => $this->address,
            ]);

            DB::commit();

            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

            return redirect()->route('admin-dashboard-subscribers');

        } catch (\Exception $e) {

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'سیستم با خطا مواجه شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }

    }

    public function render()
    {
        return view('livewire.dashboard.admin.subscribers.create-modal');
    }
}
