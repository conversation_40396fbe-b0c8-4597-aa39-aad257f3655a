<?php

namespace App\Livewire\Dashboard\Admin\Subscribers;

use App\Models\Subscribe;
use Illuminate\Pagination\Paginator;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;

class FindUserModal extends Component
{
    use WithPagination;

    public $search;

    public $sortBy = 'DESC';

    #[On('reload-subscribers')]
    public function updatedSearch()
    {
        // $this->resetPage();
    }

    public function setUser($userId)
    {
        $this->dispatch('set-user', userId : $userId);
    }

    public function sortByOrder($sortBy)
    {
        $this->sortBy = $sortBy;
        // $this->resetPage();
    }

    public function render()
    {

        $cacheKey = 'subscribers_'.md5($this->search.'_'.$this->sortBy.'_'.request()->get('page', Paginator::resolveCurrentPage()));

        $subscribers = Subscribe::with('order')->
            where('fullname', 'like', '%'.$this->search.'%')->
            orwhere('mobile', 'like', '%'.$this->search.'%')
                ->latest()
                ->paginate(6);

        return view('livewire.dashboard.admin.subscribers.find-user-modal', [
            'subscribers' => $subscribers,
        ]);

    }
}
