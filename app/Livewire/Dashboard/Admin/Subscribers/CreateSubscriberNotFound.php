<?php

namespace App\Livewire\Dashboard\Admin\Subscribers;

use Livewire\Component;
use Livewire\Attributes\On;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
use App\Models\Subscribe;
use Illuminate\Support\Facades\DB;
class CreateSubscriberNotFound extends Component
{
    use LivewireAlert;

    public $userId;
    public $code, $fullname, $phone, $zipcode, $address;



    public function rules()
    {
        return [
            'fullname' => 'required',
            'phone' => 'required',
            'zipcode' => 'required',
            'address' => 'required'
        ];
    }

    public function messages()
    {
        return [
            'fullname.required' => 'پر کردن فیلد الزامیست',
            'phone.required' => 'پر کردن فیلد الزامیست',
            'zipcode.required' => 'پر کردن فیلد الزامیست',
            'address.required' => 'پر کردن فیلد الزامیست',
        ];
    }

    public function save(){

        $this->validate();

        try{

            DB::beginTransaction();

            $subscribe_exist = Subscribe::where('mobile', $this->phone)->latest()->first();
            if($subscribe_exist){
                $this->alert('error', 'شماره موبایل تکراری می باشد', [
                    'position' => 'top-start',
                ]);
                $this->dispatch('set-user', userId :  $subscribe_exist->id);
                return;
            }

            $subscribe = Subscribe::latest()->first();
            $subscriber = Subscribe::create([
                'code' => $subscribe?->code + 1,
                'fullname' => $this->fullname,
                'mobile' => $this->phone,
                'codepost' => $this->zipcode,
                'address' => $this->address
            ]);


            DB::commit();

           $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

            $this->dispatch('set-user', userId :  $subscriber->id);
            $this->dispatch('close-subscribe-modal');
            $this->dispatch('reload-subscribers');

            $this->reset(['fullname', 'phone', 'zipcode', 'address']);


        }catch(\Exception $e){

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 30000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }

    }
    public function render()
    {
        return view('livewire.dashboard.admin.subscribers.create-subscriber-not-found');
    }
}
