<?php

namespace App\Livewire\Dashboard\Admin\Subscribers;

use Livewire\Component;

class Fillter extends Component
{

    public $code, $fullname, $phone, $address;

    public function fillter(){
        $data = [
            'code' => $this->code,
            'fullname' => $this->fullname,
            'phone' => $this->phone,
            'address' => $this->address,
        ];

        $this->dispatch('subscribe-filter', $data);
    }

    public function ClearFillter(){

        
        $this->code = null;
        $this->fullname = null;
        $this->phone = null;
        $this->address = null;

        $data = [
            'code' => $this->code,
            'fullname' => $this->fullname,
            'phone' => $this->phone,
            'address' => $this->address,
        ];
        
        $this->dispatch('subscribe-filter', $data);
    }

    public function render()
    {
        return view('livewire.dashboard.admin.subscribers.fillter');
    }
}
