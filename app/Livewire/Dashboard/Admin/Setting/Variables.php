<?php

namespace App\Livewire\Dashboard\Admin\Setting;
use App\Models\Setting;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Lazy;
use Livewire\Component;
class Variables extends Component
{
    use LivewireAlert;

    public array $data = [
        'card_number' => '',
        'fullname_card_number' => '',
        'sheba_card_number',
        'shop_name',
        'shop_phone',
        'shop_address',
        'shop_zipcode',
        'is_shop_open' => false,
    ];

    public function loadGold()
    {


        DB::beginTransaction();
        try {

            $types = [
                'card_number',
                'fullname_card_number',
                'sheba_card_number',
                'shop_name',
                'shop_phone',
                'shop_address',
                'shop_zipcode',
                'is_shop_open',
            ];

            foreach ($types as $item) {
                Setting::updateOrCreate(['type' => $item], ['body' => $this->data[$item]]);
            }

            DB::commit();
            $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
                'position' => 'top-start',
            ]);

            // return redirect('/setting');
        } catch (\Exception $exceptionxception) {

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'سیستم با خطا مواجه شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }
    }

    public function mount()
    {
        $settings = Setting::whereIn('type', [
            'card_number',
            'fullname_card_number',
            'sheba_card_number',
            'shop_name',
            'shop_phone',
            'shop_address',
            'shop_zipcode',
            'is_shop_open',
        ])->pluck('body', 'type')->toArray();


        $this->data['card_number'] = $settings['card_number'] ?? null;
        $this->data['fullname_card_number'] = $settings['fullname_card_number'] ?? null;
        $this->data['sheba_card_number'] = $settings['sheba_card_number'] ?? null;

        $this->data['shop_name'] = $settings['shop_name'] ?? null;
        $this->data['shop_phone'] = $settings['shop_phone'] ?? null;
        $this->data['shop_address'] = $settings['shop_address'] ?? null;
        $this->data['shop_zipcode'] = $settings['shop_zipcode'] ?? null;
        $this->data['is_shop_open'] = isset($settings['is_shop_open']) && (int) $settings['is_shop_open'] === 1;


    }


    public function placeholder()
    {
        return <<<'HTML'
        <div
                    class="flex gap-3 rounded-md bg-white mt-4 h-96 min-h-96 "

                >
                    <svg
                        class="inline h-6 w-6 animate-spin text-red-700"
                        role="status"
                        aria-hidden="true"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="whitespace-nowrap text-sm font-bold text-gray-400"> درحال دریافت اطلاعات از سرور ...</span>
                </div>

        HTML;
    }

    public function save()
    {
        DB::beginTransaction();
        try {

            $types = [
                'card_number',
                'fullname_card_number',
                'sheba_card_number',
                'shop_name',
                'shop_phone',
                'shop_address',
                'shop_zipcode',
                'is_shop_open',
            ];

            foreach ($types as $item) {
                Setting::updateOrCreate(['type' => $item], ['body' => $this->data[$item]]);
            }

            DB::commit();

            Artisan::call('optimize:clear');
            $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
                'position' => 'top-start',
            ]);

            // return redirect('/setting');
        } catch (\Exception $e) {

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }
    }

    public function render()
    {
        return view('livewire.dashboard.admin.setting.variables');
    }
}
