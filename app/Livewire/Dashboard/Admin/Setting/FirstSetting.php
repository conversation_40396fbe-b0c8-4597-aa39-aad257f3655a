<?php

namespace App\Livewire\Dashboard\Admin\Setting;

use App\Models\Setting;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Lazy;
use Livewire\Component;

class FirstSetting extends Component
{
    use LivewireAlert;

    public array $data = [
        'gold18k_status' => true,
        'gold18k' => '',
        'gold18kup' => '',
        'factorId' => '',
        'profit' => '',
        'construction_wages' => '',
        'tax' => '',
        'expireTimeLink' => '',
        'factor' => '',
        'idleTimeout' => '',
        'countdownTime' => '',
        'popupTime' => '',
        'post_price' => '',
        'card_number' => '',
        'fullname_card_number' => '',
        'sheba_card_number'
    ];

    public function loadGold()
    {

        if ($this->data['gold18k_status'] == true || $this->data['gold18k_status'] == 1) {
            $this->data['gold18k'] = getGold18kOrigin();
        }

        DB::beginTransaction();
        try {

            $types = [
                'gold18k_status',
                'gold18k',
                'gold18kup',
                'factor',
                'expireTimeLink',
                'profit',
                'construction_wages',
                'tax',
                'idleTimeout',
                'countdownTime',
                'popupTime',
                'post_price',
                // 'card_number',
                // 'fullname_card_number',
                // 'sheba_card_number'
            ];

            foreach ($types as $item) {
                Setting::updateOrCreate(['type' => $item], ['body' => $this->data[$item]]);
            }

            DB::commit();
            $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
                'position' => 'top-start',
            ]);

            // return redirect('/setting');
        } catch (\Exception $exceptionxception) {

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'سیستم با خطا مواجه شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }
    }

    public function mount()
    {
        $settings = Setting::whereIn('type', [
            'gold18k',
            'factorId',
            'gold18kup',
            'gold18k_status',
            'factor',
            'profit',
            'profit_offer',
            'weight_greater_than',
            'construction_wages',
            'tax',
            'expireTimeLink',
            'idleTimeout',
            'countdownTime',
            'popupTime',
            'post_price',
            // 'card_number',
            // 'fullname_card_number',
            // 'sheba_card_number',
        ])->pluck('body', 'type')->toArray();

        $this->data['gold18k_status'] = $settings['gold18k_status'] == 1 ? true : false;
        $this->data['factor'] = isset($settings['factor']) && $settings['factor'] == 1 ? true : false;

        $this->data['gold18kup'] = formatMoney($settings['gold18kup']) ?? null;
        $this->data['factorId'] = $settings['factorId'] ?? null;
        $this->data['expireTimeLink'] = $settings['expireTimeLink'] ?? null;
        $this->data['construction_wages'] = $settings['construction_wages'] ?? null;
        $this->data['profit'] = $settings['profit'] ?? null;
        $this->data['profit_offer'] = $settings['profit_offer'] ?? null;
        $this->data['weight_greater_than'] = $settings['weight_greater_than'] ?? null;
        $this->data['tax'] = $settings['tax'] ?? null;
        $this->data['idleTimeout'] = $settings['idleTimeout'] ?? null;
        $this->data['countdownTime'] = $settings['countdownTime'] ?? null;
        $this->data['popupTime'] = $settings['popupTime'] ?? null;
        $this->data['post_price'] = formatMoney($settings['post_price']) ?? null;
        // $this->data['card_number'] = $settings['card_number'] ?? null;
        // $this->data['fullname_card_number'] = $settings['fullname_card_number'] ?? null;
        // $this->data['sheba_card_number'] = $settings['sheba_card_number'] ?? null;

        $this->loadGold18k($settings['gold18k']);
    }

    #[Lazy]
    public function loadGold18k($gold18k)
    {
        if ($this->data['gold18k_status'] == true) {
            $this->data['gold18k'] = getGold18kOrigin();
        } else {
            $this->data['gold18k'] = formatMoney($gold18k) ?? null;
        }
    }

    public function placeholder()
    {
        return <<<'HTML'
        <div
                    class="flex gap-3 rounded-md bg-white mt-4 h-96 min-h-96 "

                >
                    <svg
                        class="inline h-6 w-6 animate-spin text-red-700"
                        role="status"
                        aria-hidden="true"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="whitespace-nowrap text-sm font-bold text-gray-400"> درحال دریافت اطلاعات از سرور ...</span>
                </div>

        HTML;
    }

    public function save()
    {
        DB::beginTransaction();
        try {

            $types = [
                'gold18k_status',
                'gold18k',
                'gold18kup',
                'factorId',
                'factor',
                'expireTimeLink',
                'profit',
                'profit_offer',
                'weight_greater_than',
                'construction_wages',
                'tax',
                'idleTimeout',
                'countdownTime',
                'popupTime',
                'post_price',
                // 'card_number',
                // 'fullname_card_number',
                // 'sheba_card_number',
            ];

            foreach ($types as $item) {
                Setting::updateOrCreate(['type' => $item], ['body' => $this->data[$item]]);
            }

            DB::commit();

            Artisan::call('optimize:clear');
            $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
                'position' => 'top-start',
            ]);

            // return redirect('/setting');
        } catch (\Exception $e) {

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }
    }

    public function render()
    {
        return view('livewire.dashboard.admin.setting.first-setting');
    }
}
