<?php

namespace App\Livewire\Dashboard\Admin\Setting;

use Livewire\Component;

class Calculator extends Component
{
    public array $data = [
        'weight',
        'gold18k',
        'construction_wages',
        'profit' => '7',
        'tax' => '10',
        'product_price' => 0,
        'commission' => 0,
        'total' => 0,

    ];

    public function mount()
    {
        $this->data['gold18k'] = getGold18k();
    }

    public function calculator()
    {
        // گرفتن مقادیر و تبدیل به float با مقدار پیش‌فرض صفر
        $gold18K = (float) str_replace(',', '', $this->data['gold18k'] ?? 0);
        $weight = (float) ($this->data['weight'] ?? 0);
        $constructionWagesPercent = (float) ($this->data['construction_wages'] ?? 0) / 100;
        $profitPercent = (float) ($this->data['profit'] ?? 0) / 100;
        $taxPercent = (float) ($this->data['tax'] ?? 0) / 100;

        // اجرت ساخت
        $wage = $weight * $constructionWagesPercent;

        // سود
        $profit = ($weight + $wage) * $profitPercent;

        // قیمت کل بدون مالیات
        $totalPriceBeforeTax = $weight + $wage + $profit;

        // قیمت کل ضرب در قیمت طلا
        $totalPrice = $totalPriceBeforeTax * $gold18K;

        // محاسبه مالیات
        $tax = ($totalPrice - ($weight * $gold18K)) * $taxPercent;

        // قیمت نهایی
        $finalPrice = $totalPrice + $tax;

        // ذخیره نتایج در data
        $this->data['total'] = formatMoney($finalPrice) ?? 0;

        // کمیسیون

        $commission = calcCommission($weight, $gold18K);

        $this->data['commission'] = formatMoney($commission) ?? 0;

        // قیمت کالا
        $rawGold = $weight * $gold18K;
        $productProfit = $rawGold * $profitPercent;
        $productPrice = $productProfit;
        $this->data['product_price'] = formatMoney($productPrice) ?? 0;

        // فرمت قیمت طلا
        $this->data['gold18k'] = formatMoney($gold18K);
    }

    public function render()
    {
        return view('livewire.dashboard.admin.setting.calculator');
    }
}
