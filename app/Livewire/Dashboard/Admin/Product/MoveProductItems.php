<?php

namespace App\Livewire\Dashboard\Admin\Product;

use App\Models\ProductDetail;
use App\Models\ProductDetailMove;
use Illuminate\Support\Facades\DB;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;

class MoveProductItems extends Component
{
    use LivewireAlert;

    public array $form = [
        'branchId' => null,
    ];

    public array $selectedItems = [];

    #[On('move-product-items')]
    public function getProductDetails($selectedItems)
    {
        $this->selectedItems = $selectedItems;
        // dd($selectedItems);
    }

    public function save()
    {

        try {

            DB::beginTransaction();
            if (isset($this->selectedItems) && $this->selectedItems != []) {
                foreach ($this->selectedItems as $item) {

                    $productItem = ProductDetail::whereId($item)->first();

                    if ($productItem) {

                        ProductDetailMove::create([
                            'product_id' => $productItem->product_id,
                            'eticket' => $productItem->eticket,
                            'weight' => $productItem->weight,
                            'cutie' => $productItem->cutie,
                            'profit' => $productItem->profit,
                            'construction_wages' => $productItem->construction_wages,
                            'color_id' => $productItem->color_id,
                            'branch_id' => $productItem->branch_id,
                            'branch_to_id' => $this->form['branchId'],
                            'amount' => $productItem->amount,
                            'tax' => $productItem->tax,
                            'stone_price' => $productItem->stone_price,
                            'chain_size' => $productItem->chain_size,
                            'pluck' => $productItem->pluck,
                            'model' => $productItem->model,
                            'reserved_count' => $productItem->reserved_count,
                            'gold18k' => $productItem->gold18k,
                            'out_stock' => $productItem->out_stock,
                        ]);

                        $productItem->branch_id = $this->form['branchId'];
                        $productItem->save();
                    }

                }
                DB::commit();

                $this->alert('success', 'ثبت موفق', [
                    'position' => 'center',
                    'timer' => 30000,
                    'toast' => false,
                    'text' => 'جابجایی اطلاعات با موفقیت انجام شد',
                    'timerProgressBar' => true,
                    'showDenyButton' => true,
                    'onDenied' => '',
                    'denyButtonText' => 'بسیار خب متوجه شدم',
                ]);

                return redirect()->route('admin-dashboard-products');
            }
        } catch (\Exception $e) {

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 30000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }

        // dd($this->selectedItems);

    }

    public function render()
    {
        return view('livewire.dashboard.admin.product.move-product-items');
    }
}
