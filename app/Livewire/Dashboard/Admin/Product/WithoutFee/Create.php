<?php

namespace App\Livewire\Dashboard\Admin\Product\WithoutFee;

use App\Models\ProductGallery;
use App\Models\ProductWithoutFee;
use Exception;
use Illuminate\Support\Facades\DB;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;

class Create extends Component
{
    use LivewireAlert;

    public array $files = [];

    public array $image = [];

    public array $form = [
        'title' => null,
        'fullname_customer' => null,
        'phone' => null,
        'national_number' => null,
        'weight' => null,
        'work_rate' => null,
        'payer_details' => null,
        'description' => null,
        'bank' => null,
    ];

    public function rules()
    {
        return [
            'form.title' => 'required',
            'form.fullname_customer' => 'required',
            'form.phone' => 'required',
            'form.national_number' => 'required',
            'form.weight' => 'required',
            'form.work_rate' => 'required',
            'form.payer_details' => 'required',
            'form.bank' => 'required',
        ];
    }

    public function confirmRemoveImage($key)
    {
        $this->alert('warning', 'آیا مطمئن هستید که می‌خواهید حذف کنید؟', [
            'position' => 'center',
            'toast' => false,
            'showConfirmButton' => true,
            'confirmButtonText' => 'بله',
            'showCancelButton' => true,
            'cancelButtonText' => 'خیر',
            'onConfirmed' => 'removeImage',
            'data' => ['key' => $key],
            'customClass' => [
                'htmlContainer' => 'text-base',  // کلاس سفارشی برای متن
            ],
        ]);

    }
    protected $listeners = [
        'removeImage',
    ];
    public function removeImage($data)
    {
        $key = $data['key'];

        if (isset($this->files[$key])) {
            unset($this->files[$key]);
            $this->files = array_values($this->files);
        }

        $this->alert('success', 'تصویر با موفقیت حذف شد.');
    }

    #[On('upload-product-image')]
    public function addImage($image)
    {
        array_push($this->files, $image);
    }

    public function saveFileFromTemporaryUrl($temporaryUrl, $directory = 'product-gallery')
    {
        try {
            // دریافت محتویات فایل
            $fileContents = file_get_contents($temporaryUrl);

            if (!$fileContents) {
                throw new Exception("Failed to retrieve file contents from URL: $temporaryUrl");
            }

            // تولید نام منحصربه‌فرد برای فایل
            $fileName = uniqid() . '.' . pathinfo(parse_url($temporaryUrl, PHP_URL_PATH), PATHINFO_EXTENSION);

            // مسیر ذخیره فایل
            $storagePath = storage_path("app/public/{$directory}/");

            // اطمینان از وجود پوشه
            if (!is_dir($storagePath)) {
                mkdir($storagePath, 0755, true);
            }

            // ذخیره فایل
            file_put_contents($storagePath . $fileName, $fileContents);

            // بازگرداندن آدرس فایل ذخیره‌شده
            return "storage/{$directory}/{$fileName}";
        } catch (Exception $e) {
            // مدیریت خطا
            return null;
        }
    }

    public function messages()
    {
        return [
            'form.title.required' => 'پر کردن فیلد الزامیست',
            'form.fullname_customer.required' => 'پر کردن فیلد الزامیست',
            'form.phone.required' => 'پر کردن فیلد الزامیست',
            'form.national_number.required' => 'پر کردن فیلد الزامیست',
            'form.weight.required' => 'پر کردن فیلد الزامیست',
            'form.work_rate.required' => 'پر کردن فیلد الزامیست',
            'form.payer_details.required' => 'پر کردن فیلد الزامیست',
            'form.bank.required' => 'پر کردن فیلد الزامیست',
        ];
    }

    public function save()
    {

        $this->validate();

        if (count($this->files) < 2) {
            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 30000,
                'toast' => false,
                'text' => 'حداقل دو فایل باید انتخاب شود.',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

            return;
        }

        try {

            DB::beginTransaction();
            $product = ProductWithoutFee::create([
                'user_id' => auth()->user()->id,
                'title' => $this->form['title'],
                'fullname_customer' => $this->form['fullname_customer'],
                'phone' => $this->form['phone'],
                'national_number' => $this->form['national_number'],
                'weight' => $this->form['weight'],
                'work_rate' => $this->form['work_rate'],
                'payer_details' => $this->form['payer_details'],
                'description' => $this->form['description'],
                'bank' => $this->form['bank'],

            ]);

            foreach ($this->files as $file) {
                $temporaryUrl = $file['image'];

                // ذخیره فایل و دریافت آدرس ذخیره‌شده
                $savedFilePath = $this->saveFileFromTemporaryUrl($temporaryUrl);
                // $fileFormat = pathinfo(parse_url($file['image'], PHP_URL_PATH), PATHINFO_EXTENSION);
                ProductGallery::create([
                    'product_id' => $product->id,
                    'url' => $savedFilePath,
                    'format' => 'image',
                ]);
            }

            DB::commit();

            return redirect()->route('admin-dashboard-products-without-fee');

        } catch (\Exception $e) {

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 30000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }

    }

    public function render()
    {
        return view('livewire.dashboard.admin.product.without-fee.create');
    }
}
