<?php

namespace App\Livewire\Dashboard\Admin\Product\WithoutFee;

use App\Jobs\UpdateAmountProductsJob;
use App\Models\Product;
use App\Models\ProductCount;
use App\Models\ProductDetail;
use App\Models\ProductGallery;
use App\Models\ProductWithoutFee;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;

class ShowProductDetail extends Component
{
    use LivewireAlert;

    public array $files = [];

    public array $image = [];

    public $productId;

    public $productDetailId;

    public array $form = [
        'title' => null,
        'fullname_customer' => null,
        'phone' => null,
        'national_number' => null,
        'weight' => null,
        'work_rate' => null,
        'payer_details' => null,
        'description' => null,
        'bank' => null,

        'forId' => null,
        'categoryId' => null,

        'colorId' => null,
        'construction_wages' => 0,
        'cutie' => 18,
        'tax' => 0,
        'profit' => 7,
        'chain_size' => null,
        'pluck' => null,
        'stone_price' => null,
        'eticket' => null,
        'weight_product' => null,
        'branchId' => null,
    ];

    public function confirmRemoveImage($key)
    {
        $this->alert('warning', 'آیا مطمئن هستید که می‌خواهید حذف کنید؟', [
            'position' => 'center',
            'toast' => false,
            'showConfirmButton' => true,
            'confirmButtonText' => 'بله',
            'showCancelButton' => true,
            'cancelButtonText' => 'خیر',
            'onConfirmed' => 'removeImage',
            'data' => ['key' => $key],
            'customClass' => [
                'htmlContainer' => 'text-base',  // کلاس سفارشی برای متن
            ],
        ]);

    }
    protected $listeners = [
        'removeImage',
    ];
    public function removeImage($data)
    {
        $key = $data['key'];

        if (isset($this->files[$key])) {
            unset($this->files[$key]);
            $this->files = array_values($this->files);
        }

        $this->alert('success', 'تصویر با موفقیت حذف شد.');
    }

    #[On('upload-product-image')]
    public function addImage($image)
    {
        array_push($this->files, $image);
    }

    public function saveFileFromTemporaryUrl($temporaryUrl, $directory = 'product-gallery')
    {
        try {
            // دریافت محتویات فایل
            $fileContents = file_get_contents($temporaryUrl);

            if (!$fileContents) {
                throw new Exception("Failed to retrieve file contents from URL: $temporaryUrl");
            }

            // تولید نام منحصربه‌فرد برای فایل
            $fileName = uniqid() . '.' . pathinfo(parse_url($temporaryUrl, PHP_URL_PATH), PATHINFO_EXTENSION);

            // مسیر ذخیره فایل
            $storagePath = storage_path("app/public/{$directory}/");

            // اطمینان از وجود پوشه
            if (!is_dir($storagePath)) {
                mkdir($storagePath, 0755, true);
            }

            // ذخیره فایل
            file_put_contents($storagePath . $fileName, $fileContents);

            // بازگرداندن آدرس فایل ذخیره‌شده
            return "storage/{$directory}/{$fileName}";
        } catch (Exception $e) {
            // مدیریت خطا
            return null;
        }
    }

    public function rules()
    {
        return [
            'form.title' => 'required',
            'form.fullname_customer' => 'required',
            'form.phone' => 'required',
            'form.national_number' => 'required',
            'form.weight' => 'required',
            'form.work_rate' => 'required',
            'form.payer_details' => 'required',
            'form.description' => 'required',
            'form.bank' => 'required',
            'form.forId' => 'required',
            'form.categoryId' => 'required',
            'form.colorId' => 'required',
            'form.construction_wages' => 'required',
            'form.cutie' => 'required',
            'form.tax' => 'required',
            'form.profit' => 'required',
            // 'form.chain_size' => 'required',
            // 'form.pluck' => 'required',
            // 'form.stone_price' => 'required',
            'form.eticket' => 'required',
            'form.weight_product' => 'required',
            'form.branchId' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'form.title.required' => 'عنوان محصول الزامی است',
            'form.fullname_customer.required' => 'نام مشتری الزامی است',
            'form.phone.required' => 'شماره تماس الزامی است',
            'form.national_number.required' => 'کد ملی الزامی است',
            'form.weight.required' => 'وزن الزامی است',
            'form.work_rate.required' => 'درصد اجرت الزامی است',
            'form.payer_details.required' => 'اطلاعات پرداخت‌کننده الزامی است',
            'form.description.required' => 'توضیحات الزامی است',
            'form.bank.required' => 'بانک الزامی است',
            'form.forId.required' => 'مورد استفاده الزامی است',
            'form.categoryId.required' => 'دسته‌بندی الزامی است',
            'form.colorId.required' => 'رنگ محصول الزامی است',
            'form.construction_wages.required' => 'اجرت ساخت الزامی است',
            'form.cutie.required' => 'عیار محصول الزامی است',
            'form.tax.required' => 'مقدار مالیات الزامی است',
            'form.profit.required' => 'سود الزامی است',
            'form.chain_size.required' => 'اندازه زنجیر الزامی است',
            'form.pluck.required' => 'نوع پلاک الزامی است',
            'form.stone_price.required' => 'قیمت سنگ الزامی است',
            'form.eticket.required' => 'کد اتیکت الزامی است',
            'form.weight_product.required' => 'وزن نهایی الزامی است',
            'form.branchId.required' => 'انتخاب شعبه الزامی است',
        ];
    }

    #[On('show-detail-product')]
    public function loadProductDetail($productId)
    {
        $this->productId = $productId;
        $product = ProductWithoutFee::whereId($productId)->firstOrFail();
        $this->form['title'] = $product->title;
        $this->form['fullname_customer'] = $product->fullname_customer;
        $this->form['phone'] = $product->phone;
        $this->form['national_number'] = $product->national_number;
        $this->form['weight'] = $product->weight;
        $this->form['work_rate'] = $product->work_rate;
        $this->form['payer_details'] = $product->payer_details;
        $this->form['description'] = $product->description;
        $this->form['bank'] = $product->bank;

        $this->files = $product?->gallery?->map(function ($image) {
            return [
                'image' => '/' . $image->url, // توجه کن مسیر از public شروع بشه
                'is_old' => true, // برای تشخیص عکس‌های قبلی
                'id' => $image->id, // شاید برای حذف بعدی بخوای ازش استفاده کنی
            ];
        })->toArray();

        $this->productDetailId = null;
        if ($product->product_id != null) {

            $this->productDetailId = $product->product_id;
            $p = Product::whereId($product->product_id)->first();

            $this->form['forId'] = $p->for_id;
            $this->form['categoryId'] = $p->category_id;
            $this->form['title'] = $p->title_fa;

            $pd = ProductDetail::whereProductId($p->id)->first();
            $this->form['eticket'] = $pd?->eticket;
            $this->form['weight_product'] = $pd?->weight;
            $this->form['cutie'] = $pd?->cutie;
            $this->form['profit'] = $pd?->profit;
            $this->form['construction_wages'] = $pd?->construction_wages;
            $this->form['colorId'] = $pd?->color_id;
            $this->form['branchId'] = $pd?->branch_id;
            $this->form['tax'] = $pd?->tax;
            $this->form['stone_price'] = $pd?->stone_price;
            $this->form['chain_size'] = $pd?->chain_size;
            $this->form['pluck'] = $pd?->pluck;
        }

    }

    public function update()
    {

        $this->validate();

        if (count($this->files) < 2) {
            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 30000,
                'toast' => false,
                'text' => 'حداقل دو فایل باید انتخاب شود.',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

            return;
        }

        try {

            DB::beginTransaction();

            $product = ProductWithoutFee::findOrFail($this->productId);
            $product->update($this->form);

            // حذف عکس‌هایی که دیگه نیستند
            $existingIds = collect($this->files)->filter(fn($f) => isset($f['is_old']) && $f['is_old'])->pluck('id')->toArray();
            $product->gallery()->whereNotIn('id', $existingIds)->delete();

            // ذخیره عکس‌های جدید
            foreach ($this->files as $file) {
                if (!isset($file['is_old'])) {
                    $savedPath = $this->saveFileFromTemporaryUrl($file['image']);
                    ProductGallery::create([
                        'product_id' => $product->id,
                        'url' => $savedPath,
                        'format' => 'image',
                    ]);
                }
            }

            if ($this->productDetailId == null) {
                if ($this->form['eticket'] != null) {

                    if ($product->product_id == null) {

                        $p = Product::create([
                            'user_id' => auth()->user()->id,
                            'title_fa' => $this->form['title'],
                            'title_eng' => $this->form['title'],
                            'slug' => Str::slug($this->form['title'], '-'),
                            'category_id' => $this->form['categoryId'],
                            'for_id' => $this->form['forId'],
                            'description' => $this->form['description'],
                            'meta_keywords' => '',
                            'meta_description' => '',
                        ]);

                        foreach ($this->files as $file) {

                            // dd($file['image']);
                            // $temporaryUrl = $file['image'];
                            // $savedFilePath = $this->saveFileFromTemporaryUrl($temporaryUrl);

                            ProductGallery::create([
                                'product_id' => $p->id, // یا اگر نام متغیر متفاوت است، جایگزین شود
                                'url' => $file['image'] ?? null,
                                'format' => 'image',
                            ]);
                        }

                        $pd = ProductDetail::create([
                            'product_id' => $p->id,
                            'eticket' => $this->form['eticket'],
                            'weight' => $this->form['weight_product'],
                            'cutie' => $this->form['cutie'],
                            'profit' => $this->form['profit'],
                            'construction_wages' => $this->form['construction_wages'],
                            'color_id' => $this->form['colorId'],
                            'branch_id' => $this->form['branchId'],
                            'tax' => $this->form['tax'],
                            'stone_price' => $this->form['stone_price'],
                            'chain_size' => $this->form['chain_size'],
                            'pluck' => $this->form['pluck'],
                            // 'model' => $this->form['model'],
                        ]);

                        ProductCount::create([
                            'product_id' => $p->id,
                            'product_detail_id' => $pd->id,
                            'count' => 1,

                        ]);

                        $product = ProductWithoutFee::findOrFail($this->productId);
                        $product->update([
                            'product_id' => $p->id,
                            'status' => 'success',
                        ]);
                    }

                }
            }


            Artisan::call('optimize:clear');
            UpdateAmountProductsJob::dispatch();

            DB::commit();

            $this->alert('success', 'بروزرسانی موفق', [
                'position' => 'center',
                'timer' => 30000,
                'toast' => false,
                'text' => 'اطلاعات با موفقیت بروزرسانی شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        } catch (\Exception $e) {

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 30000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }

    }

    public function render()
    {
        return view('livewire.dashboard.admin.product.without-fee.show-product-detail');
    }
}
