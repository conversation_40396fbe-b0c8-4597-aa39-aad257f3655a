<?php

namespace App\Livewire\Dashboard\Admin\Product;

use App\Models\Product;
use App\Models\ProductCount;
use App\Models\ProductDetail;
use App\Models\ProductGallery;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;

class Create extends Component
{
    use LivewireAlert;

    public array $data = [
        'title_fa' => null,
        'title_eng' => null,
        'categoryId' => null,
        'forId' => null,
        'description' => null,
        'meta_keywords' => null,
        'meta_description' => null,
        'amount' => null,
        'count' => null,
        'eticket' => null,
        'commission' => null,
    ];

    public array $files = [];

    public function rules()
    {
        return [
            'data.title_fa' => 'required',
            'data.title_eng' => 'required',
            'data.categoryId' => 'required',
            'data.forId' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.title_fa.required' => 'پر کردن فیلد الزامیست',
            'data.title_eng.required' => 'پر کردن فیلد الزامیست',
            'data.categoryId.required' => 'پر کردن فیلد الزامیست',
            'data.forId.required' => 'پر کردن فیلد الزامیست',
        ];
    }

    public function confirmRemoveImage($key)
    {
        $this->alert('warning', 'آیا مطمئن هستید که می‌خواهید حذف کنید؟', [
            'position' => 'center',
            'toast' => false,
            'showConfirmButton' => true,
            'confirmButtonText' => 'بله',
            'showCancelButton' => true,
            'cancelButtonText' => 'خیر',
            'onConfirmed' => 'removeImage',
            'data' => ['key' => $key],
            'customClass' => [
                'htmlContainer' => 'text-base',  // کلاس سفارشی برای متن
            ],
        ]);

    }
    protected $listeners = [
        'removeImage',
    ];
    public function removeImage($data)
    {
        $key = $data['key'];

        if (isset($this->files[$key])) {
            unset($this->files[$key]);
            $this->files = array_values($this->files);
        }

        $this->alert('success', 'تصویر با موفقیت حذف شد.');
    }

    #[On('upload-product-image')]
    public function addImage($image)
    {
        array_push($this->files, $image);
    }

    public function saveFileFromTemporaryUrl($temporaryUrl, $directory = 'product-gallery')
    {
        try {
            // دریافت محتویات فایل
            $fileContents = file_get_contents($temporaryUrl);

            if (!$fileContents) {
                throw new Exception("Failed to retrieve file contents from URL: $temporaryUrl");
            }

            // تولید نام منحصربه‌فرد برای فایل
            $fileName = uniqid() . '.' . pathinfo(parse_url($temporaryUrl, PHP_URL_PATH), PATHINFO_EXTENSION);

            // مسیر ذخیره فایل
            $storagePath = storage_path("app/public/{$directory}/");

            // اطمینان از وجود پوشه
            if (!is_dir($storagePath)) {
                mkdir($storagePath, 0755, true);
            }

            // ذخیره فایل
            file_put_contents($storagePath . $fileName, $fileContents);

            // بازگرداندن آدرس فایل ذخیره‌شده
            return "storage/{$directory}/{$fileName}";
        } catch (Exception $e) {
            // مدیریت خطا
            return null;
        }
    }

    public function save()
    {

        $this->validate();

        try {

            DB::beginTransaction();
            $product = Product::create([
                'user_id' => auth()->user()->id,
                'title_fa' => $this->data['title_fa'],
                'title_eng' => $this->data['title_eng'],
                'slug' => Str::slug($this->data['title_eng'], '-'),
                'category_id' => $this->data['categoryId'],
                'for_id' => $this->data['forId'],
                'description' => $this->data['description'],
                'meta_keywords' => $this->data['meta_keywords'],
                'meta_description' => $this->data['meta_description'],
                'fixed_amount' => str_replace(',', '', $this->data['amount']),
                'fixed_commission' => str_replace(',', '', $this->data['commission']),
            ]);

            foreach ($this->files as $file) {
                $temporaryUrl = $file['image'];

                // ذخیره فایل و دریافت آدرس ذخیره‌شده
                $savedFilePath = $this->saveFileFromTemporaryUrl($temporaryUrl);
                // $fileFormat = pathinfo(parse_url($file['image'], PHP_URL_PATH), PATHINFO_EXTENSION);
                ProductGallery::create([
                    'product_id' => $product->id,
                    'url' => $savedFilePath,
                    'format' => 'image',
                ]);
            }

            if (!empty($this->data['amount'])) {
                $ProductDetail = ProductDetail::create([
                    'product_id' => $product->id,
                    'amount' => str_replace(',', '', $this->data['amount']),
                    'eticket' => $this->data['eticket'],
                ]);
                ProductCount::create([
                    'count' => $this->data['count'],
                    'amount' => str_replace(',', '', $this->data['amount']),
                    'product_detail_id' => $ProductDetail->id,
                    'product_id' => $product->id,
                ]);
            }
            DB::commit();

            Artisan::call('optimize:clear');

            if (!empty($this->data['amount'])) {
                return redirect()->route('admin-dashboard-products');
            }

            return redirect()->route('admin-dashboard-product-details-show', ['productId' => hashId($product->id)]);

        } catch (\Exception $e) {

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 30000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }

    }

    public function render()
    {
        return view('livewire.dashboard.admin.product.create');
    }
}
