<?php

namespace App\Livewire\Dashboard\Admin\Product;

use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\WithFileUploads;

class UploadImage extends Component
{
    use WithFileUploads;

    #[Validate('image|max:3040')]
    public $images = [];

    public function updatedImages()
    {
        foreach ($this->images as $image) {
            $this->dispatch('upload-product-image', [
                'image' => $image->temporaryUrl(),
            ]);
        }
        $this->images = [];
    }

    public $imageNumber = 0;

    public $listeners = ['$refresh', 'addImageNumber'];

    public function addImageNumber($count)
    {
        $this->imageNumber = $count;
    }

    // public function updatedImage()
    // {
    //     if ($this->image) {
    //         $this->dispatch('upload-product-image', [
    //             'image' => $this->image->temporaryUrl(),
    //         ]);

    //         $this->image = null;
    //     }
    // }

    public function render()
    {
        return view('livewire.dashboard.admin.product.upload-image');
    }
}
