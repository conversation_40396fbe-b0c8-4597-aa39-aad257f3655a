<?php

namespace App\Livewire\Dashboard\Admin\Product;

use App\Models\Category;
use App\Models\Product;
use Livewire\Component;
use Livewire\WithPagination;

class Lists extends Component
{
    use WithPagination;

    public $category_id;

    public $title;

    public $categories = [];

    public function mount()
    {
        $this->categories = Category::get();
    }

    public function fillter() {}

    public function ClearFillter()
    {
        $this->category_id = null;
        $this->title = null;

    }

    public function render()
    {
        $query = Product::with('gallery', 'category', 'productCount', 'details');

        if ($this->category_id != null) {
            $query->where('category_id', $this->category_id);
        }

        $title = $this->title;
        if ($title != null) {
            $query->where('title_fa', 'like', '%'.$title.'%');
        }

        $products = $query->latest()->paginate(100);

        return view('livewire.dashboard.admin.product.lists', ['products' => $products]);
    }
}
