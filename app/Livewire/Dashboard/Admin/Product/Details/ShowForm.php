<?php

namespace App\Livewire\Dashboard\Admin\Product\Details;

use App\Jobs\GetPriceGold18kProductJob;
use App\Models\ProductCount;
use App\Models\ProductDetail;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;

class ShowForm extends Component
{
    use LivewireAlert;

    public bool $redirectTo = false;

    public $form = [
        'eticket' => null,
        'branchId' => null,
        'colorId' => null,
        'count' => null,
        'construction_wages' => null,
        'weight' => null,
        'cutie' => 18,
        'profit' => null,
        'tax' => '',
        'chain_size' => '',
        'stone_price' => '',
        'pluck' => '',
        'model' => '',
        'details' => '',
        'status' => '',
    ];

    public $productDetailId;

    #[On('product-detail-show')]
    public function getProductDetails($productDetailId)
    {

        $this->productDetailId = $productDetailId;
        $productDetail = ProductDetail::with('color')->whereId(unHashId($this->productDetailId))->firstorfail();
        $this->form['eticket'] = $productDetail->eticket;
        $this->form['branchId'] = $productDetail->branch_id;
        $this->form['colorId'] = $productDetail->color_id;
        $this->form['construction_wages'] = $productDetail->construction_wages;
        $this->form['weight'] = $productDetail->weight;
        $this->form['cutie'] = $productDetail->cutie;
        $this->form['profit'] = $productDetail->profit;
        $this->form['tax'] = $productDetail->tax;
        $this->form['count'] = $productDetail->productCount?->count ?? 0;
        $this->form['stone_price'] = formatMoney($productDetail->stone_price);
        $this->form['chain_size'] = formatMoney($productDetail->chain_size);
        $this->form['stone_price'] = formatMoney($productDetail->stone_price);
        // $this->form['chain_size'] = $productDetail->chain_size;
        $this->form['pluck'] = $productDetail->pluck;
        $this->form['model'] = $productDetail->model;
        $this->form['status'] = $productDetail->status;

    }

    protected $rules = [
        'form.eticket' => 'required|string|max:255',
        'form.branchId' => 'required|integer',
        'form.colorId' => 'required|integer',
        'form.count' => 'required|integer',
        'form.construction_wages' => 'required|numeric|min:0',
        'form.weight' => 'required',
        'form.cutie' => 'required|max:255',
        'form.profit' => 'required|numeric|min:0',
        'form.tax' => 'required',
    ];

    protected $messages = [
        'form.cutie.required' => 'فیلد اعیار الزامی است.',
        'form.eticket.required' => 'فیلد کد تیکت الزامی است.',
        'form.profit.required' => 'فیلد سود الزامی است.',
        'form.construction_wages.required' => 'فیلد اجرت ساخت الزامی است.',
        'form.eticket.string' => 'فیلد کد تیکت باید یک رشته باشد.',
        'form.eticket.max' => 'فیلد کد تیکت نباید بیشتر از ۲۵۵ کاراکتر باشد.',
        'form.branchId.required' => 'انتخاب شعبه الزامی است.',
        'form.branchId.integer' => 'شناسه شعبه باید عدد صحیح باشد.',
        'form.branchId.exists' => 'شناسه شعبه معتبر نیست.',
        'form.colorId.required' => 'انتخاب رنگ الزامی است.',
        'form.colorId.integer' => 'شناسه رنگ باید عدد صحیح باشد.',
        'form.colorId.exists' => 'شناسه رنگ معتبر نیست.',
        'form.count.required' => 'فیلد تعداد الزامی است.',
        'form.count.integer' => 'فیلد تعداد باید عدد صحیح باشد.',
        'form.count.min' => 'حداقل مقدار تعداد باید ۱ باشد.',
        'form.construction_wages.numeric' => 'فیلد اجرت ساخت باید عددی باشد.',
        'form.construction_wages.min' => 'حداقل مقدار اجرت ساخت نمی‌تواند کمتر از صفر باشد.',
        'form.weight.required' => 'فیلد وزن الزامی است.',
        'form.cutie.string' => 'فیلد توضیحات باید یک رشته باشد.',
        'form.cutie.max' => 'فیلد توضیحات نباید بیشتر از ۲۵۵ کاراکتر باشد.',
        'form.profit.numeric' => 'فیلد سود باید عددی باشد.',
        'form.profit.min' => 'مقدار سود نمی‌تواند کمتر از صفر باشد.',
        'form.tax.required' => 'درصد مالیات محصول را مشخص کنید.',
    ];

    public function save()
    {
        $this->validate();

        try {

            DB::beginTransaction();
            ProductDetail::whereId(unHashId($this->productDetailId))->update([
                'eticket' => $this->form['eticket'],
                'weight' => $this->form['weight'],
                'cutie' => $this->form['cutie'],
                'profit' => $this->form['profit'],
                'tax' => $this->form['tax'],
                'color_id' => (int) $this->form['colorId'],
                'branch_id' => (int) $this->form['branchId'],
                'construction_wages' => $this->form['construction_wages'],
                'chain_size' => str_replace(',', '', $this->form['chain_size']),
                'stone_price' => str_replace(',', '', $this->form['stone_price']),
                // 'chain_size' => $this->form['chain_size'],
                'pluck' => $this->form['pluck'],
                'model' => $this->form['model'],
                'status' => $this->form['status'],
            ]);

            ProductCount::where('product_detail_id', unHashId($this->productDetailId))->update([
                'count' => (int) $this->form['count'],
            ]);

            Artisan::call('optimize:clear');

            $productId = unHashId($this->productDetailId);
            GetPriceGold18kProductJob::dispatch($productId);

            DB::commit();

            $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
                'position' => 'top-start',
            ]);

            Artisan::call('optimize:clear');

            if ($this->redirectTo) {
                return redirect()->route('admin-dashboard-products');
            }

            $this->dispatch('reload-products');

        } catch (\Exception $e) {

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }

    }

    public function render()
    {
        return view('livewire.dashboard.admin.product.details.show-form');
    }
}
