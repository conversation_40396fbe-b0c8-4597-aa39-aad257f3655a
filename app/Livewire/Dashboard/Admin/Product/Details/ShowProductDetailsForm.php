<?php

namespace App\Livewire\Dashboard\Admin\Product\Details;

use App\Jobs\GetPriceGold18kProductJob;
use App\Models\ProductCount;
use App\Models\ProductDetail;
use App\Models\Setting;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;

class ShowProductDetailsForm extends Component
{
    use LivewireAlert;

    public $product;

    public $form = [
        'eticket' => null,
        'branchId' => null,
        'colorId' => null,
        'count' => 1,
        'construction_wages' => null,
        'weight' => null,
        'cutie' => 18,
        'profit' => null,
        'tax' => 9,
        'chain_size' => '',
        'stone_price' => '',
        'pluck' => '',
        'model' => '',
    ];

    public $data = [
        'eticket' => null,
        'weight' => null,
    ];

    protected $rules = [
        'form.eticket' => 'required|string|max:255',
        'form.branchId' => 'required|integer',
        'form.colorId' => 'required|integer',
        'form.count' => 'required|integer|min:1',
        'form.construction_wages' => 'required|numeric|min:0',
        'form.weight' => 'required',
        'form.cutie' => 'required|max:255',
        'form.profit' => 'required|numeric|min:0',
        'form.tax' => 'required|numeric|min:0',
    ];

    protected $messages = [
        'form.cutie.required' => 'فیلد اعیار الزامی است.',
        'form.eticket.required' => 'فیلد کد تیکت الزامی است.',
        'form.profit.required' => 'فیلد سود الزامی است.',
        'form.construction_wages.required' => 'فیلد اجرت ساخت الزامی است.',
        'form.eticket.string' => 'فیلد کد تیکت باید یک رشته باشد.',
        'form.eticket.max' => 'فیلد کد تیکت نباید بیشتر از ۲۵۵ کاراکتر باشد.',
        'form.branchId.required' => 'انتخاب شعبه الزامی است.',
        'form.branchId.integer' => 'شناسه شعبه باید عدد صحیح باشد.',
        'form.branchId.exists' => 'شناسه شعبه معتبر نیست.',
        'form.colorId.required' => 'انتخاب رنگ الزامی است.',
        'form.colorId.integer' => 'شناسه رنگ باید عدد صحیح باشد.',
        'form.colorId.exists' => 'شناسه رنگ معتبر نیست.',
        'form.count.required' => 'فیلد تعداد الزامی است.',
        'form.count.integer' => 'فیلد تعداد باید عدد صحیح باشد.',
        'form.count.min' => 'حداقل مقدار تعداد باید ۱ باشد.',
        'form.construction_wages.numeric' => 'فیلد اجرت ساخت باید عددی باشد.',
        'form.construction_wages.min' => 'حداقل مقدار اجرت ساخت نمی‌تواند کمتر از صفر باشد.',
        'form.weight.required' => 'فیلد وزن الزامی است.',
        'form.cutie.string' => 'فیلد توضیحات باید یک رشته باشد.',
        'form.cutie.max' => 'فیلد توضیحات نباید بیشتر از ۲۵۵ کاراکتر باشد.',
        'form.profit.numeric' => 'فیلد سود باید عددی باشد.',
        'form.profit.min' => 'مقدار سود نمی‌تواند کمتر از صفر باشد.',
        'form.tax.required' => 'درصد مالیات الزامی می باشد.',
    ];

    public function mount()
    {
        $settings = Setting::whereIn('type', [
            'profit',
            'construction_wages',
            'tax',
        ])->pluck('body', 'type')->toArray();

        $this->form['construction_wages'] = $settings['construction_wages'] ?? null;
        $this->form['profit'] = $settings['profit'] ?? null;
        $this->form['tax'] = $settings['tax'] ?? null;
    }

    public function save()
    {

        $this->validate();

        try {

            DB::beginTransaction();

            $productExist = ProductDetail::where('branch_id', $this->form['branchId'])->where('eticket', $this->form['eticket'])->first();
            if (isset($productExist) && $productExist != null) {
                $this->alert('error', 'خطا در ثبت', [
                    'position' => 'center',
                    'timer' => 30000,
                    'toast' => false,
                    'text' => 'محصول با اتیکیت '.$this->form['eticket'].' در شعبه انتخابی موجود می باشد امکان ثبت اتیکت تکراری در شعبه وجود ندارد',
                    'timerProgressBar' => true,
                    'showDenyButton' => true,
                    'onDenied' => '',
                    'denyButtonText' => 'بسیار خب متوجه شدم',
                ]);

                return;
            }

            $productDetail = ProductDetail::create([
                'product_id' => $this->product->id,
                'eticket' => $this->form['eticket'],
                'weight' => $this->form['weight'],
                'cutie' => $this->form['cutie'],
                'profit' => $this->form['profit'],
                'tax' => $this->form['tax'],
                'color_id' => (int) $this->form['colorId'],
                'construction_wages' => $this->form['construction_wages'],
                'branch_id' => (int) $this->form['branchId'],
                'chain_size' => str_replace(',', '', $this->form['chain_size']),
                'stone_price' => str_replace(',', '', $this->form['stone_price']),
                'pluck' => $this->form['pluck'],
                'model' => $this->form['model'],
            ]);

            ProductCount::create([
                'product_id' => $this->product->id,
                'product_detail_id' => $productDetail->id,
                'count' => (int) $this->form['count'],
            ]);

            $cacheKey = "product_price_{$productDetail->id}_true";
            Cache::forget($cacheKey);

            Artisan::call('optimize:clear');

            GetPriceGold18kProductJob::dispatch($productDetail->id);
            DB::commit();

            Artisan::call('optimize:clear');
            $this->form['eticket'] = null;
            $this->form['weight'] = null;

        } catch (\Exception $e) {

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 30000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }

    }

    public function removeItem($productDetailId)
    {

        try {

            DB::beginTransaction();

            ProductCount::where('product_detail_id', unHashId($productDetailId))->delete();
            ProductDetail::whereId(unHashId($productDetailId))->delete();

            Artisan::call('optimize:clear');

            DB::commit();

            $this->alert('success', 'اطلاعات با موفقیت حذف شد', [
                'position' => 'top-start',
            ]);

        } catch (\Exception $e) {

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }
    }

    public function resetFormExcept(array $except = [])
    {
        foreach ($this->form as $key => $value) {
            if (! in_array($key, $except)) {
                $this->form[$key] = null;
            }
        }
    }

    #[On('reload-products')]
    public function reaload() {}

    public function render()
    {
        $products = ProductDetail::with('color');

        $products->where('product_id', $this->product->id);

        if (! empty($this->data['eticket'])) {
            $products->where('eticket', 'like', '%'.$this->data['eticket'].'%');
        }

        if (! empty($this->data['weight'])) {
            $products->where('weight', 'like', '%'.$this->data['weight'].'%');
        }

        // if (! empty($this->data['weight'])) {
        //     $products->where('weight', 'like', '%'.$this->data['weight'].'%');
        // }

        return view('livewire.dashboard.admin.product.details.show-product-details-form', [
            'products' => $products->get(),
        ]);
    }
}
