<?php

namespace App\Livewire\Dashboard\Admin\Product;

use App\Jobs\UpdateAmountProductsJob;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class ProductsUpdateButton extends Component
{
    use LivewireAlert;

    public function productsUpdate()
    {
        UpdateAmountProductsJob::dispatch();
        $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
            'position' => 'top-start',
        ]);
    }

    public function render()
    {
        return view('livewire.dashboard.admin.product.products-update-button');
    }
}
