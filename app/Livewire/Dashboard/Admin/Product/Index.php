<?php

namespace App\Livewire\Dashboard\Admin\Product;

use App\Models\Category;
use App\Models\ProductDetail;
use Livewire\Attributes\Locked;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public array $data = [
        'category_id' => null,
        'title' => null,
        'eticket' => null,
        'inweight' => null,
        'outweight' => null,
        'product' => null,
        'categories' => [],
    ];

    public array $selectedItems = [];

    protected $queryString = [
        'data.product',
    ];

    #[Locked]
    public $image_server_url;

    public function mount()
    {
        $this->image_server_url = env('APP_ASSET_URL');
        $this->data['categories'] = Category::get();
    }

    public function fillter() {}

    public function ClearFillter()
    {
        $this->data['category_id'] = null;
        $this->data['title'] = null;
        $this->data['eticket'] = null;
        $this->data['inweight'] = null;
        $this->data['outweight'] = null;
    }

    public function moveSelectedItems()
    {
        $this->dispatch('move-product-items', $this->selectedItems);
        // dd($this->selectedItems);
    }

    public function render()
    {

        $query = ProductDetail::with('gallery', 'product', 'color', 'product.category', 'productCount', 'branch', 'product.details');

        if ($this->data['product']) {
            $query->where('product_id', $this->data['product']);
        }

        if ($this->data['category_id'] != null) {
            $query->whereHas('product', function ($query) {
                $query->where('category_id', $this->data['category_id']);
            });
        }

        if ($this->data['title'] != null) {

            $query->whereHas('product', function ($query) {
                $query->where('title_fa', 'like', '%'.$this->data['title'].'%');
            });
        }

        if ($this->data['eticket'] != null) {
            $query->whereHas('product', function ($query) {
                $query->where('eticket', $this->data['eticket']);
            });
        }

        if ($this->data['inweight'] != null && $this->data['outweight'] != null) {
            $query->whereRaw("CAST(REPLACE(weight, ',', '') AS DOUBLE) BETWEEN ? AND ?", [$this->data['inweight'], $this->data['outweight']]);
        }

        $products = $query->latest()->paginate(perPage: 100);

        return view('livewire.dashboard.admin.product.index', [
            'details' => $products,
        ]);

        // return view('livewire.dashboard.admin.product.index',[
        //     'details' => ProductDetail::latest()->get()
        // ]);
    }
}
