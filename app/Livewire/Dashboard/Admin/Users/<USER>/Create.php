<?php

namespace App\Livewire\Dashboard\Admin\Users\RolePermission;

use App\Models\Permission;
use App\Models\Role;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
use LivewireUI\Modal\ModalComponent;

class Create extends ModalComponent
{
    use LivewireAlert;

    public $name;

    public $label;

    public $level;

    public $data = [];

    protected $listeners = ['showModal'];

    public function showModal()
    {

        $this->data = [];
    }

    public function resetState() {}

    protected $rules = [
        'name' => 'required|unique:roles',
        'label' => 'required',
    ];

    public function save()
    {

        $this->validate();

        $role = Role::Create([

            'name' => $this->name,

            'label' => $this->label,

            'level' => $this->level,
            
        ]);


        $role->permissions()->sync($this->data);


        $this->alert('success', 'مقام جدید با موفقیت ثبت شد', [
            'position' => 'top-start',
        ]);

        $this->name = '';
        $this->label = '';

        // $this->dispatchBrowserEvent('modalClose', ['modalName' => 'RolesModal']);
    }

    public function render()
    {
        return view('livewire.dashboard.admin.users.role-permission.create', [
            'permissions' => Permission::get(),
        ]);
    }
}
