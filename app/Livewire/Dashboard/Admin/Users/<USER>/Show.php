<?php

namespace App\Livewire\Dashboard\Admin\Users\RolePermission;

use App\Models\Permission;
use App\Models\Role;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use LivewireUI\Modal\ModalComponent;

class Show extends ModalComponent
{
    use LivewireAlert;

    public $roleId;

    public $name;

    public $label;

    public $level;

    public $data = [];

    public function resetState() {}

    protected $listeners = ['loadData', 'showModal', 'confirmed'];

    public function mount()
    {
        $this->data = [];

        $role = Role::findOrFail($this->roleId);
        $this->name = $role->name;
        $this->label = $role->label;
        $this->level = $role->level;

        $this->data = $role->permissions->pluck('id')->toArray();

    }

    public function confirmed()
    {
        Role::whereId($this->roleId)->delete();
        $this->alert('success', 'اطلاعات نقش کاربری با موفقیت حذف شد', [
            'position' => 'top-start',
        ]);
        $this->dispatch('role-reload');
    }

    public function remove($id)
    {
        $this->roleId = $id;
        $this->alert('warning', 'حذف شود؟', [
            'position' => 'center',
            'timer' => 10000,
            'toast' => false,
            'showConfirmButton' => true,
            'cancelButtonText' => 'خیر',
            'confirmButtonText' => 'بله حذف کن!',
            'onConfirmed' => 'confirmed',
            'showCancelButton' => true,
            'onDismissed' => '',
            'text' => 'آیا مایل به حذف نقش کاربری هستید؟ در نظر داشته باشید اگر این نقش به کاربری یا کاربران نسبت داده شده باشد سطوح دسترسیشان حذف خواهد شد و عملا به هیچ یک از آیتم های سایت دسترسی نخواهند داشت',
        ]);
    }

    public function showModal()
    {

        $this->data = [];
    }

    protected $rules = [
        'label' => 'required',
    ];

    protected $messages = [
        'label.required' => 'عنوان فارسی نقش را وارد کنید',
    ];

    public function update()
    {
        // dd($this->data);
        $this->validate();

        $role = Role::findOrFail($this->roleId);

        $role->update([
            'label' => $this->label,
            'level' => $this->level,
        ]);
        $role->permissions()->sync($this->data);

        $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
            'position' => 'top-start',
        ]);

        $this->dispatch('role-reload');

    }

    public function render()
    {
        return view('livewire.dashboard.admin.users.role-permission.show', [
            'permissions' => Permission::get(),
        ]);
    }
}
