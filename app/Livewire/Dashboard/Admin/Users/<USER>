<?php

namespace App\Livewire\Dashboard\Admin\Users;

use App\Models\Role;
use App\Models\User;
use Illuminate\Validation\Rule;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use <PERSON>wireUI\Modal\ModalComponent;

class ShowUser extends ModalComponent
{
    use LivewireAlert;

    public array $data = [
        'fullname' => null,
        'username' => null,
        'email' => null,
        'phone' => null,
        'level' => null,
        'status' => null,
        'userId' => null,
    ];

    public function resetState() {}

    public function mount($userId)
    {
        $this->data['userId'] = $userId;
        $user = User::whereId($userId)->first();

        if ($user) {
            $this->data['fullname'] = $user->fullname;
            $this->data['username'] = $user->username;
            $this->data['phone'] = $user->mobile;
            $this->data['email'] = $user->email;
            $this->data['status'] = $user->status;
            $this->data['level'] = $user->level;
        }
        // dd($user->toArray());
    }

    protected function rules()
    {
        return [
            'data.fullname' => ['required'],
            'data.username' => [
                'required',
                Rule::unique('users', 'username')->ignore($this->data['userId']),
            ],
            'data.phone' => [
                'required',
                Rule::unique('users', 'mobile')->ignore($this->data['userId']),
            ],
            'data.email' => [
                'required',
                'email',
                Rule::unique('users', 'email')->ignore($this->data['userId']),
            ],
            'data.level' => ['required'],
            'data.status' => ['required'],
        ];
    }

    protected function messages()
    {
        return [
            'data.fullname.required' => 'نام و نام خانوادگی کاربر الزامیست.',
            'data.username.required' => 'نام کاربری الزامیست.',
            'data.username.unique' => 'نام کاربری تکراری می باشد.',
            'data.phone.required' => 'شماره تماس الزامیست.',
            'data.phone.unique' => 'این شماره تماس قبلاً ثبت شده است.',
            'data.email.required' => 'آدرس ایمیل الزامیست.',
            'data.email.email' => 'فرمت آدرس ایمیل معتبر نیست.',
            'data.email.unique' => 'این آدرس ایمیل قبلاً ثبت شده است.',
            'data.level.required' => 'نقش و سطح دسترسی الزامیست.',
            'data.status.required' => 'وضعیت اکانت را مشخص کنید.',
        ];
    }

    public function update()
    {
        $this->validate();

        User::whereId($this->data['userId'])->update([
            'fullname' => $this->data['fullname'],
            'username' => $this->data['username'],
            'mobile' => $this->data['phone'],
            'email' => $this->data['email'],
            'level' => $this->data['level'],
            'status' => $this->data['status'],
            'password' => bcrypt($this->data['phone']),
        ]);

        $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
            'position' => 'top-start',
        ]);

    }

    public function render()
    {
        return view('livewire.dashboard.admin.users.show-user', [
            'roles' => Role::get(),
        ]);
    }
}
