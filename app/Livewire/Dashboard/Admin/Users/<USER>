<?php

namespace App\Livewire\Dashboard\Admin\Users;

use Livewire\Component;
use App\Models\User;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class Index extends Component
{
    use WithPagination;

    public $search;

    public function render()
    {

         // دریافت همه کاربران
         $users = User::all();

         

         // تقسیم کاربران به دو مجموعه: آنلاین و آفلاین
         $onlineUsers = $users->filter(function($user) {
             return Cache::has('user-is-online-' . $user->id);
         });
 
         $offlineUsers = $users->filter(function($user) {
             return !Cache::has('user-is-online-' . $user->id);
         });
 
         // ترکیب کاربران آنلاین و آفلاین
         $sortedUsers = $onlineUsers->merge($offlineUsers);
 
         // تبدیل کالکشن به صفحه‌بندی
         $currentPage = LengthAwarePaginator::resolveCurrentPage();
         $perPage = 50;
         $currentPageItems = $sortedUsers->slice(($currentPage - 1) * $perPage, $perPage)->values();
         $paginatedUsers = new LengthAwarePaginator($currentPageItems, $sortedUsers->count(), $perPage);
         
         // تنظیم مسیر برای صفحه‌بندی
         $paginatedUsers->setPath(request()->url());
 
         return view('livewire.dashboard.admin.users.index', [
             'users' => $paginatedUsers
         ]);
        // return view('livewire.dashboard.admin.users.index',[
        //     'users' => User::paginate(20)
        // ]);
    }
}
