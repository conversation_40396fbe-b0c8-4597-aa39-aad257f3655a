<?php

namespace App\Livewire\Dashboard\Admin\Users;

use App\Models\Role;
use App\Models\User;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use LivewireUI\Modal\ModalComponent;

class CreateUser extends ModalComponent
{
    use LivewireAlert;

    public array $data = [
        'fullname' => null,
        'username' => null,
        'email' => null,
        'phone' => null,
        'level' => null,
        'status' => null,
    ];

    public function resetState() {}

    protected $rules = [
        'data.fullname' => 'required',
        'data.username' => 'required|unique:users,username',
        'data.phone' => 'required|unique:users,mobile',
        'data.email' => 'required|email|unique:users,email',
        'data.level' => 'required',
        'data.status' => 'required',
    ];

    protected function messages()
    {
        return [
            'data.fullname.required' => 'نام و نام خانوادگی کاربر الزامیست.',
            'data.username.required' => 'نام کاربری الزامیست.',
            'data.username.unique' => 'نام کاربری تکراری می باشد.',
            'data.phone.required' => 'شماره تماس الزامیست.',
            'data.phone.unique' => 'این شماره تماس قبلاً ثبت شده است.',
            'data.email.required' => 'آدرس ایمیل الزامیست.',
            'data.email.email' => 'فرمت آدرس ایمیل معتبر نیست.',
            'data.email.unique' => 'این آدرس ایمیل قبلاً ثبت شده است.',
            'data.level.required' => 'نقش و سطح دسترسی الزامیست.',
            'data.status.required' => 'وضعیت اکانت را مشخص کنید.',
        ];
    }

    public function save()
    {
        $this->validate();

        User::create([
            'fullname' => $this->data['fullname'],
            'username' => $this->data['username'],
            'mobile' => $this->data['phone'],
            'email' => $this->data['email'],
            'level' => $this->data['level'],
            'status' => $this->data['status'],
            'password' => bcrypt($this->data['phone']),
        ]);

        $this->alert('success', 'کاربر جدید با موفقیت ثبت شد', [
            'position' => 'top-start',
        ]);

        $this->closeModal();

    }

    public function render()
    {
        return view('livewire.dashboard.admin.users.create-user', [
            'roles' => Role::get(),
        ]);
    }
}
