<?php

namespace App\Livewire\Dashboard\Admin\Factor;

use App\Models\ExceptionLog;
use App\Models\FactorItemEmpty;
use App\Models\FactorOrderEmpty;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\On;
use Livewire\Component;
class Item extends Component
{

    public $facorItemId;

    public array $data = [
        'totalCalc' => null,
        'code' => null,
        'title' => null,
        'gold18k' => null,
        'cutie' => null,
        'weight' => null,
        'construction_wages' => null,
        'profit' => null,
        'tax' => null,
        'total' => null,
        'eticket' => null,
        'post' => null,
        'sender' => null,
        'packing' => null,
    ];

    public $keyItem;



    public function setCalc($total)
    {

        $factorItems = FactorItemEmpty::whereId($this->facorItemId)->first();
        if (isset($factorItems)) {
            $factorItems->post = 'yes';
            $factorItems->total = $total;
            $factorItems->save();

            if ($total != null) {
                $this->data['post'] = 'yes';
                $this->data['total'] = $total;
            } else {
                $this->data['post'] = null;
                $this->calcFactorItem();
            }
        }

    }

    public function addItemAmount($description, $amount, $type)
    {
        // dd('s');
        // dd($this->facorItemId);
        $amount = str_replace(',', '', $amount);
        $total = str_replace(',', '', $this->data['total']);
        // $this->post = 'yes';

        $total = (float) $amount + (float) $total;
        $this->data['total'] = formatMoney($total);

        $factorItems = FactorItemEmpty::whereId($this->facorItemId)->first();

        if (isset($factorItems) && $factorItems != null) {

            $factorItems->post = 'yes';

            // dd($factorItems->toArray());
            if ($factorItems->weight == null || $factorItems->weight == 0) {
                $this->data['profit'] = 0;
                $factorItems->profit = 0;
                $factorItems->tax = 0;
                $this->data['tax'] = 0;
                $this->data['sender'] = 0;
                $factorItems->construction_wages = 0;
                $this->data['construction_wages'] = 0;
                $factorItems->weight = 0;
                $this->data['weight'] = 0;
                $factorItems->cutie = 0;
                $this->data['cutie'] = 0;
                $factorItems->gold18k = 0;
                $this->data['gold18k'] = 0;
                $factorItems->code = '-';
                $this->data['code'] = '-';
                $factorItems->eticket = '-';
                $this->data['eticket'] = '-';
                $factorItems->title = $description;
                $this->data['title'] = $description;

            }

            if ($type == 'post') {

                $this->data['sender'] = $amount;
                $factorItems->sender = $amount;
                // $this->data['post'] = 'yes';
                // dd('sender: ', $amount);
            } else {

                $this->data['packing'] = $amount;
                $factorItems->packing = $amount;
                // dd('packing:', $amount);
                // $this->data['post'] = null;
            }

            $factorItems->total = $this->data['total'];

            $factorItems->save();

            // $factorItems = FactorItemEmpty::whereId($this->facorItemId)->first();

        }
        // $this->calcFactorItem();
        // dd($this->data['packing']);
        // $this->saveItemChange();
        // $this->total = formatMoney($this->total);

        // $this->title = $description;
        // $this->post = 'yes';
        // $this->total = $amount;
        // $factorItems = FactorItemEmpty::whereId($this->facorItemId)->first();
        // if(isset($factorItems) && $factorItems != null){
        //     $factorItems->title = $description;
        //     $factorItems->total = $amount;
        //     $factorItems->post = 'yes';
        //     $factorItems->save();
        // }
    }

    public function clearType($type)
    {

        // $this->calcFactorItem();
        $factorItems = FactorItemEmpty::where('id', $this->facorItemId)->first();

        if (isset($factorItems) && $factorItems != null) {
            // $factorItems->total = $this->data['total'];
            if ($factorItems->weight == null || $factorItems->weight == 0) {

                // dd($this->facorItemId);
                $factorItems->code = null;
                $this->data['code'] = null;
                $factorItems->title = null;
                $this->data['title'] = null;
                $factorItems->eticket = null;
                $this->data['eticket'] = null;
                $factorItems->profit = null;
                $this->data['profit'] = null;
                $factorItems->tax = null;
                $this->data['tax'] = null;
                $factorItems->construction_wages = null;
                $this->data['construction_wages'] = null;
                $factorItems->gold18k = null;
                $this->data['gold18k'] = null;
                $factorItems->weight = null;
                $this->data['weight'] = null;
                $factorItems->cutie = null;
                $this->data['cutie'] = null;
            }

            $factorItems->post = null;
            $this->data['post'] = null;

            if ($type == 'post') {
                $this->data['sender'] = null;
                $factorItems->sender = null;
            } else {
                $this->data['packing'] = null;
                $factorItems->packing = null;
            }

            $factorItems->save();
            $this->calcFactorItem();
        }
        // $this->saveItemChange();
        // dd($factorItems->toArray());

    }

    public function saveItem()
    {
        $factorItems = FactorItemEmpty::whereId($this->facorItemId)->first();
        if (isset($factorItems) && $factorItems != null) {

            $factorItems->eticket = $this->data['eticket'];
            $factorItems->code = $this->data['code'];
            $factorItems->title = $this->data['title'];
            $factorItems->gold18k = $this->data['gold18k'];
            $factorItems->cutie = $this->data['cutie'];
            $factorItems->weight = $this->data['weight'];
            $factorItems->construction_wages = $this->data['construction_wages'];
            $factorItems->profit = $this->data['profit'];
            $factorItems->tax = $this->data['tax'];
            $factorItems->total = $this->data['total'];
            $factorItems->post = $this->data['post'];
            $factorItems->sender = $this->data['sender'];
            $factorItems->packing = $this->data['packing'];
            $factorItems->save();
        }
    }

    #[On('gold18k')]
    public function updateGold18k($gold18k)
    {
        $this->data['gold18k'] = $gold18k;
        $this->calcFactorItem();
        $this->saveItemChange();
    }

    #[On('post')]
    public function updatePost($post)
    {
        $this->data['post'] = $post;
        $this->calcFactorItem();
        $this->saveItemChange();
    }

    public function mount()
    {
        $this->data['gold18k'] = $this->data['gold18k'] == null ? getGold18k() : $this->data['gold18k'];
        try {

            DB::beginTransaction();

            FactorOrderEmpty::where('order_id', null)->delete();
            FactorItemEmpty::where('order_id', null)->delete();

            DB::commit();

            $this->alert('success', 'حذف با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

            return redirect('/factor');

        } catch (\Exception $e) {

            DB::Rollback();



        }
        $this->loadData();
    }

    public function loadData()
    {
        $setting = \App\Models\Setting::whereIn('type', ['tax'])
            ->pluck('body', 'type')
            ->toArray();

        // $this->saveItemChange();
        $factorItems = FactorItemEmpty::whereId($this->facorItemId)->first();
        // dd($this->facorItemId);
        if (isset($factorItems) && $factorItems != null) {

            // dd($factorItems->packing);
            $this->data['eticket'] = $factorItems->eticket;
            $this->data['code'] = $factorItems->code;
            $this->data['title'] = $factorItems->title;
            $this->data['gold18k'] = $factorItems->gold18k;
            $this->data['cutie'] = $factorItems->cutie;
            $this->data['weight'] = $factorItems->weight;
            $this->data['construction_wages'] = $factorItems->construction_wages;
            $this->data['profit'] = $factorItems->profit;

            if ($factorItems->tax == null && $this->data['tax'] == null) {
                $this->data['tax'] = floatval($setting['tax']);
            } else {
                $this->data['tax'] = $factorItems->tax != $this->data['tax'] && $this->data['tax'] != null ? $this->data['tax'] : $factorItems->tax;
            }

            // $this->data['tax'] = $factorItems->tax ?? $this->data['tax'];

            $this->data['total'] = $factorItems->total;
            $this->data['post'] = $factorItems->post;
            $this->data['sender'] = $factorItems->sender;
            $this->data['packing'] = $factorItems->packing;

            // $sender = $factorItems->sender != null ? str_replace(',', '', $factorItems->sender) : 0;
            // $packing = $factorItems->packing != null ? str_replace(',', '', $factorItems->packing) : 0;

            // $this->data['total'] = $factorItems->total != null ? $factorItems->total : $sender + $packing;
        }

        // dd($factorItems->toArray());

        // dd($this->data['sender'], $this->data['packing']);
    }

    public function calcFactorItem()
    {

        try {
            $setting = \App\Models\Setting::whereIn('type', ['tax'])
                ->pluck('body', 'type')
                ->toArray();

            if ($this->data['gold18k'] != null && $this->data['weight'] != null && $this->data['construction_wages'] != null && $this->data['profit'] != null && $this->data['cutie'] != null) {

                $gold18K = str_replace(',', '', $this->data['gold18k']);

                $weight = $this->data['weight'];
                $weight = str_replace(['/', '\\'], '.', $this->data['weight']);

                $constructionWagesPercent = $this->data['construction_wages'] / 100;
                $profitPercent = $this->data['profit'] / 100;
                $cutie = $this->data['cutie'];
                // dd($weight, $constructionWagesPercent, $profitPercent, $gold18K);
                // محاسبه اجرت ساخت
                $wage = $weight * $constructionWagesPercent;

                // محاسبه سود
                $profit = ($weight + $wage) * $profitPercent;

                // محاسبه قیمت کل بدون مالیات
                $totalPriceBeforeTax = $weight + $wage + $profit;

                // محاسبه قیمت کل با ضرب در قیمت روز طلا
                $totalPrice = $totalPriceBeforeTax * $gold18K;

                $tax = 0.09;
                $tax = $this->data['tax'] != null ? $this->data['tax'] / 100 : $setting['tax'] / 100;

                // محاسبه مالیات
                // dd(formatMoney($weight * $gold18K));
                $tax = ($totalPrice - ($weight * $gold18K)) * $tax;
                // dd($gold18K, 'weight: ', $weight, 'tax: ', formatMoney($tax));
                $sender = $this->data['sender'] != null ? str_replace(',', '', $this->data['sender']) : 0;
                $packing = $this->data['packing'] != null ? str_replace(',', '', $this->data['packing']) : 0;

                // محاسبه نهایی
                $finalPrice = $totalPrice + $tax + $sender + $packing;

                // dd(formatMoney($finalPrice));

                // نمایش نتیجه
                // $result = formatMoney($finalPrice, 0, '.', ',');
                if ($this->data['post'] != 'yes') {

                    $this->data['total'] = formatMoney($finalPrice);
                }

            } elseif ($this->data['sender'] != null || $this->data['packing'] != null) {

                $sender = str_replace(',', '', $this->data['sender']);
                $packing = str_replace(',', '', $this->data['packing']);

                // dd($sender + $packing);
                $this->data['total'] = $sender + $packing;
                // dd($this->data['total']);

            } else {

                $this->data['total'] = 0;

            }


            $this->saveItemChange();
        } catch (\Throwable $e) {

            ExceptionLog::create([
                'file' => 'item factor order',
                'method' => 'calcFactorItem',
                'line' => "343",
                'description' => $e->getMessage(),
            ]);

        }

    }

    public function saveItemChange()
    {

        try {
            $factorItems = FactorItemEmpty::whereId($this->facorItemId)->first();


            if (isset($factorItems) && $factorItems != null) {

                $factorItems->eticket = $this->data['eticket'];
                $factorItems->code = $this->data['code'];
                $factorItems->title = $this->data['title'];
                $factorItems->gold18k = $this->data['gold18k'];
                $factorItems->cutie = $this->data['cutie'];
                $factorItems->weight = str_replace(['/', '\\'], '.', $this->data['weight']);

                $factorItems->construction_wages = $this->data['construction_wages'];
                $factorItems->profit = $this->data['profit'];
                $factorItems->tax = $this->data['tax'];
                $factorItems->post = $this->data['post'];
                $factorItems->total = $this->data['total'];
                $factorItems->sender = $this->data['sender'];
                $factorItems->packing = $this->data['packing'];
                $factorItems->user_id = auth()->user()->id;
                $factorItems->save();

            }


            $this->dispatch('change-total-factor');
        } catch (\Throwable $e) {


            ExceptionLog::create([
                'file' => 'item factor order',
                'method' => 'calcFactorItem',
                'line' => "343",
                'description' => $e->getMessage(),
            ]);

        }
    }

    public function updatedDataEticket()
    {
        $this->calcFactorItem();
    }

    public function updatedDataCode()
    {
        $this->calcFactorItem();
    }

    public function updatedDataTitle()
    {
        $this->calcFactorItem();
    }

    public function updatedDataCutie()
    {
        $this->calcFactorItem();
    }

    public function updatedDataWeight()
    {
        $this->calcFactorItem();
    }

    public function updatedDataConstructionWages()
    {
        $this->calcFactorItem();
    }

    public function updatedDataProfit()
    {
        $this->calcFactorItem();
    }

    public function updatedDataTotal()
    {
        $this->calcFactorItem();
    }


    public function render()
    {
        $this->loadData();
        $this->calcFactorItem();

        return view('livewire.dashboard.admin.factor.item');
    }
}
