<?php

namespace App\Livewire\Dashboard\Admin\Factor;

use App\Models\FactorItem;
use App\Models\FactorItemEmpty;
use App\Models\FactorOrder;
use App\Models\FactorOrderEmpty;
use App\Models\Financial;
use App\Models\Sefaresh;
use App\Models\StatusHistory;
use App\Models\Subscribe;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;
use Exception;

class Sum extends Component
{
    use LivewireAlert;

    public $order;
    public $files = [];

    public $total;

    public $totalFactor;

    public $deposit;

    public $discount;

    public $countWeight = 0;

    public $post = null;

    public $post_type;

    public $status = 'yes';

    public $userRefer;

    public $sex = 'طلا';

    public $category;

    public $last_status;

    public $users;

    public $orderId;
    public $description;

    public function confirmRemoveImage($key)
    {
        $this->alert('warning', 'آیا مطمئن هستید که می‌خواهید حذف کنید؟', [
            'position' => 'center',
            'toast' => false,
            'showConfirmButton' => true,
            'confirmButtonText' => 'بله',
            'showCancelButton' => true,
            'cancelButtonText' => 'خیر',
            'onConfirmed' => 'removeImage',
            'data' => ['key' => $key],
            'customClass' => [
                'htmlContainer' => 'text-base',  // کلاس سفارشی برای متن
            ],
        ]);

    }
    protected $listeners = [
        'removeImage',
    ];
    public function removeImage($data)
    {
        $key = $data['key'];

        if (isset($this->files[$key])) {
            unset($this->files[$key]);
            $this->files = array_values($this->files);
        }

        $this->alert('success', 'تصویر با موفقیت حذف شد.');
    }

    #[On('upload-product-image')]
    public function addImage($image)
    {
        array_push($this->files, $image);
    }

    #[On('change-total-factor')]
    public function loadFactor()
    {
        $this->calcFactor();
    }

    public function mount()
    {
        $this->calcFactor();

        $this->users = \App\Models\User::where('status', '1')->get();

    }

    public function saveFileFromTemporaryUrl($temporaryUrl, $directory = 'product-gallery')
    {
        try {
            // دریافت محتویات فایل
            $fileContents = file_get_contents($temporaryUrl);

            if (!$fileContents) {
                throw new Exception("Failed to retrieve file contents from URL: $temporaryUrl");
            }

            // تولید نام منحصربه‌فرد برای فایل
            $fileName = uniqid() . '.' . pathinfo(parse_url($temporaryUrl, PHP_URL_PATH), PATHINFO_EXTENSION);

            // مسیر ذخیره فایل
            $storagePath = storage_path("app/public/{$directory}/");

            // اطمینان از وجود پوشه
            if (!is_dir($storagePath)) {
                mkdir($storagePath, 0755, true);
            }

            // ذخیره فایل
            file_put_contents($storagePath . $fileName, $fileContents);

            // بازگرداندن آدرس فایل ذخیره‌شده
            return "storage/{$directory}/{$fileName}";
        } catch (Exception $e) {
            // مدیریت خطا - لاگ کردن خطا
            Log::error('خطا در ذخیره فایل: ' . $e->getMessage());
            return null;
        }
    }



    public function saveFactor()
    {
        // بررسی وجود کاربر احراز هویت شده
        if (!Auth::check()) {
            $this->alert('error', 'کاربر احراز هویت نشده است');
            return;
        }

        $user = $this->userRefer == null ? Auth::user()->id : $this->userRefer;

        $this->createOrder($user);
    }

    private function createOrder($user)
    {

        $factor = FactorOrderEmpty::where('order_id', null)->latest()->first();
        $factorItem = FactorItemEmpty::where('order_id', null)->latest()->first();
        $subscribe = Subscribe::where('mobile', $factor->phone)->latest()->first();

        // dd($factor, $subscribe);

        $v = verta();
        $code = '';

        $c = Sefaresh::where('month', '=', $v->month)->where('year', '=', $v->year)->count();
        if (isset($c)) {
            $c += 1;
            $this->sex == 'طلا' ? $code = 'G' . substr($v->year, 3) . $v->month . '/' . $c : $code = 'S' . substr($v->year, 3) . $v->month . '/' . $c;
        }

        try {

            DB::beginTransaction();
            $order = Sefaresh::create(
                [
                    'code' => $code,
                    'chat_id' => Str::uuid(),
                    'user_id' => $user,
                    'type' => $this->category,
                    'type_construction' => 'سفارشی',
                    'model' => $factorItem->title,
                    'sex' => $this->sex,
                    'color' => '-',
                    'font' => '-',
                    'name_pluck' => '-',
                    'post_type' => $this->post_type,
                    'tracking_code' => '-',
                    'tips' => '-',
                    'total_amount' => str_replace(',', '', $this->total),
                    'deposit1' => str_replace(',', '', $this->deposit),
                    'deposit2' => '0',
                    'remaining' => str_replace(',', '', $this->totalFactor),
                    'sefaresh_total' => str_replace(',', '', $this->total),
                    'package_type' => '-',
                    'package_amount' => '-',

                    'fullname' => $factor->fullname,
                    'phone' => $factor->phone,
                    'address' => isset($subscribe->address) && $subscribe->address != null ? $subscribe->address : '',
                    'codepost' => isset($subscribe->codepost) && $subscribe->codepost != null ? $subscribe->codepost : '',

                    'chain' => '-',
                    'size' => '-',
                    'size_wrist' => '-',
                    'size_ankle' => '-',

                    'order_register_date' => $factor->factor_create_date,
                    'customer_date' => $factor->factor_create_date,
                    'last_status' => $this->last_status,
                    'date_last_status' => '-',

                    'whatsapp' => '',
                    'phone_whatsapp' => '0',
                    'product_code' => '0',

                    'designer_id' => '',
                    'manufacturer_id' => '',
                    'dimensions' => '',

                    'year' => $v->year,
                    'month' => $v->month,
                    'day' => $v->day,

                    'gram' => $factor->countWeight,
                    'gold18k' => $factorItem->gold18k,

                ]
            );

            // ایجاد رکورد مالی
            Financial::updateOrCreate(
                ['sefaresh_id' => $order->id],
                [
                    'user_id' => $user,
                    'sefaresh_id' => $order->id,
                ]
            );

            // ایجاد تاریخچه وضعیت
            StatusHistory::Create([
                'user_id' => $user,
                'sefaresh_id' => $order->id,
                'last_status' => $factor->factor_create_date,
                'date_last_status' => $this->last_status,
            ]);

            $this->orderId = $order->id;
            DB::commit();
            $this->createFactor($user);

            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

        } catch (Exception $e) {
            DB::Rollback();

            // لاگ کردن خطا
            Log::error('خطا در ثبت سفارش: ' . $e->getMessage());

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'ثبت سفارش با خطا مواجه شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }
    }

    private function createFactor($user)
    {
        try {

            DB::beginTransaction();

            $factor = FactorOrderEmpty::where('order_id', null)->latest()->first();
            $factor->order_id = $this->orderId;
            $factor->save();
            $factorCreate = FactorOrder::create([
                'user_id' => Auth::user()->id,
                'order_id' => $this->orderId,
                'subscribe_id' => $factor->subscribe_id,
                'fullname' => $factor->fullname,
                'subscribe_code' => $factor->subscribe_code,
                'phone' => $factor->phone,
                'factor_total' => $factor->factor_total,
                'total' => $factor->total,
                'countWeight' => $factor->countWeight,
                'post' => $factor->post,
                'user_attach' => $user,
                'factor_deposit' => $factor->factor_deposit,
                'factor_discount' => $factor->factor_discount,
                'factor_number' => $factor->factor_number,
                'factor_create_date' => $factor->factor_create_date,
                'fullname_r' => $factor->fullname_r,
                'phone_r' => $factor->phone_r,
                'gift' => $factor->gift,
                'description' => $factor->description,
            ]);

            $factorItems = FactorItemEmpty::where('order_id', null)->get();
            foreach ($factorItems as $item) {

                $item->order_id = $this->orderId;
                $item->save();

                FactorItem::create([
                    'user_id' => Auth::user()->id,
                    'factor_id' => $factorCreate->id,
                    'order_id' => $this->orderId,
                    'code' => $item->code,
                    'eticket' => $item->eticket,
                    'title' => $item->title,
                    'gold18k' => $item->gold18k,
                    'cutie' => $item->cutie,
                    'weight' => $item->weight,
                    'construction_wages' => $item->construction_wages,
                    'profit' => $item->profit,
                    'total' => $item->total,
                ]);
                // $item->delete();
            }

            // $factor->delete();

            DB::commit();

            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

            if ($this->orderId != null) {
                return redirect()->route('admin-dashboard-show-order', $this->orderId);
            }

            return redirect()->route('admin-dashboard-factors');

        } catch (Exception $e) {
            DB::Rollback();

            // لاگ کردن خطا
            Log::error('خطا در ثبت فاکتور: ' . $e->getMessage());

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'ثبت فاکتور با خطا مواجه شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }

    }


    private function calcFactor()
    {
        $this->loadSumFactorEmpty();

        $this->dispatch('change-factor');
    }

    private function loadSumFactorEmpty()
    {
        $factor = FactorOrderEmpty::where('order_id', null)->latest()->first();

        if (isset($factor) && $factor != null) {
            // محاسبه مجموع کل و وزن یکبار
            $total = FactorItemEmpty::where('order_id', null)->sum(DB::raw('REPLACE(total, ",", "")'));
            $countWeight = FactorItemEmpty::where('order_id', null)->sum(DB::raw('REPLACE(weight, ",", "")'));

            $factor->factor_total = number_format($total, 0, '.', ',');

            $factor_total = $total;

            // تبدیل مقادیر به عدد برای محاسبه
            $discount = $factor->factor_discount != null ? (float) str_replace(',', '', $factor->factor_discount) : 0;
            $deposit = $factor->factor_deposit != null ? (float) str_replace(',', '', $factor->factor_deposit) : 0;
            $totalFactor = $factor_total - $deposit - $discount;
            $factor->total = number_format($totalFactor, 0, '.', ',');

            $factor->countWeight = $countWeight;
            $factor->save();

            $this->total = $factor->factor_total;

            // اطمینان از اینکه مقادیر عددی هستند قبل از number_format
            $this->deposit = $factor->factor_deposit != null ? number_format((float) str_replace(',', '', $factor->factor_deposit)) : 0;
            $this->discount = $factor->factor_discount != null ? number_format((float) str_replace(',', '', $factor->factor_discount)) : 0;

            $this->totalFactor = $factor->total;
            $this->countWeight = $factor->countWeight;
            $this->description = $factor->description;
        }
    }


    public function updatedDeposit()
    {
        $deposit = str_replace(',', '', $this->deposit);

        $factor = FactorOrderEmpty::where('order_id', null)->latest()->first();
        $factor->factor_deposit = $deposit != null ? $deposit : '0';
        $factor->description = $this->description;
        $factor->save();

        $this->calcFactor();
    }

    public function updatedDiscount()
    {
        $discount = str_replace(',', '', $this->discount);

        $factor = FactorOrderEmpty::where('order_id', null)->latest()->first();
        $factor->factor_discount = $discount != null ? $discount : '0';
        $factor->save();

        $this->calcFactor();
    }

    public function updatedDescription()
    {

        $factor = FactorOrderEmpty::where('order_id', null)->latest()->first();
        $factor->description = $this->description;
        $factor->save();

        $this->calcFactor();
    }

    public function render()
    {
        return view('livewire.dashboard.admin.factor.sum');
    }
}
