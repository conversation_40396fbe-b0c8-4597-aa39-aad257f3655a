<?php

namespace App\Livewire\Dashboard\Admin\Order\Factor;

use App\Models\FactorItemEmpty;
use App\Models\FactorOrderEmpty;
use App\Models\Financial;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\On;
use Livewire\Component;

class Details extends Component
{
    public $order;

    public $total = 0;

    public $commission = 0;

    public $countWeight = 0;

    public $sender = 0;

    public $packing = 0;

    public $image_server_url;
    public $image_server_url_1;
    public $image_server_url_2;
    public $image_server_url_3 = "https://shop.tidamode.ir/";

    // public function mount(){
    //     $this->total = formatMoney($this->order->total_amount);
    //     $this->countWeight = isset($this->order->factors->countWeight) && $this->order->factors->countWeight != null ? $this->order->factors->countWeight : 0;
    // }

    public function mount()
    {

        $this->image_server_url_1 = env('APP_TIDAMODE_SHOP_IMAGE_URL');
        $this->image_server_url_2 = env('APP_ASSET_URL');

        // $this->image1 = $this->order->image1;
        // $this->image3 = $this->order->image3;
        // $image_server_url_1 = env('APP_TIDAMODE_SHOP_IMAGE_URL');
        // $image_server_url_2 = env('APP_ASSET_URL');

        // // تعیین مسیر تصویر اول در سرور اول
        // $image1_path_1 = $image_server_url_1 . $this->order->image1;

        // // بررسی وجود تصویر اول در مسیر اول
        // $headers = @get_headers($image1_path_1);
        // if ($headers && strpos($headers[0], '200')) {
        //     // تصویر در مسیر اول وجود دارد، استفاده از مسیر اول برای تمامی تصاویر
        //     $this->image_server_url = $image_server_url_1;
        // } else {
        //     // تصویر در مسیر اول وجود ندارد، استفاده از مسیر دوم برای تمامی تصاویر
        //     $this->image_server_url = $image_server_url_2;
        // }

        // $headers = @get_headers($this->image_server_url . $this->order->image1);
        // if ($headers && strpos($headers[0], '200')) {
        //     // تنظیم مسیر نهایی برای تصاویر
        //     $this->image1 = $this->order->image1 != null ? $this->image_server_url . $this->order->image1 : '';
        // } else {
        //     // تنظیم مسیر نهایی برای تصاویر
        //     $this->image1 = $this->order->image1 != null ? 'https://shop.tidamode.ir/' . $this->order->image1 : '';
        // }

    }

    #[On('change-factor')]
    public function loadFactor()
    {
        $this->calcFactor();
    }

    private function calcFactor()
    {

        // try {
        //     $factor = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();
        //     if ($factor) {
        //         $weight = FactorItemEmpty::where('order_id', $this->order->id)
        //             ->whereNotNull('weight')
        //             ->sum('weight');

        //         $this->commission = calcCommission($weight, $factor->order->gold18k);
        //         $this->total = $weight * (float) str_replace(',', '', $factor->order->gold18k) * 7 / 10;

        // Financial::updateOrCreate(
        //     ['sefaresh_id' => $this->order->id],
        //     [
        //         'user_id' => auth()->user()->id,
        //         'sefaresh_id' => $this->order->id,
        //         'commission' => (float) str_replace(',', '', $this->commission),
        //         'profit' => (float) str_replace(',', '', $this->total),
        //     ]
        // );

        //         $this->total = formatMoney($this->total);
        //         $this->commission = formatMoney($this->commission);

        //     }
        // } catch (\Exception $e) {
        //     // $this->alert('error', 'خطا در ثبت', [
        //     //     'position' => 'center',
        //     //     'timer' => 3000,
        //     //     'toast' => false,
        //     //     'text' => 'محاسبه هوشمند مالی برای این سفارش با مشکل مواجه شده. دقت بفرمایید اطلاعات فاکتور این سفارش به درستی پر شده باشند',
        //     //     'timerProgressBar' => true,
        //     //     'showDenyButton' => true,
        //     //     'onDenied' => '',
        //     //     'denyButtonText' => 'بسیار خب متوجه شدم',
        //     // ]);

        // }

        $factor = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();

        $settings = \App\Models\Setting::whereIn('type', [
            'gold18k',
            'gold18kup',
            'gold18k_status',
            'tax',
            'construction_wages',
        ])->pluck('body', 'type')->toArray();

        if ($factor) {

            if (isset($factor?->factorItem?->weight) && isset($factor->order->gold18k) && isset($factor?->factorItem?->profit)) {

                $factorItems = FactorItemEmpty::where('factor_id', $factor->id)->get();

                $commissionCalc = 0;
                $totalCalc = 0;

                foreach ($factorItems as $item) {

                    if ($item->tax != null && $item?->weight != null && $item?->weight != 0) {
                        $tax = $item->tax ?? ($settings['tax'] ?? 0);

                        $profit = $item->profit ?? ($settings['profit'] ?? 0);

                        $countWeight = $item?->weight;

                        $gold18k = floatval(str_replace(',', '', $factor->order->gold18k ?? $settings['gold18k']));

                        $profitPercent = floatval(preg_replace('/[^0-9.]/', '', $profit ?? 0));
                        $taxPercent = floatval(preg_replace('/[^0-9.]/', '', $tax ?? 0));

                        $countWeight = floatval($countWeight ?? 0);

                        if ($taxPercent == 0) {
                            $taxPercent = 10;
                            // بسته به منطق شما: یا خطا پرتاب کنید یا مقدار پیش‌فرض بگذارید
                            // throw new \RuntimeException('Tax percent cannot be zero.');
                        }

                        if ($countWeight <= 0.8) {
                            $profit = 3_000_000;
                        } elseif ($countWeight <= 1.2) {
                            $profit = 4_000_000;
                        } else {

                            $profit = $countWeight * $gold18k * $profitPercent / $taxPercent / 10;

                        }


                        $totalCalc += $profit;
                        $commissionCalc += $profit != null || $profit > 0 ? calcCommission($countWeight, $gold18k) : 0;
                    }


                }



                Financial::updateOrCreate(
                    ['sefaresh_id' => $this->order->id],
                    [
                        'user_id' => auth()->user()->id,
                        'sefaresh_id' => $this->order->id,
                        'commission' => (float) str_replace(',', '', $commissionCalc),
                        'profit' => (float) str_replace(',', '', $totalCalc),
                    ]
                );

                $this->total = formatMoney($totalCalc);
                $this->commission = formatMoney($commissionCalc);
            }

        }

    }

    public function render()
    {
        $factor = [];
        if ($this->order->id) {
            $factor = FactorItemEmpty::where('order_id', $this->order->id)->get();
        }
        return view('livewire.dashboard.admin.order.factor.details', [
            'factors' => $factor,
        ]);
    }
}
