<?php

namespace App\Livewire\Dashboard\Admin\Order\Factor;

use Livewire\Component;
use App\Models\FinancialTransaction as FTransaction;
use App\Models\Sefaresh;
use Illuminate\Support\Facades\DB;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
class FinancialTransaction extends Component
{
    public $orderId;
    use LivewireAlert;

    public $data = [
        'total',
        'financialId'
    ];

    public function rules()
    {
        return [
            'data.total' => 'required',
            'data.financialId' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.total.required' => 'مبلغ را وارد کنید',
            'data.financialId.required' => 'صندوق را مشخص کنید',
        ];
    }

    public function save()
    {
        $this->validate();

        DB::beginTransaction();
        try {

            $order = Sefaresh::whereId($this->orderId)->first();
            FTransaction::create([
                'user_id' => auth()->user()->id,
                'order_id' => $this->orderId,
                'financial_fund_id' => $this->data['financialId'],
                'primary' => false,
                'type' => 'deposit',
                'description' => 'ثبت واریزی برای سفارش : ' . $order->code . ' توسط کاربر ' . auth()->user()->fullname . ' ثبت شد',
                'amount' => (float) str_replace(',', '', $this->data['total']),
            ]);


            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);


            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->alert('error', 'خطا در اعتبارسنجی', [
                'position' => 'center',
                'timer' => 10000,
                'toast' => false,
                'html' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }


    }

    public function removeItem($itemId)
    {
        FTransaction::whereId($itemId)->delete();
        $this->alert('success', 'حذف با موفقیت انجام شد', [
            'position' => 'top-start',
        ]);
    }


    public function render()
    {
        return view('livewire.dashboard.admin.order.factor.financial-transaction', [
            'transactions' => FTransaction::where('order_id', $this->orderId)->with('fund')->paginate(20)
        ]);
    }
}
