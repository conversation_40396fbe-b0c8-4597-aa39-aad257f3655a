<?php

namespace App\Livewire\Dashboard\Admin\Order;

use Livewire\Component;

class UploadFile extends Component
{
    public $border, $title, $image;
    public $button = true;

    public $image_server_url;
    public $image_server_url_1;
    public $image_server_url_2;
    public $image_server_url_3 = "https://shop.tidamode.ir/";

    public function mount()
    {

        $this->image_server_url_1 = env('APP_TIDAMODE_SHOP_IMAGE_URL');
        $this->image_server_url_2 = env('APP_ASSET_URL');
    }
    public function render()
    {
        return view('livewire.dashboard.admin.order.upload-file');
    }
}
