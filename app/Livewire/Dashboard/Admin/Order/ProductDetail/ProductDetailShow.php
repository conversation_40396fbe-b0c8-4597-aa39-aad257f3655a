<?php

namespace App\Livewire\Dashboard\Admin\Order\ProductDetail;

use App\Models\Invoice;
use App\Models\InvoiceProductDetail;
use Illuminate\Support\Facades\DB;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;

class ProductDetailShow extends Component
{
    use LivewireAlert;

    public $form = [
        'productId' => null,
        'invoiceId' => null,
        'eticket' => null,
        'branchId' => null,
        'colorId' => null,
        'count' => null,
        'construction_wages' => null,
        'weight' => null,
        'cutie' => 18,
        'profit' => null,
        'tax' => '',
        'stone_price' => 0,
        'chain_size' => '',
        'pluck' => '',
        'model' => '',
        'gold18k' => '',
        'totalFactor' => '',
        'post' => '',
        'discount' => '',
        'totalPayment' => '',
    ];

    #[On('show-invoice-product')]
    public function loadFactor($productId, $invoiceId)
    {
        $this->form['productId'] = $productId;
        $this->form['invoiceId'] = $invoiceId;

        $invoice = Invoice::whereId($invoiceId)->firstOrFail();
        $product = InvoiceProductDetail::whereId($productId)->first();

        if ($invoice) {
            $this->form['totalFactor'] = formatMoney($invoice->total_factor);
            $this->form['post'] = formatMoney($invoice->money_post);
            $this->form['discount'] = formatMoney($invoice->discount);
            $this->form['totalPayment'] = formatMoney($invoice->total_payment);
        }

        if ($product) {
            $gold18k = $product->product?->parent?->gold18k ?? 0;

            $this->form['branchId'] = $product->branch_id;
            $this->form['colorId'] = $product->color_id;
            // $this->form['count'] = $product->count;
            $this->form['eticket'] = $product->eticket;
            $this->form['construction_wages'] = $product->construction_wages;
            $this->form['weight'] = $product->weight;
            $this->form['cutie'] = $product->cutie;
            $this->form['profit'] = $product->profit;
            $this->form['tax'] = $product->tax;
            $this->form['stone_price'] = $product->stone_price;
            $this->form['chain_size'] = $product->chain_size;
            $this->form['pluck'] = $product->pluck;
            $this->form['model'] = $product->model;
            $this->form['gold18k'] = formatMoney($gold18k);
        }
    }

    public function save()
    {
        try {
            DB::beginTransaction();

            InvoiceProductDetail::whereId($this->form['productId'])->update([
                'branch_id' => $this->form['branchId'],
                'color_id' => $this->form['colorId'],
                // 'gold18k' => $this->form['gold18k'],
                // 'count' => $this->form['count'],
                'eticket' => $this->form['eticket'],
                'construction_wages' => $this->form['construction_wages'],
                'weight' => $this->form['weight'],
                'cutie' => $this->form['cutie'],
                'profit' => $this->form['profit'],
                'tax' => $this->form['tax'],
                'stone_price' => $this->form['stone_price'],
                'chain_size' => $this->form['chain_size'],
                'pluck' => $this->form['pluck'],
                'model' => $this->form['model'],
            ]);

            Invoice::whereId($this->form['invoiceId'])->update([
                'gold18k' => (float) str_replace(',', '', $this->form['gold18k']),
                'total_factor' => (float) str_replace(',', '', $this->form['totalFactor']),
                'money_post' => (float) str_replace(',', '', $this->form['post']),
                'discount' => (float) str_replace(',', '', $this->form['discount']),
                'total_payment' => (float) str_replace(',', '', $this->form['totalPayment']),
            ]);

            DB::commit();

            $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
                'position' => 'top-start',
            ]);
        } catch (\Exception $e) {
            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'سیستم با خطا مواجه شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }

    }

    public function render()
    {
        return view('livewire.dashboard.admin.order.product-detail.product-detail-show');
    }
}
