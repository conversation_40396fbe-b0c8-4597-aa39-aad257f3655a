<?php

namespace App\Livewire\Dashboard\Admin\Order;

use App\Models\ExceptionLog;
use App\Models\FactorOrderEmpty;
use App\Models\Financial;
use App\Models\Invoice;
use App\Models\InvoiceProductDetail;
use App\Models\ProductDetail;
use App\Models\Resiver;
use App\Models\Sefaresh;
use App\Models\StatusHistory;
use App\Models\StatusSms;
use App\Models\Subscribe;
use App\Models\Tracking;
use App\Service\Payamak\SmsService;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;
use Mockery\Expectation;

class Show extends Component
{
    use LivewireAlert;
    use WithFileUploads;

    public $order;

    public array $data = [
        'category' => null,
        'type' => null,
        'type_construction' => null,
        'negative' => null,
        'model' => null,
        'sex' => null,
        'color' => null,
        'post_type' => null,
        'total_amount' => null,
        'package_type' => null,
        'fullname' => null,
        'phone' => null,
        'address' => null,
        'codepost' => null,
        'last_status' => null,
        'date_last_status' => null,
        'order_register_date' => null,
        'rCodePost' => null,
        'whatsapp' => null,
        'deposit1' => null,
        'deposit2' => null,
        'package_amount' => null,
        'name_pluck' => null,
        'chain' => null,
        'size' => null,
        'size_wrist' => null,
        'size_ankle' => null,
        'designer_id' => null,
        'manufacturer_id' => null,
        'dimensions' => null,
        'font' => null,
        'tracking_code' => null,
        'customer_date' => null,
        'image1' => null,
        'image2' => null,
        'image3' => null,
        'image4' => null,
        'image5' => null,
        'image6' => null,
        'image7' => null,
        'total' => null,
        'remaining' => null,
        'gram' => null,
        'gold18k' => null,
        'chResiver' => false,
        'r_fullname' => null,
        'r_codepost' => null,
        'sefaresh_total' => null,
        'phone_whatsapp' => null,
        'product_code' => null,
        'fullname_agent' => null,
        'phone_agent' => null,
    ];

    public $settings;

    public $total;

    public $countWeight;

    public $sender;

    public $packing;

    public $commission;

    #[On('captured')]
    public function captured($image, $imageItem)
    {
        // dd($image, $imageItem);
        if ($image) {
            $this->data['image' . $imageItem] = $this->uploadBase64($image);
        }
    }

    public $image_server_url_1;
    public $image_server_url_2;
    public $image_server_url_3 = "https://shop.tidamode.ir/";

    public function mount()
    {

        $this->data['type_construction'] = $this->order->type_construction;
        $this->data['rCodePost'] = $this->order->codepost;
        $this->data['model'] = $this->order->model;
        $this->data['sex'] = $this->order->sex;
        $this->data['color'] = $this->order->color;
        $this->data['font'] = $this->order->font;
        $this->data['name_pluck'] = $this->order->name_pluck;
        $this->data['post_type'] = $this->order->post_type;
        $this->data['tracking_code'] = $this->order->tracking_code;
        $this->data['tips'] = $this->order->tips;
        $this->data['total_amount'] = formatMoney($this->order->total_amount);
        $this->data['deposit1'] = formatMoney($this->order->deposit1);
        $this->data['deposit2'] = formatMoney($this->order->deposit2);
        $this->data['remaining'] = formatMoney($this->order->remaining);
        $this->data['sefaresh_total'] = $this->order->sefaresh_total;
        $this->data['package_type'] = $this->order->package_type;
        $this->data['package_amount'] = $this->order->package_amount;
        $this->data['fullname'] = $this->order->fullname;
        $this->data['phone'] = $this->order->phone;
        $this->data['address'] = $this->order->address;
        $this->data['codepost'] = $this->order->codepost;
        $this->data['chain'] = $this->order->chain;
        $this->data['size'] = $this->order->size;
        $this->data['size_wrist'] = $this->order->size_wrist;
        $this->data['size_ankle'] = $this->order->size_ankle;
        $this->data['order_register_date'] = $this->order->order_register_date;
        $this->data['customer_date'] = $this->order->customer_date;
        $this->data['last_status'] = $this->order->last_status;
        $this->data['date_last_status'] = $this->order->date_last_status;
        $this->data['whatsapp'] = $this->order->whatsapp;
        $this->data['phone_whatsapp'] = $this->order->phone_whatsapp;
        $this->data['product_code'] = $this->order->product_code;
        $this->data['designer_id'] = $this->order->designer_id;
        $this->data['manufacturer_id'] = $this->order->manufacturer_id;
        $this->data['dimensions'] = $this->order->dimensions;
        $this->data['gram'] = $this->order->gram;
        $this->data['gold18k'] = formatMoney($this->order->gold18k);

        $this->data['fullname_agent'] = $this->order->fullname_agent;
        $this->data['phone_agent'] = $this->order->phone_agent;

        $this->image_server_url_1 = env('APP_TIDAMODE_SHOP_IMAGE_URL');
        $this->image_server_url_2 = env('APP_ASSET_URL');

        for ($i = 1; $i <= 7; $i++) {
            $image = $this->order->{'image' . $i};
            $this->data['image' . $i] = $image ? ltrim($image, '/\\') : null;
        }


        // $this->data['image1'] = $this->getValidImageUrl($this->order->image1, $image_server_url_1, $image_server_url_2);
        // $this->data['image2'] = $this->getValidImageUrl($this->order->image2, $image_server_url_1, $image_server_url_2);
        // $this->data['image3'] = $this->getValidImageUrl($this->order->image3, $image_server_url_1, $image_server_url_2);
        // $this->data['image4'] = $this->getValidImageUrl($this->order->image4, $image_server_url_1, $image_server_url_2);
        // $this->data['image5'] = $this->getValidImageUrl($this->order->image5, $image_server_url_1, $image_server_url_2);
        // $this->data['image6'] = $this->getValidImageUrl($this->order->image6, $image_server_url_1, $image_server_url_2);
        // $this->data['image7'] = $this->getValidImageUrl($this->order->image7, $image_server_url_1, $image_server_url_2);

        $this->data['r_fullname'] = isset($this->order->resiver) ? $this->order->resiver->fullname : '';
        $this->data['r_phone'] = isset($this->order->resiver) ? $this->order->resiver->mobile : '';
        $this->data['r_codepost'] = isset($this->order->resiver) ? $this->order->resiver->codepost : '';
        $this->data['r_address'] = isset($this->order->resiver) ? $this->order->resiver->address : '';
        $this->data['chResiver'] = isset($this->order->resiver) ? true : false;

        $this->calc();

        $this->calcFactor();
    }


    public function rules()
    {
        return [
            // 'data.type' => 'required',
            // 'data.gram' => 'required',
            'data.type_construction' => 'required',
            'data.model' => 'required',
            'data.sex' => 'required',
            'data.color' => 'required',
            'data.post_type' => 'required',
            'data.total_amount' => 'required',
            'data.package_type' => 'required',
            'data.fullname' => 'required',
            'data.phone' => 'required|min:11',
            'data.address' => 'required',
            'data.codepost' => 'required',
            'data.last_status' => 'required',
            'data.date_last_status' => 'required',
            'data.order_register_date' => 'required',
            'data.whatsapp' => 'required',
            // 'image1' => 'required|max:1536|mimes:jpg,png,jpeg',
            // 'image3' => 'required|max:1536|mimes:jpg,png,jpeg',
            // 'image6' => 'required|max:1536|mimes:jpg,png,jpeg',
        ];
    }

    public function messages()
    {
        return [
            // 'data.type.required' => 'پر کردن فیلد الزامیست',
            // 'data.gram.required' => 'پر کردن فیلد الزامیست',
            // 'gram.numeric' => 'مقدار وارد شده برای فیلد گرم باید یک عدد باشد.',
            // 'gram.regex' => 'مقدار وارد شده برای فیلد گرم باید یک عدد اعشاری معتبر باشد.',
            'data.type_construction.required' => 'پر کردن فیلد الزامیست',
            'data.model.required' => 'پر کردن فیلد الزامیست',
            'data.sex.required' => 'پر کردن فیلد الزامیست',
            'data.color.required' => 'پر کردن فیلد الزامیست',
            'data.post_type.required' => 'پر کردن فیلد الزامیست',
            'data.total_amount.required' => 'پر کردن فیلد الزامیست',
            'data.package_type.required' => 'پر کردن فیلد الزامیست',
            'data.fullname.required' => 'پر کردن فیلد الزامیست',
            'data.phone.required' => 'پر کردن فیلد الزامیست',
            'data.address.required' => 'پر کردن فیلد الزامیست',
            'data.codepost.required' => 'پر کردن فیلد الزامیست',
            'data.last_status.required' => 'پر کردن فیلد الزامیست',
            'data.date_last_status.required' => 'پر کردن فیلد الزامیست',
            'data.order_register_date.required' => 'پر کردن فیلد الزامیست',
            'data.whatsapp.required' => 'پر کردن فیلد الزامیست',
            // 'image1.required' => 'بارگزاری فایل الزامیست',
            // 'image3.required' => 'بارگزاری فایل الزامیست',
            // 'image6.required' => 'بارگزاری فایل الزامیست',
        ];
    }


    private function uploadBase64($image)
    {
        try {
            // پردازش تصویر base64
            $base64Image = str_replace('data:image/png;base64,', '', $image); // توجه: $image باید آرایه باشد
            $base64Image = str_replace(' ', '+', $base64Image);
            $imageBinary = base64_decode($base64Image);

            // ایجاد نام فایل
            $fileName = 'webcam_' . time() . '.png';

            // مسیر کامل ذخیره‌سازی (با نام فایل)
            $directory = storage_path('app/public/uploads/webcam');
            $fullPath = $directory . '/' . $fileName;

            // ایجاد دایرکتوری اگر وجود ندارد
            // if (! file_exists($directory)) {
            //     mkdir($directory, 0755, true);
            // }

            // ذخیره فایل
            file_put_contents($fullPath, $imageBinary);

            // مسیر نسبی برای دیتابیس
            $relativePath = 'uploads/webcam/' . $fileName;

            return '/storage/' . $relativePath;
        } catch (\Exception $e) {

            // dd($e->getMessage());

        }
    }





    public function calc()
    {

        try {
            if ($this->data['deposit1'] != null || $this->data['deposit2'] != null) {
                $deposit1 = $this->data['deposit1'] != null ? str_replace(',', '', $this->data['deposit1']) : 0;
                $deposit2 = $this->data['deposit2'] != null ? str_replace(',', '', $this->data['deposit2']) : 0;
                $total = ((float) $deposit1 + (float) $deposit2);
                $this->data['total'] = number_format($total);
            }

            if ($this->data['total_amount'] != null) {
                if ($this->data['deposit1'] != null || $this->data['deposit2'] != null) {
                    $total = str_replace(',', '', $this->data['total_amount']);
                    $deposit1 = $this->data['deposit1'] != null ? str_replace(',', '', $this->data['deposit1']) : 0;
                    $deposit2 = $this->data['deposit2'] != null ? str_replace(',', '', $this->data['deposit2']) : 0;

                    $remaining = (float) $total - ((float) $deposit1 + (float) $deposit2);
                    $this->data['remaining'] = number_format($remaining);
                } else {
                    $this->data['remaining'] = $this->data['total_amount'];
                }
            }

            $this->data['deposit1'] = formatMoney($this->data['deposit1']);
            $this->data['deposit2'] = formatMoney($this->data['deposit2']);
            $this->data['remaining'] = formatMoney($this->data['remaining']);

            $deposit1 = str_replace(',', '', $this->data['deposit1']);
            $deposit2 = str_replace(',', '', $this->data['deposit2']);
            $this->data['total_amount'] = formatMoney($deposit1 + $deposit2);


            Sefaresh::whereId($this->order->id)->update(
                [
                    'deposit1' => $deposit1,
                    'deposit2' => $deposit2,
                    'remaining' => $remaining,
                    'total_amount' => $deposit1 + $deposit2,
                ]
            );
            $factor = FactorOrderEmpty::where('order_id', $this->order->id)->latest()->first();
            if ($factor) {
                $factor->factor_deposit = $deposit1 + $deposit2;
                $factor->save();
            }


        } catch (\Throwable $e) {

            ExceptionLog::create([
                'file' => 'show order',
                'method' => 'calc',
                'line' => "338",
                'description' => $e->getMessage(),
            ]);

            $this->alert('error', 'خطا در محاسبه مبلغ', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }

        // $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
        //     'position' => 'top-start',
        // ]);
    }

    public function store(Request $req)
    {
        $rCodePost = str_replace('-', '', $this->data['codepost']);

        try {

            DB::beginTransaction();

            $total_amount = str_replace(',', '', $this->data['total_amount']);
            $deposit1 = str_replace(',', '', $this->data['deposit1']);
            $deposit2 = str_replace(',', '', $this->data['deposit2']);
            $package_amount = str_replace(',', '', $this->data['package_amount']);

            $total_amount = $total_amount == '' ? 0 : $total_amount;

            $deposit1 = $deposit1 == '' ? 0 : $deposit1;

            $deposit2 = $deposit2 == '' ? 0 : $deposit2;

            $package_amount = $package_amount == '' ? 0 : $package_amount;

            $total = (float) $total_amount + (float) $package_amount;

            $negative = (float) $total - (float) $deposit1 - (float) $deposit2;

            Sefaresh::whereId($this->order->id)->update(
                [
                    'type_construction' => $this->data['type_construction'],
                    'model' => $this->data['model'],
                    'sex' => $this->data['sex'],
                    'color' => $this->data['color'],
                    'font' => $this->data['font'],
                    'name_pluck' => $this->data['name_pluck'],
                    'post_type' => $this->data['post_type'],
                    'tracking_code' => $this->data['tracking_code'],
                    'tips' => $this->data['tips'],
                    'total_amount' => str_replace(',', '', $total_amount),
                    'deposit1' => str_replace(',', '', $deposit1),
                    'deposit2' => str_replace(',', '', $deposit2),
                    'remaining' => str_replace(',', '', $negative),
                    'sefaresh_total' => str_replace(',', '', $total_amount),
                    'package_type' => $this->data['package_type'],
                    'package_amount' => $package_amount,

                    'fullname' => $this->data['fullname'],
                    'phone' => $this->data['phone'],
                    'address' => $this->data['address'],
                    'codepost' => $this->data['codepost'],

                    'chain' => $this->data['chain'],
                    'size' => $this->data['size'],
                    'size_wrist' => $this->data['size_wrist'],
                    'size_ankle' => $this->data['size_ankle'],

                    'order_register_date' => $this->data['order_register_date'],
                    'customer_date' => $this->data['customer_date'],
                    'last_status' => $this->data['last_status'],
                    'date_last_status' => $this->data['date_last_status'],

                    'whatsapp' => $this->data['whatsapp'],
                    'phone_whatsapp' => '0',
                    'product_code' => '0',

                    'designer_id' => $this->data['designer_id'],
                    'manufacturer_id' => $this->data['manufacturer_id'],
                    'dimensions' => $this->data['dimensions'],

                    'image1' => $this->data['image1'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                        ? saveFileFromTemporaryImage($this->data['image1'])
                        : $this->replaceUrls($this->data['image1']),

                    'image2' => $this->data['image2'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                        ? saveFileFromTemporaryImage($this->data['image2'])
                        : $this->replaceUrls($this->data['image2']),

                    'image3' => $this->data['image3'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                        ? saveFileFromTemporaryImage($this->data['image3'])
                        : $this->replaceUrls($this->data['image3']),

                    'image4' => $this->data['image4'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                        ? saveFileFromTemporaryImage($this->data['image4'])
                        : $this->replaceUrls($this->data['image4']),

                    'image5' => $this->data['image5'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                        ? saveFileFromTemporaryImage($this->data['image5'])
                        : $this->replaceUrls($this->data['image5']),

                    'image6' => $this->data['image6'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                        ? saveFileFromTemporaryImage($this->data['image6'])
                        : $this->replaceUrls($this->data['image6']),

                    'image7' => $this->data['image7'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                        ? saveFileFromTemporaryImage($this->data['image7'])
                        : $this->replaceUrls($this->data['image7']),

                    'gram' => $this->data['gram'],
                    'gold18k' => $this->data['gold18k'],

                    'fullname_agent' => $this->data['fullname_agent'],
                    'phone_agent' => $this->data['phone_agent'],

                ]
            );

            $this->checkHistoryOrderStatus();

            $this->cancelOrderWithRelationInvoice();

            $this->sendTrackNumberPost();

            $this->checkOrderGift();

            $this->createSubscriber($this->data);

            DB::commit();

            $this->alert('success', 'بروزرسانی موفق', [
                'position' => 'center',
                'timer' => 30000,
                'toast' => false,
                'text' => 'ثبت اطلاعات با موفقیت بروزرسانی',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        } catch (\Exception $e) {

            DB::Rollback();

            ExceptionLog::create([
                'file' => 'livewire.dashboard.admin.order.show',
                'method' => 'store',
                'line' => '575',
                'description' => $e->getMessage(),
            ]);

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }

    }

    private function createSubscriber($data)
    {
        try {
            $first = Subscribe::whereMobile($data['phone'])->first();
            if (!isset($first)) {
                $latest = Subscribe::latest()->first();
                $code = '';
                if (isset($latest) && $latest->count() > 0) {
                    $code = $latest->code += 1;
                } else {
                    $code = 1;
                }
                $rCodePost = str_replace('-', '', $data['codepost']);
                Subscribe::Create([
                    'code' => $code,
                    'fullname' => $data['fullname'],
                    'codepost' => $rCodePost,
                    'mobile' => $data['phone'],
                    'address' => $data['address'],
                ]);

            }
        } catch (\Exception $e) {
        }

    }

    private function checkHistoryOrderStatus()
    {

        $count = StatusHistory::where('sefaresh_id', $this->order->id)->where('last_status', $this->data['last_status'])->count();
        if ($count == 0) {

            StatusHistory::Create([
                'user_id' => auth()->user()->id,
                'sefaresh_id' => $this->order->id,
                'last_status' => $this->data['last_status'],
                'date_last_status' => $this->data['date_last_status'],
            ]);

            $o = Sefaresh::whereId($this->order->id)->first();
            if ($this->data['last_status'] == 'money') {

                $smsService = new SmsService;
                $smsService->send('tasvie', [
                    'phone' => $this->order->phone,
                    'fullname' => $this->order->fullname,
                    'code' => $this->order->code,
                    'status' => status_last($this->data['last_status']),
                ]);

            } elseif ($this->data['last_status'] == 'send' && $this->order->tracking_code == '') {

                $smsService = new SmsService;
                $smsService->send('order_send', [
                    'phone' => $this->order->phone,
                    'fullname' => $this->order->fullname,
                    'code' => $this->order->code,
                    'status' => status_last($this->data['last_status']),
                ]);

            } else {

                $smsService = new SmsService;
                $smsService->send('change_order_status', [
                    'phone' => $this->order->phone,
                    'fullname' => $this->order->fullname,
                    'code' => $this->order->code,
                    'status' => status_last($this->data['last_status']),
                ]);

            }

            StatusSms::create([
                'user_id' => auth()->user()->id,
                'code' => $o->code,
                'phone' => $this->data['phone'],
                'fullname' => $this->data['fullname'],
                'status' => $this->data['last_status'],
            ]);

        }
    }

    private function cancelOrderWithRelationInvoice()
    {
        if ($this->order->invoice_id != null && $this->data['last_status'] == 'cancel') {
            $invoice = Invoice::where('id', $this->order->invoice_id)->first();
            if ($this->data['last_status'] == 'cancel' && isset($invoice) && $invoice != null && $invoice->status != 'reject') {

                Invoice::where('id', $this->order->invoice_id)->update([
                    'status' => 'reject',
                ]);

                $invoice = Invoice::whereId($this->order->invoice_id)->first();
                $invoiceDetails = InvoiceProductDetail::where('invoice_id', $invoice->id)->get();

                foreach ($invoiceDetails as $item) {

                    ProductDetail::where('product_id', $item->product->product->id)->where('eticket', $item->eticket)->update([
                        'out_stock' => null,
                        'reserved_count' => null,
                    ]);
                }
                Artisan::call('optimize:clear');
            }

        }

        if ($this->data['last_status'] == 'cancel') {
            Financial::updateOrCreate(
                ['sefaresh_id' => $this->order->id],
                [
                    'commission' => 0,
                    'profit' => 0,
                ]
            );
        }

    }

    private function sendTrackNumberPost()
    {
        if (isset($this->order->tracking_code)) {
            if (auth()->user()->level == 'admin') {
                $tracking = Tracking::Where('tracking_code', $this->order->tracking_code)->count();
                if ($tracking == 0) {

                    $t = new Tracking;
                    $t->user_id = auth()->user()->id;
                    $t->sefaresh_id = $this->order->id;
                    $t->fullname = $this->order->fullname;
                    $t->phone = $this->order->phone;
                    $t->tracking_code = $this->order->tracking_code;
                    $t->status = 1;
                    $t->save();

                    if ($this->order->post_type == 'تیباکس') {

                        $smsService = new SmsService;
                        $smsService->send('tracking_post', [
                            'phone' => $this->order->phone,
                            'code' => $this->order->tracking_code,
                        ]);

                    } elseif ($this->order->post_type == 'پست' || $this->order->post_type == 'پست ویژه') {

                        $smsService = new SmsService;
                        $smsService->send('tracking_tipax', [
                            'phone' => $this->order->phone,
                            'code' => $this->order->tracking_code,
                        ]);
                    }

                }
            }
        }
    }

    private function checkOrderGift()
    {
        if ($this->data['chResiver'] != null) {
            $resiver = Resiver::updateOrCreate(
                ['sefaresh_id' => $this->order->id],
                [
                    'user_id' => auth()->user()->id,
                    'sefaresh_id' => $this->order->id,
                    'fullname' => $this->data['r_fullname'],
                    'codepost' => $this->data['r_codepost'],
                    'mobile' => $this->data['phone'],
                    'address' => $this->data['r_address'],
                ]
            );
        } else {
            $r = Resiver::where('sefaresh_id', $this->order->id)->first();
            if (isset($r)) {
                $r->delete();
            }
        }
    }

    public function replaceUrls($url)
    {
        $domains = [
            'https://tidamode.iran.liara.run/',
            'https://tidamode.iran.liara.run/',
            'https://shop.tidamode.ir/',
        ];

        foreach ($domains as $domain) {
            if (strpos($url, $domain) === 0) { // اگر ابتدای رشته با این دامین شروع شد
                return substr($url, strlen($domain)); // دامین رو حذف کن
            }
        }

        return $url; // اگر هیچکدوم نبود، همون url اولیه رو برگردون
    }

    public function uploadDataImage($image, $folder)
    {
        if ($image) {
            $file = $image;
            $filename = Str::random(40) . '.' . $file->getClientOriginalExtension();
            $file->move('Receipt//' . $folder, $filename);

            return $ImagePatch1 = 'Receipt//' . $folder . '//' . $filename;
        }
    }

    public function updatedDataImage1()
    {
    }

    public function updatedDataTotalAmount()
    {
        $this->calc();
    }

    public function updatedDataDeposit1()
    {
        $this->calc();
    }

    public function updatedDataDeposit2()
    {
        $this->calc();
    }

    public function remove($item)
    {
        if (array_key_exists($item, $this->data)) {
            $this->data[$item] = null;
        }
    }

    private function calcFactor()
    {
    }

    #[On('set-user')]
    public function setSubscriber($userId)
    {
        $user = Subscribe::whereId($userId)->first();
        if (isset($user) && $user != null) {
            $this->data['fullname'] = $user->fullname;
            $this->data['phone'] = $user->mobile;
            $this->data['code'] = $user->code;
            $this->data['address'] = $user->address;
            $this->data['codepost'] = $user->codepost;
        }
    }

    public function render()
    {
        $users = Cache::remember('active_users', 3600, function () {
            return \App\Models\User::where('status', '1')->get();
        });

        return view('livewire.dashboard.admin.order.show', [
            'users' => $users,
        ]);
    }
}
