<?php

namespace App\Livewire\Dashboard\Admin\Order;

use App\Models\Creator;
use App\Models\FactorItemEmpty;
use App\Models\FactorOrderEmpty;
use App\Models\Financial;
use App\Models\Resiver;
use App\Models\Sefaresh;
use App\Models\StatusHistory;
use App\Models\Subscribe;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Jantinnerezo\LivewireAlert\LivewireAlert;

use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;

class Create extends Component
{
    use LivewireAlert;
    use WithFileUploads;

    public $settings;

    public $category;



    public array $data = [

        'type' => null,
        'type_construction' => null,
        'model' => null,
        'sex' => null,
        'color' => null,
        'post_type' => null,
        'total_amount' => null,
        'package_type' => null,
        'fullname' => null,
        'phone' => null,
        'address' => null,
        'codepost' => null,
        'last_status' => null,
        'date_last_status' => null,
        'order_register_date' => null,
        'whatsapp' => 'حضوری الماس',
        'deposit1' => null,
        'deposit2' => null,
        'package_amount' => null,
        'name_pluck' => null,
        'chain' => null,
        'size' => null,
        'size_wrist' => null,
        'size_ankle' => null,
        'designer_id' => null,
        'manufacturer_id' => null,
        'dimensions' => null,
        'font' => null,
        'tracking_code' => null,
        'tips' => null,
        'customer_date' => null,
        'image1' => null,
        'image2' => null,
        'image3' => null,
        'image4' => null,
        'image5' => null,
        'image6' => null,
        'image7' => null,
        'total' => 0,
        'remaining' => 0,
        'gram' => null,
        'gold18k' => 0,
        'chResiver' => false,
        'r_fullname' => null,
        'r_codepost' => null,
        'r_phone' => null,
        'r_address' => null,
        'fullname_agent' => null,
        'phone_agent' => null,
    ];

    public function rules()
    {
        return [
            // 'data.type' => 'required',
            // 'gram' => 'required',
            // 'data.type_construction' => 'required',
            'data.model' => 'required',
            'data.sex' => 'required',
            'data.color' => 'required',
            'data.post_type' => 'required',
            // 'data.total_amount' => 'required',
            // 'data.package_type' => 'required',
            'data.fullname' => 'required',
            'data.phone' => 'required|min:11',
            'data.address' => 'required',
            'data.codepost' => 'required',
            'data.last_status' => 'required',
            'data.date_last_status' => 'required',
            'data.order_register_date' => 'required',
            'data.whatsapp' => 'required',
            'data.image1' => 'required|max:1536',
            // 'data.image2' => 'required|max:1536',
            // 'data.image3' => 'required|max:1536',
            // 'data.image4' => 'required|max:1536',
            // 'data.image5' => 'required|max:1536',
            // 'data.image6' => 'required|max:1536',
            // 'data.image3' => 'required|max:1536|mimes:jpg,png,jpeg',
            // 'data.image6' => 'required|max:1536|mimes:jpg,png,jpeg',
        ];
    }

    public function messages()
    {
        return [
            // 'data.type.required' => 'پر کردن فیلد نوع سفارش الزامیست',
            // 'data.gram.required' => 'پر کردن فیلد گرم الزامیست',
            // 'gram.numeric' => 'مقدار وارد شده برای فیلد گرم باید یک عدد باشد.',
            // 'gram.regex' => 'مقدار وارد شده برای فیلد گرم باید یک عدد اعشاری معتبر باشد.',
            'data.type_construction.required' => 'پر کردن فیلد نوع ساخت الزامیست',
            'data.model.required' => 'پر کردن فیلد مدل محصول الزامیست',
            'data.sex.required' => 'پر کردن فیلد جنسیت الزامیست',
            'data.color.required' => 'پر کردن فیلد رنگ الزامیست',
            'data.post_type.required' => 'پر کردن فیلد نوع پست الزامیست',
            'data.total_amount.required' => 'پر کردن فیلد مبلغ کل الزامیست',
            'data.package_type.required' => 'پر کردن فیلد نوع بسته‌بندی الزامیست',
            'data.fullname.required' => 'پر کردن فیلد نام و نام خانوادگی الزامیست',
            'data.phone.required' => 'پر کردن فیلد شماره تماس الزامیست',
            'data.address.required' => 'پر کردن فیلد آدرس الزامیست',
            'data.codepost.required' => 'پر کردن فیلد کد پستی الزامیست',
            'data.last_status.required' => 'پر کردن فیلد آخرین وضعیت سفارش الزامیست',
            'data.date_last_status.required' => 'پر کردن فیلد تاریخ آخرین وضعیت الزامیست',
            'data.order_register_date.required' => 'پر کردن فیلد تاریخ ثبت سفارش الزامیست',
            'data.whatsapp.required' => 'پر کردن فیلد شماره واتساپ الزامیست',
            'data.image1.required' => 'بارگذاری رسید اول یا بیعانه اول الزامیست',
            'data.image3.required' => 'بارگذاری تصویر نمونه کار الزامیست',
            'data.image6.required' => 'بارگذاری تصویر طرح انتخابی مشتری الزامیست',
        ];

    }

    #[On('captured')]
    public function captured($image, $imageItem)
    {
        // dd($image, $imageItem);
        if ($image) {
            $this->data['image' . $imageItem] = $this->uploadBase64($image);
        }
    }




    public function updatedDataImage1()
    {
    }

    public function remove($item)
    {
        if (array_key_exists($item, $this->data)) {
            $this->data[$item] = null;
        }
    }



    private function uploadBase64($image)
    {
        try {
            // پردازش تصویر base64
            $base64Image = str_replace('data:image/png;base64,', '', $image); // توجه: $image باید آرایه باشد
            $base64Image = str_replace(' ', '+', $base64Image);
            $imageBinary = base64_decode($base64Image);

            // ایجاد نام فایل
            $fileName = 'webcam_' . time() . '.png';

            // مسیر کامل ذخیره‌سازی (با نام فایل)
            $directory = storage_path('app/public/uploads/webcam');
            $fullPath = $directory . '/' . $fileName;

            // ایجاد دایرکتوری اگر وجود ندارد
            // if (! file_exists($directory)) {
            //     mkdir($directory, 0755, true);
            // }

            // ذخیره فایل
            file_put_contents($fullPath, $imageBinary);

            // مسیر نسبی برای دیتابیس
            $relativePath = 'uploads/webcam/' . $fileName;

            return '/storage/' . $relativePath;
        } catch (\Exception $e) {

            dd($e->getMessage());

        }
    }

    public function store(Request $req)
    {

        // try {

        $this->validate();

        $rCodePost = str_replace('-', '', $this->data['codepost']);

        // $rPhone = fixPhoneNumber($this->data['phone']);
        // if (! preg_match('/^09[0-9]{9}$/', $rPhone)) {
        //     return response()->json(['error' => ['شماره موبایل سفارش دهنده را بصورت صحیح وارد نمایید']]);
        // }

        $v = verta();
        $code = '';

        $c = Sefaresh::where('month', '=', $v->month)->where('year', '=', $v->year)->count();
        if (isset($c)) {
            $c += 1;
            $this->data['sex'] == 'طلا' ? $code = 'G' . substr($v->year, 3) . $v->month . '/' . $c : $code = 'S' . substr($v->year, 3) . $v->month . '/' . $c;
        }

        $total_amount = str_replace(',', '', $this->data['total_amount']);
        $deposit1 = str_replace(',', '', $this->data['deposit1']);
        $deposit2 = str_replace(',', '', $this->data['deposit2']);
        $package_amount = str_replace(',', '', $this->data['package_amount']);

        $total_amount = $total_amount == '' ? '0' : $total_amount;
        $deposit1 = $deposit1 == '' ? '0' : $deposit1;
        $deposit2 = $deposit2 == '' ? '0' : $deposit2;
        $package_amount = $package_amount == '' ? '0' : $package_amount;

        $total = $total_amount + $package_amount;
        $negative = $total - $deposit1 - $deposit2;

        $name_pluck = $this->data['name_pluck'] ?? '-';
        $chain = $this->data['chain'] ?? '-';
        $size = $this->data['size'] ?? '-';
        $size_wrist = $this->data['size_wrist'] ?? '-';
        $size_ankle = $this->data['size_ankle'] ?? '-';
        $model = $this->data['model'] ?? '-';

        // try {

        DB::beginTransaction();
        $order = Sefaresh::updateOrCreate(
            [
                'code' => $code,
            ],
            [
                'chat_id' => Str::uuid(),
                'user_id' => auth()->user()->id,
                'type' => $this->category,
                'type_construction' => $this->data['type_construction'],
                'model' => $model,
                'sex' => $this->data['sex'],
                'color' => $this->data['color'],
                'font' => $this->data['font'],
                'name_pluck' => $name_pluck,
                'post_type' => $this->data['post_type'],
                'tracking_code' => $this->data['tracking_code'],
                'tips' => $this->data['tips'],
                'total_amount' => $this->data['total'],
                'deposit1' => str_replace(',', '', $this->data['deposit1']),
                'deposit2' => str_replace(',', '', $this->data['deposit2']),
                'remaining' => $negative,
                'sefaresh_total' => str_replace(',', '', $this->data['total']),
                'package_type' => $this->data['package_type'],
                'package_amount' => str_replace(',', '', $package_amount),

                'fullname' => $this->data['fullname'],
                'phone' => $this->data['phone'],
                'address' => $this->data['address'],
                'codepost' => $this->data['codepost'],

                'chain' => $chain,
                'size' => $size,
                'size_wrist' => $size_wrist,
                'size_ankle' => $size_ankle,

                'order_register_date' => $this->data['order_register_date'],
                'customer_date' => $this->data['customer_date'],
                'last_status' => $this->data['last_status'],
                'date_last_status' => $this->data['date_last_status'],

                'whatsapp' => $this->data['whatsapp'],
                'phone_whatsapp' => '0',
                'product_code' => '0',

                'designer_id' => $this->data['designer_id'],
                'manufacturer_id' => $this->data['manufacturer_id'],
                'dimensions' => $this->data['dimensions'],

                // 'image1' => saveFileFromTemporaryImage($this->data['image1']),
                // 'image2' => saveFileFromTemporaryImage($this->data['image2']),
                // 'image3' => saveFileFromTemporaryImage($this->data['image3']),
                // 'image4' => saveFileFromTemporaryImage($this->data['image4']),
                // 'image5' => saveFileFromTemporaryImage($this->data['image5']),
                // 'image6' => saveFileFromTemporaryImage($this->data['image6']),
                // 'image7' => saveFileFromTemporaryImage($this->data['image7']),
                'image1' => $this->data['image1'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                    ? saveFileFromTemporaryImage($this->data['image1'])
                    : $this->data['image1'],

                'image2' => $this->data['image2'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                    ? saveFileFromTemporaryImage($this->data['image2'])
                    : $this->data['image2'],

                'image3' => $this->data['image3'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                    ? saveFileFromTemporaryImage($this->data['image3'])
                    : $this->data['image3'],

                'image4' => $this->data['image4'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                    ? saveFileFromTemporaryImage($this->data['image4'])
                    : $this->data['image4'],

                'image5' => $this->data['image5'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                    ? saveFileFromTemporaryImage($this->data['image5'])
                    : $this->data['image5'],

                'image6' => $this->data['image6'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                    ? saveFileFromTemporaryImage($this->data['image6'])
                    : $this->data['image6'],

                'image7' => $this->data['image7'] instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile
                    ? saveFileFromTemporaryImage($this->data['image7'])
                    : $this->data['image7'],
                'year' => $v->year,
                'month' => $v->month,
                'day' => $v->day,

                'gram' => $this->data['gram'],
                'gold18k' => $this->data['gold18k'],

                'fullname_agent' => $this->data['fullname_agent'],
                'phone_agent' => $this->data['phone_agent'],

            ]
        );

        $financials = Financial::updateOrCreate(
            ['sefaresh_id' => $order->id],
            [
                'user_id' => auth()->user()->id,
                'sefaresh_id' => $order->id,
            ]
        );

        $historys = StatusHistory::Create([
            'user_id' => auth()->user()->id,
            'sefaresh_id' => $order->id,
            'last_status' => $this->data['last_status'],
            'date_last_status' => $this->data['date_last_status'],
        ]);

        if ($this->data['chResiver'] != null) {
            $resiver = Resiver::Create([
                'user_id' => auth()->user()->id,
                'sefaresh_id' => $order->id,
                'fullname' => $this->data['r_fullname'],
                'codepost' => $this->data['r_codepost'],
                'mobile' => $this->data['r_phone'],
                'address' => $this->data['r_address'],
            ]);
        }

        $this->createSubscriber($this->data);

        if ($this->data['last_status'] == 'wait_factory' && $this->data['manufacturer_id'] != '') {
            Creator::updateOrCreate(['sefaresh_id' => $order->id], [
                'user_id' => auth()->user()->id,
                'creator_id' => $this->data['manufacturer_id'],
                'last_status' => $this->data['last_status'],
            ]);
        }

        $deposit1_factor = str_replace(',', '', $this->data['deposit1']);
        $deposit2_factor = str_replace(',', '', $this->data['deposit2']);
        $deposit_factor = (float) $deposit1_factor + (float) $deposit2_factor;
        $factorLatestId = FactorOrderEmpty::where('order_id', null)->latest()->first();
        $factor = FactorOrderEmpty::create([
            'user_id' => auth()->user()->id,
            'order_id' => $order->id,
            'fullname' => $this->data['fullname'],
            'phone' => $this->data['phone'],
            'factor_number' => isset($factorLatestId) && $factorLatestId != null ? $factorLatestId->factor_number : 1,
            'factor_create_date' => $this->data['order_register_date'],
            'subscribe_code' => null,
            'factor_total' => str_replace(',', '', $this->data['total']),
            'factor_deposit' => $deposit_factor,
            'factor_discount' => 0,
            'total' => str_replace(',', '', $this->data['total']),
            'countWeight' => $this->data['gram'] ?? 0,
            'post' => null,
            'gold18kup' => $this->data['gold18k'],
        ]);

        for ($i = 1; $i <= 5; $i++) {
            FactorItemEmpty::create([
                'user_id' => auth()->user()->id,
                'order_id' => $order->id,
                'factor_id' => $factor->id,
                'subscribe_id' => null,
                'code' => null,
                'eticket' => null,
                'title' => null,
                'gold18k' => null,
                'cutie' => null,
                'weight' => null,
                'construction_wages' => null,
                'profit' => null,
                'tax' => null,
                'total' => null,
            ]);
        }

        DB::commit();

        $this->alert('success', 'ثبت موفق', [
            'position' => 'center',
            'timer' => 30000,
            'toast' => false,
            'text' => 'ثبت اطلاعات با موفقیت انجام شد',
            'timerProgressBar' => true,
            'showDenyButton' => true,
            'onDenied' => '',
            'denyButtonText' => 'بسیار خب متوجه شدم',
        ]);

        return redirect()->route('admin-dashboard-show-order', ['orderId' => $order->id]);

        // } catch (\Exception $e) {
        //     DB::Rollback();

        //     $this->alert('error', 'خطا در ثبت', [
        //         'position' => 'center',
        //         'timer' => 3000,
        //         'toast' => false,
        //         'text' => 'سیستم با خطا مواجه شد',
        //         'timerProgressBar' => true,
        //         'showDenyButton' => true,
        //         'onDenied' => '',
        //         'denyButtonText' => 'بسیار خب متوجه شدم',
        //     ]);

        // }
        // } catch (\Illuminate\Validation\ValidationException $e) {
        //     // جمع‌آوری خطاها
        //     $errors = collect($e->validator->errors()->all())->implode('<br>');

        //     // نمایش پیام خطاها در توست
        //     $this->alert('error', 'خطا در اعتبارسنجی', [
        //         'position' => 'center',
        //         'timer' => 3000,
        //         'toast' => false,
        //         'html' => $errors, // لیست خطاها
        //         'timerProgressBar' => true,
        //         'showDenyButton' => true,
        //         'denyButtonText' => 'بسیار خب متوجه شدم',
        //     ]);
        // }

    }

    private function createSubscriber($data)
    {
        try {
            $first = Subscribe::whereMobile($data['phone'])->first();
            if (!isset($first)) {
                $latest = Subscribe::latest()->first();
                $code = '';
                if (isset($latest) && $latest->count() > 0) {
                    $code = $latest->code += 1;
                } else {
                    $code = 1;
                }
                $rCodePost = str_replace('-', '', $data['codepost']);
                Subscribe::Create([
                    'code' => $code,
                    'fullname' => $data['fullname'],
                    'codepost' => $rCodePost,
                    'mobile' => $data['phone'],
                    'address' => $data['address'],
                ]);

            }
        } catch (\Exception $e) {
        }

    }

    // public function uploadImage($image, $folder){
    //     if($image) {
    //         $file = $image;
    //         $filename = Str::random(40).'.'.$file->getClientOriginalExtension();
    //         $file->move('Receipt//'. $folder, $filename);
    //         return $ImagePatch1 = 'Receipt//'. $folder . '//' . $filename;
    //     }
    // }

    // public function updatedImage1(){

    //     $v = verta();
    //     $folder = 'userid_' . auth()->user()->id . '_' .$v->year . '-' . $v->month . '-' . $v->day;
    //     File::makeDirectory('Receipt\\'.$folder, $mode = 0777, true, truaddresse);
    //     $filename = Str::random(40).'.'.$this->image1->getClientOriginalExtension();
    //     // $this->image1->storeAs(path: 'Receipt', name: $filename);
    //     // $this->image1->storeAs('/', $filename, disk: 'public');
    // }

    public function updatedDataTotalAmount()
    {
        $this->calc();
    }

    public function updatedDataDeposit1()
    {
        $this->calc();
    }

    public function updatedDataDeposit2()
    {
        $this->calc();
    }

    public function calc()
    {

        if ($this->data['deposit1'] != null || $this->data['deposit2'] != null) {
            $deposit1 = $this->data['deposit1'] != null ? str_replace(',', '', $this->data['deposit1']) : 0;
            $deposit2 = $this->data['deposit2'] != null ? str_replace(',', '', $this->data['deposit2']) : 0;
            $total = ((float) $deposit1 + (float) $deposit2);
            $this->data['total'] = number_format($total);
        }

        if ($this->data['total_amount'] != null) {
            if ($this->data['deposit1'] != null || $this->data['deposit2'] != null) {
                $total = str_replace(',', '', $this->data['total_amount']);
                $deposit1 = $this->data['deposit1'] != null ? str_replace(',', '', $this->data['deposit1']) : 0;
                $deposit2 = $this->data['deposit2'] != null ? str_replace(',', '', $this->data['deposit2']) : 0;

                $remaining = (float) $total - ((float) $deposit1 + (float) $deposit2);
                $this->data['remaining'] = number_format($remaining);
            } else {
                $this->data['remaining'] = $this->data['total_amount'];
            }
        }

        $this->data['deposit1'] = formatMoney($this->data['deposit1']);
        $this->data['deposit2'] = formatMoney($this->data['deposit2']);
        $this->data['remaining'] = formatMoney($this->data['remaining']);

        $deposit1 = str_replace(',', '', $this->data['deposit1']);
        $deposit2 = str_replace(',', '', $this->data['deposit2']);
        $this->data['total_amount'] = formatMoney($deposit1 + $deposit2);

        $this->alert('success', 'اطلاعات با موفقیت بروزرسانی شد', [
            'position' => 'top-start',
        ]);
    }

    #[On('set-user')]
    public function setSubscriber($userId)
    {
        $user = Subscribe::whereId($userId)->first();
        if (isset($user) && $user != null) {
            $this->data['fullname'] = $user->fullname;
            $this->data['phone'] = $user->mobile;
            $this->data['code'] = $user->code;
            $this->data['address'] = $user->address;
            $this->data['codepost'] = $user->codepost;
        }
    }

    public function render()
    {
        return view('livewire.dashboard.admin.order.create');
    }
}
