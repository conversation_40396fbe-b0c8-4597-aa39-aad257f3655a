<?php

namespace App\Livewire\Dashboard\Admin\Order\Transactions;

use App\Models\User;
use App\Models\ZinbalTransaction;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public array $data = [
        'refNumber' => '',
        'result' => 100,
        'userId' => '',
        'subscriber_fullname' => '',
        'subscriber_phone' => '',
        'subscriber_cart' => '',
        'eticket' => '',
    ];

    public function fillter() {}

    public function ClearFillter()
    {
        $this->data['refNumber'] = null;
        $this->data['result'] = 100;
        $this->data['userId'] = null;
        $this->data['subscriber_fullname'] = null;
        $this->data['subscriber_phone'] = null;
        $this->data['subscriber_cart'] = null;
    }

    public function render()
    {

        $query = ZinbalTransaction::with('order', 'order.user');

        if (auth()->user()->level != 'admin') {
            $query->where('user_id', auth()->id());
        }

        if ($this->data['userId'] != null) {
            $query->whereHas('order.user', function ($query) {
                $query->where('id', $this->data['userId']);
            });
        }

        if ($this->data['eticket'] != null) {
            $query->whereHas('order.invoice.productDetails', function ($query) {
                $query->where('eticket', $this->data['eticket']);
            });
        }

        if ($this->data['subscriber_fullname'] != null) {
            $query->whereHas('order', function ($query) {
                $query->where('fullname', 'like', '%'.$this->data['fullname'].'%');
            });
        }

        if ($this->data['subscriber_phone'] != null) {
            $query->whereHas('order', function ($query) {
                $query->where('phone', 'like', '%'.$this->data['phone'].'%');
            });
        }

        if ($this->data['subscriber_cart'] != null) {
            $query->where('cardNumber', 'like', '%'.$this->data['subscriber_cart'].'%');
        }

        if ($this->data['refNumber'] != null) {
            $query->where('trackId', 'like', '%'.$this->data['refNumber'].'%');
        }

        $query->where('result', 'like', '%'.$this->data['result'].'%');

        return view('livewire.dashboard.admin.order.transactions.index', [
            'transactions' => $query->latest()->paginate(50),
            'users' => User::get(),
        ]);
    }
}
