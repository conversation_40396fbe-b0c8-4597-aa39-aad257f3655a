<?php

namespace App\Livewire\Dashboard\Admin\Reports\FinancialFund;

use App\Models\FinancialFund;
use App\Models\FinancialTransaction;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use LivewireUI\Modal\ModalComponent;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
class CreateFinancialFund extends ModalComponent
{

    use LivewireAlert;

    public array $data = [
        'title' => null,
        'terminalId' => null,
        'amount' => null,
        'description' => null,
        'status' => 'active'
    ];

    public $FinancialFundId;

    public function resetState()
    {
    }

    public function mount()
    {

        $FinancialFund = FinancialFund::whereId($this->FinancialFundId)->first();
        if (isset($FinancialFund) && $FinancialFund != null) {
            $this->data = [
                'title' => $FinancialFund->title,
                'terminalId' => $FinancialFund->terminal_id,
                'description' => $FinancialFund->description,
                'status' => $FinancialFund?->status,
            ];
        }
    }


    public function rules()
    {
        return [
            'data.title' => 'required',
            // 'data.terminalId' => 'required',
            'data.amount' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.title.required' => 'عنوان درگاه را وارد کنید',
            // 'data.terminalId.required' => 'شناسه ترمینال را وارد کنید',
            'data.amount.required' => 'موجودی اولیه را وارد کنید',
        ];
    }

    public function save()
    {
        $this->validate();

        DB::beginTransaction();
        try {
            $FinancialFund = FinancialFund::create([
                'user_id' => auth()->user()->id,
                'title' => $this->data['title'],
                'terminal_id' => $this->data['terminalId'],
                'description' => $this->data['description'],
                'status' => $this->data['status'],
            ]);

            FinancialTransaction::create([
                'user_id' => auth()->user()->id,
                'financial_fund_id' => $FinancialFund->id,
                'primary' => true,
                'type' => 'deposit',
                'description' => 'شارژ موجودی اولیه در صفحه ایجاد صندوق به مبلغ: ' . $this->data['amount'],
                'amount' => (float) str_replace(',', '', $this->data['amount']),
            ]);

            $this->dispatch('reload-financial-list');

            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

            $this->closeModal();
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->alert('error', 'خطا در اعتبارسنجی', [
                'position' => 'center',
                'timer' => 10000,
                'toast' => false,
                'html' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }


    }

    public function render()
    {
        return view('livewire.dashboard.admin.reports.financial-fund.create-financial-fund');
    }
}
