<?php

namespace App\Livewire\Dashboard\Admin\Reports\FinancialFund;

use App\Models\FinancialFund;
use Livewire\Component;
use LivewireUI\Modal\ModalComponent;
use Jantinnerezo\LivewireAlert\LivewireAlert;
class ShowFinancialFund extends ModalComponent
{
    use LivewireAlert;

    public array $data = [
        'title' => null,
        'terminalId' => null,
        // 'amount' => null,
        'description' => null,
        'status' => 'active'
    ];

    public $FinancialFundId;

    public function resetState()
    {
    }

    public function mount()
    {

        $FinancialFund = FinancialFund::whereId($this->FinancialFundId)->first();
        if (isset($FinancialFund) && $FinancialFund != null) {
            $this->data = [
                'title' => $FinancialFund->title,
                'terminalId' => $FinancialFund->terminal_id,
                'description' => $FinancialFund->description,
                'status' => $FinancialFund?->status,
            ];
        }
    }

    public function rules()
    {
        return [
            'data.title' => 'required',
            // 'data.terminalId' => 'required',
            // 'data.amount' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.title.required' => 'عنوان درگاه را وارد کنید',
            // 'data.terminalId.required' => 'شناسه ترمینال را وارد کنید',
            // 'data.amount.required' => 'موجودی اولیه را وارد کنید',
        ];
    }

    public function update()
    {
        $this->validate();

        FinancialFund::whereId($this->FinancialFundId)->update([
            'title' => $this->data['title'],
            'terminal_id' => $this->data['terminalId'],
            'description' => $this->data['description'],
            'status' => $this->data['status'],
        ]);

        $this->dispatch('reload-financial-list');

        $this->alert('success', 'بروزرسانی اطلاعات با موفقیت انجام شد', [
            'position' => 'top-start',
        ]);

        $this->closeModal();

    }

    public function render()
    {
        return view('livewire.dashboard.admin.reports.financial-fund.show-financial-fund');
    }
}
