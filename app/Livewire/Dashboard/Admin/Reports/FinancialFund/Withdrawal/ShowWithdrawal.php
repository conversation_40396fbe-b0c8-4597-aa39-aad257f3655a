<?php

namespace App\Livewire\Dashboard\Admin\Reports\FinancialFund\Withdrawal;

use Livewire\Component;
use App\Models\FinancialTransaction;
use Illuminate\Support\Facades\DB;
use LivewireUI\Modal\ModalComponent;
use <PERSON>tinnerezo\LivewireAlert\LivewireAlert;
class ShowWithdrawal extends ModalComponent
{
    use LivewireAlert;

    public array $data = [
        'amount' => null,
        'financialId' => null,
        'description' => null,
    ];

    public $transactionId;

    public function resetState()
    {
    }

    public function mount()
    {
        $transaction = FinancialTransaction::whereId($this->transactionId)->first();
        if ($transaction) {
            $this->data['amount'] = formatMoney($transaction->amount);
            $this->data['financialId'] = $transaction->financial_fund_id;
            $this->data['description'] = $transaction->description;
        }

    }


    public function rules()
    {
        return [
            'data.amount' => 'required',
            'data.financialId' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.amount.required' => 'مبلغ برداشت را وارد کنید',
            'data.financialId.required' => 'صندوق برداشت را مشخص کنید',
        ];
    }

    public function update()
    {
        $this->validate();

        DB::beginTransaction();
        try {


            FinancialTransaction::whereId($this->transactionId)->update([
                'financial_fund_id' => $this->data['financialId'],
                'description' => $this->data['description'],
                'amount' => (float) str_replace(',', '', $this->data['amount']),
            ]);

            $this->dispatch('reload-financial-transaction-list');

            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

            $this->closeModal();
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->alert('error', 'خطا در اعتبارسنجی', [
                'position' => 'center',
                'timer' => 10000,
                'toast' => false,
                'html' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }


    }

    public function render()
    {
        return view('livewire.dashboard.admin.reports.financial-fund.withdrawal.show-withdrawal');
    }
}
