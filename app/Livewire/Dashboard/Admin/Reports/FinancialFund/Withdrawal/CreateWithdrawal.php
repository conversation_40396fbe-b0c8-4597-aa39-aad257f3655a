<?php

namespace App\Livewire\Dashboard\Admin\Reports\FinancialFund\Withdrawal;

use Livewire\Component;
use App\Models\FinancialTransaction;
use Illuminate\Support\Facades\DB;
use LivewireUI\Modal\ModalComponent;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
class CreateWithdrawal extends ModalComponent
{
    use LivewireAlert;

    public array $data = [
        'amount' => null,
        'financialId' => null,
        'description' => null,
    ];

    public $FinancialFundId;

    public function resetState()
    {
    }

    public function mount()
    {
        $this->data['financialId'] = $this->FinancialFundId;

    }


    public function rules()
    {
        return [
            'data.amount' => 'required',
            'data.financialId' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'data.amount.required' => 'مبلغ برداشت را وارد کنید',
            'data.financialId.required' => 'صندوق برداشت را مشخص کنید',
        ];
    }

    public function save()
    {
        $this->validate();

        DB::beginTransaction();
        try {


            FinancialTransaction::create([
                'user_id' => auth()->user()->id,
                'financial_fund_id' => $this->data['financialId'],
                'primary' => false,
                'type' => 'withdrawal',
                'description' => $this->data['description'],
                'amount' => (float) str_replace(',', '', $this->data['amount']),
            ]);

            $this->dispatch('reload-financial-transaction-list');

            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

            $this->closeModal();
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->alert('error', 'خطا در اعتبارسنجی', [
                'position' => 'center',
                'timer' => 10000,
                'toast' => false,
                'html' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }


    }

    public function render()
    {
        return view('livewire.dashboard.admin.reports.financial-fund.withdrawal.create-withdrawal');
    }
}
