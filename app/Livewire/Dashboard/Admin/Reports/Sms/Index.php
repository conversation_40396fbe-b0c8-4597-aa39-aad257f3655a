<?php

namespace App\Livewire\Dashboard\Admin\Reports\Sms;

use Livewire\Component;
use App\Models\StatusSms;
use Livewire\WithPagination;
class Index extends Component
{
    use WithPagination;

    public $order_code, $fullname, $phone, $last_status;

    public function fillter(){
        
    }

    public function ClearFillter(){
        $this->order_code = '';
        $this->fullname = '';
        $this->phone = '';
        $this->last_status = '';
    }

    public function render()
    {
        return view('livewire.dashboard.admin.reports.sms.index',[
            'reports' => StatusSms::where('code', 'like', '%'.$this->order_code.'%')->
                where('code', 'like', '%'.$this->order_code.'%')->
                where('fullname', 'like', '%'.$this->fullname.'%')->
                where('phone', 'like', '%'.$this->phone.'%')->
                where('status', 'like', '%'.$this->last_status.'%')->
                latest()->paginate(20)
        ]);
    }
}
