<?php

namespace App\Livewire\Dashboard\Admin\Reports\Survey;

use App\Models\SurveyResponse;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public function render()
    {
        return view('livewire.dashboard.admin.reports.survey.index', [
            'reports' => SurveyResponse::latest()->paginate(perPage: 30),
        ]);
    }
}
