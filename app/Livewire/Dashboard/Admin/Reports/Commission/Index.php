<?php

namespace App\Livewire\Dashboard\Admin\Reports\Commission;

use App\Models\Financial;
use App\Models\Sefaresh;
use Livewire\Component;

class Index extends Component
{
    public $data = [
        'MonthlyOne' => '',
        'MonthlyTow' => '',
        'Yearly' => '',
    ];

    public function mount()
    {
        $date = verta();
        $this->data['MonthlyOne'] = '1';
        $this->data['MonthlyTow'] = '12';
        $this->data['Yearly'] = $date->year;
    }

    public function search()
    {
    }

    public function render()
    {
        $orders = $this->getFilteredOrders();
        $orderIds = $orders->pluck('id');

        $commission = $this->sumFinancialField($orderIds, 'commission');
        $profit = $this->sumFinancialField($orderIds, 'profit');

        $totalAmount = $this->sumNumericField($orders, 'total_amount');
        $deposit1 = $this->sumNumericField($orders, 'deposit1');
        $deposit2 = $this->sumNumericField($orders, 'deposit2');

        $remaining = $totalAmount - ($deposit1 + $deposit2);
        $totalCountOrder = $orders->count();

        return view('livewire.dashboard.admin.reports.commission.index', compact(
            'commission',
            'totalAmount',
            'deposit1',
            'deposit2',
            'remaining',
            'profit',
            'totalCountOrder'
        ));
    }

    protected function getFilteredOrders()
    {
        return Sefaresh::whereBetween('month', [$this->data['MonthlyOne'], $this->data['MonthlyTow']])
            ->where('year', $this->data['Yearly'])
            ->where('last_status', '!=', 'cancel')
            ->get(['id', 'total_amount', 'deposit1', 'deposit2']);
    }

    protected function sumFinancialField($orderIds, $field)
    {
        return Financial::whereIn('sefaresh_id', $orderIds)->sum($field);
    }

    protected function sumNumericField($orders, $field)
    {
        return $orders->sum(function ($order) use ($field) {
            return (int) str_replace(',', '', $order->{$field});
        });
    }
}
