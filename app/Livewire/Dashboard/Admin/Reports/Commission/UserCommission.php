<?php

namespace App\Livewire\Dashboard\Admin\Reports\Commission;

use App\Models\Financial;
use App\Models\Sefaresh;
use App\Models\User;
use Livewire\Component;

class UserCommission extends Component
{
    public $data = [
        'Monthly' => '',
        'Yearly' => '',
        'user_id' => '',
    ];

    public function mount()
    {
        $date = verta();
        $this->data['Monthly'] = $date->month;
        $this->data['Yearly'] = $date->year;
    }

    public function search() {}

    public function render()
    {
        $orders = $this->getFilteredOrders();
        $orderIds = $orders->pluck('id');

        $commission = $this->sumFinancialField($orderIds, 'commission');
        $profit = $this->sumFinancialField($orderIds, 'profit');

        $totalAmount = $this->sumNumericField($orders, 'total_amount');
        $deposit1 = $this->sumNumericField($orders, 'deposit1');
        $deposit2 = $this->sumNumericField($orders, 'deposit2');
        $remaining = $totalAmount - ($deposit1 + $deposit2);
        $totalCountOrder = $orders->count();

        $users = $this->getActiveUsers();

        return view('livewire.dashboard.admin.reports.commission.user-commission', compact(
            'commission', 'totalAmount', 'deposit1', 'deposit2', 'remaining', 'totalCountOrder', 'users', 'profit'
        ));
    }

    protected function getFilteredOrders()
    {
        return Sefaresh::where('user_id', $this->data['user_id'])
            ->where('month', $this->data['Monthly'])
            ->where('year', $this->data['Yearly'])
            ->where('last_status', '!=', 'cancel')
            ->get(['id', 'total_amount', 'deposit1', 'deposit2']);
    }

    protected function sumFinancialField($orderIds, $field)
    {
        return Financial::whereIn('sefaresh_id', $orderIds)->sum($field);
    }

    protected function sumNumericField($orders, $field)
    {
        return $orders->sum(function ($item) use ($field) {
            return (float) str_replace(',', '', $item->{$field} ?? 0);
        });
    }

    protected function getActiveUsers()
    {
        return User::whereIn('level', ['user', 'admin'])
            ->where('status', 1)
            ->get();
    }
}
