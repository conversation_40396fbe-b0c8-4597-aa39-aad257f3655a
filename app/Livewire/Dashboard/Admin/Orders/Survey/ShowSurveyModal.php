<?php

// App\Livewire\Dashboard\Admin\Orders\Survey\ShowSurveyModal.php

namespace App\Livewire\Dashboard\Admin\Orders\Survey;

use App\Models\Sefaresh;
use Livewire\Attributes\On;
use Livewire\Component;

class ShowSurveyModal extends Component
{
    public $order;

    public $responses;

    public function mount()
    {
        if ($this->order) {
            $order = Sefaresh::with('surveyResponses.answerItems.question')->find($this->order->id);

            if ($order) {
                $this->responses = $order->surveyResponses;
            } else {
                $this->responses = collect();
            }
        }
    }

    #[On('set-survey')]
    public function showSurvey($orderId)
    {
        $order = Sefaresh::with('surveyResponses.answerItems.question')->find($orderId);

        if ($order) {
            $this->responses = $order->surveyResponses;
        } else {
            $this->responses = collect();
        }
    }

    public function render()
    {
        return view('livewire.dashboard.admin.orders.survey.show-survey-modal');
    }
}
