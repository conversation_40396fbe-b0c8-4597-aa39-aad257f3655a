<?php

namespace App\Livewire\Dashboard\Admin\Orders\Export;

use Livewire\Component;
use App\Exports\SefareshsExportUser;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\User;
use Livewire\Attributes\Validate;
class ExcelOrdersUser extends Component
{
    public $MonthlyOne, $MonthlyTow, $Yearly, $user;

    public function mount(){
        $v = verta();
		$this->MonthlyOne = $v->month;
		$this->MonthlyTow = $v->month;
		$this->Yearly = $v->year;
    }

    public function rules() 
    {
        return [ 
            'MonthlyOne' => 'required',
            'MonthlyTow' => 'required',
            'Yearly' => 'required',
            'user' => 'required',
        ];
    }
 
    public function messages() 
    {
        return [
            'MonthlyOne.required' => 'فیلد الزامیست',
            'MonthlyTow.required' => 'فیلد الزامیست',
            'Yearly.required' => 'فیلد الزامیست',
            'user.required' => 'کاربر مورد نظر را انتخاب کنید',
        ];
    }

    public function export() 
    {
        $this->validate();

        $user = User::whereId($this->user)->firstorfail();
        $year = faTOen($this->Yearly);
        return Excel::download(new SefareshsExportUser($this->MonthlyOne,$this->MonthlyTow,$this->Yearly,$this->user), $user->username.'-month'.$this->MonthlyOne.'-as-'.$this->MonthlyTow.'-'.$year.'.xlsx');
    }
        public function render()
    {
        return view('livewire.dashboard.admin.orders.export.excel-orders-user',[
            'users' => User::where('status', '1')->get()
        ]);
    }
}
