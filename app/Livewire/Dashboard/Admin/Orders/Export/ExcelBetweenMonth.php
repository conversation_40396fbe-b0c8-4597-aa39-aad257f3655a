<?php

namespace App\Livewire\Dashboard\Admin\Orders\Export;

use Livewire\Component;
use App\Exports\SefareshsExport;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Livewire\Attributes\Validate;
class ExcelBetweenMonth extends Component
{
    public $MonthlyOne, $MonthlyTow, $Yearly;

    public function mount(){
        $v = verta();
		$this->MonthlyOne = $v->month;
		$this->MonthlyTow = $v->month;
		$this->Yearly = $v->year;
    }

    public function rules() 
    {
        return [ 
            'MonthlyOne' => 'required',
            'MonthlyTow' => 'required',
            'Yearly' => 'required',
        ];
    }
 
    public function messages() 
    {
        return [
            'MonthlyOne.required' => 'فیلد الزامیست',
            'MonthlyTow.required' => 'فیلد الزامیست',
            'Yearly.required' => 'فیلد الزامیست',
        ];
    }

    public function export() 
    {
        $this->validate();

        $year = faTOen($this->Yearly);
        return Excel::download(new SefareshsExport($this->MonthlyOne,$this->MonthlyTow,$this->Yearly), 'tidamode-month'.$this->MonthlyOne.'-as-'.$this->MonthlyTow.'-'.$year.'.xlsx');
    }

    public function render()
    {
        return view('livewire.dashboard.admin.orders.export.excel-between-month');
    }
}
