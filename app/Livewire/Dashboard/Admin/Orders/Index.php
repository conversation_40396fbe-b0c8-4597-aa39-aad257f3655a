<?php

namespace App\Livewire\Dashboard\Admin\Orders;

use App\Models\Sefaresh;
use Livewire\Component;

class Index extends Component
{
    public $year;

    public $month;

    public function render()
    {
        $orders = Sefaresh::with('userRecipient')->where('year', $this->year)->where('month', $this->month)->latest()->get();

        return view('livewire.dashboard.admin.orders.index', [
            'orders' => $orders,
        ]);
    }
}
