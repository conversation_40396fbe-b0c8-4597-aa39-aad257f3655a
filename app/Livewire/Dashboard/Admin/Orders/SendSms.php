<?php

namespace App\Livewire\Dashboard\Admin\Orders;

use Livewire\Component;
use App\Models\Sefaresh;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use App\Models\ShortLinkPayment;
use Illuminate\Support\Str;
class SendSms extends Component
{
    use LivewireAlert;
    
    public $orderId;

    public $baseUrlLocal = 'http://127.0.0.1:8003/';
    public $baseUrlProduction = 'https://shop.tidamode.ir/';
    
    public function generateUniqueShortLink($length = 5) {
        do {
            // تولید یک رشته تصادفی شامل حروف بزرگ و کوچک
            $shortLink = Str::random(rand(3, $length));
        } while (ShortLinkPayment::where('short', $shortLink)->exists()); // بررسی عدم تکراری بودن
    
        return $shortLink;
    }

    public function send(){
        try{
            $order = Sefaresh::whereId($this->orderId)->first();
            if($order->factors->total < 1000){
                return $this->alert('error', 'مبلغ فاکتور اشتباه است', [
                    'position' => 'center',
                    'timer' => 3000,
                    'toast' => false,
                    'text' => "مبلغ کل فاکتور کمتر از 1000 ریال است، لطفا به مبلغ کل فاکتور دقت فرمایید",
                    'timerProgressBar' => true,
                    'showDenyButton' => true,
                    'onDenied' => '',
                    'denyButtonText' => 'بسیار خب متوجه شدم',
                ]);
            }

            if(isset($order->factors->phone) && $order->factors->phone != null){

                $url = $this->baseUrlProduction.'factor/'.$this->orderId;
                
                $sortLink = $this->generateUniqueShortLink(5);
                $short = ShortLinkPayment::create([
                    'user_id' => auth()->user()->id,
                    'factor_id' => $order->factors->id,
                    'order_id' => $this->orderId,
                    'link' => $url,
                    'short' => $sortLink,
                    'phone' => $order->factors->phone,
                    'fullname' => $order->user->fullname,
                    'status' => 1
                ]);

                $shortLinkUrl = $this->baseUrlProduction.'f/'.$sortLink;
                
                // dd($shortLinkUrl);
                $api = new \Ghasedak\GhasedakApi('d94533ddea183fd812aeda3f3c4f0db4272bf162a1b7415d475e8f70f05eaded');
                $api->Verify($order->factors->phone,'urluser', $order->user->fullname, $order->factors->fullname, $shortLinkUrl);
                $api->Verify($order->factors->phone,'urlsubscriber', $order->factors->fullname, $shortLinkUrl);
                
            }else{
                $this->alert('error', 'خطا در ارسال', [
                    'position' => 'center',
                    'timer' => 3000,
                    'toast' => false,
                    'text' => 'فاکتور این سفارش کامل وارد نشده است، لطفاً فاکتور رو قبل از ارسال بررسی کنید',
                    'timerProgressBar' => true,
                    'showDenyButton' => true,
                    'onDenied' => '',
                    'denyButtonText' => 'بسیار خب متوجه شدم',
                ]);
            }
        }catch(\Exception $e){
            $this->alert('error', 'خطا در سیستم', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'در سمت سرور خطایی رخ داده است، این خطا گزارش سیستمی شد، در صورت تکرار شما گزارش را ارسال کنید',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }
    }


    public function render()
    {
        return view('livewire.dashboard.admin.orders.send-sms');
    }
}
