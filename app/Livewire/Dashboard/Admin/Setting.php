<?php

namespace App\Livewire\Dashboard\Admin;

use Livewire\Component;
use App\Models\Setting as Option;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
class Setting extends Component
{
    use LivewireAlert;
    public $gold18k, $factorId;

    public $gold18k_status = true;

    public function mount(){
        function getFormattedOptionBody($type, $default) {
            $setting = Option::where('type', $type)->latest()->first();
            return isset($setting) && $setting != null ? formatMoney($setting->body) : $default;
        }
        
        $this->gold18k = getFormattedOptionBody('gold18k', 0);
        $this->factorId = getFormattedOptionBody('factorId', 1);
        
    }

    public function store(){
        try{
            
            DB::beginTransaction();

            Option::updateOrCreate(
                ['type' => 'gold18k'],
                ['body' => $this->gold18k]
            );
            
            Option::updateOrCreate(
                ['type' => 'factorId'],
                ['body' => $this->factorId]
            );
        
            DB::commit();

            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                 'position' => 'top-start',
             ]);
 
         }catch(\Exception $e){
 
             DB::Rollback();
 
             $this->alert('error', 'خطا در ثبت', [
                 'position' => 'center',
                 'timer' => 3000,
                 'toast' => false,
                 'text' => 'سیستم با خطا مواجه شد',
                 'timerProgressBar' => true,
                 'showDenyButton' => true,
                 'onDenied' => '',
                 'denyButtonText' => 'بسیار خب متوجه شدم',
             ]);
 
         }
    }

    public function render()
    {
        
        return view('livewire.dashboard.admin.setting');
    }
}
