<?php

namespace App\Livewire\Dashboard\Orders;

use App\Models\Sefaresh;
use App\Models\StatusHistory;
use Illuminate\Support\Facades\DB;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class ChangePrintGroup extends Component
{
    use LivewireAlert;

    public $selectedItems = [];

    public $last_status;

    public $date_last_status;

    public function rules()
    {
        return [
            'last_status' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'last_status.required' => 'پر کردن فیلد الزامیست',
        ];
    }

    public function store()
    {
        $this->validate();

        try {
            DB::beginTransaction();

            // دریافت سفارشات براساس آیتم‌های انتخاب‌شده
            $orders = Sefaresh::whereIn('id', $this->selectedItems)->get();

            foreach ($orders as $item) {
                $item->last_status = $this->last_status;
                $item->date_last_status = $this->date_last_status;
                $item->save();

                // ذخیره تاریخچه وضعیت
                $count = StatusHistory::where('sefaresh_id', $item->id)->where('last_status', $this->last_status)->count();
                if ($count == 0) {
                    StatusHistory::create([
                        'user_id' => auth()->user()->id,
                        'sefaresh_id' => $item->id,
                        'last_status' => $this->last_status,
                        'date_last_status' => $this->date_last_status,
                    ]);

                    // ارسال SMS بر اساس وضعیت
                    if ($this->last_status == 'money') {
                        // smsTasviehMoshtari(...)
                    } elseif ($this->last_status == 'send' && $this->post_type == 'پیک') {
                        // smsErsalShodpPeyk(...)
                    } else {
                        // smsStatusMoshtari(...)
                    }
                }
            }

            DB::commit();

            $this->alert('success', 'ثبت اطلاعات با موفقیت انجام شد', [
                'position' => 'top-start',
            ]);

            return redirect()->route('admin-dashboard-orders');

        } catch (\Exception $e) {
            DB::rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'سیستم با خطا مواجه شد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);
        }
    }

    public function render()
    {
        return view('livewire.dashboard.orders.change-print-group');
    }
}
