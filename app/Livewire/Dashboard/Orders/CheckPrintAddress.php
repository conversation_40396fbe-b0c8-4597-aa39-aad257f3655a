<?php

namespace App\Livewire\Dashboard\Orders;

use Livewire\Component;
use App\Models\Sefaresh;
class CheckPrintAddress extends Component
{
    public $orderId;
    public $checked;

    public function mount(){
        $this->checked = $this->checked == 1 ? true : false;
    }

    public function updatedChecked()
    {
        $order = Sefaresh::whereId($this->orderId)->first();
        $order->checked = $order->checked == 0 ? 1 : 0;
        $order->save();
    }
    
    public function render()
    {
        return view('livewire.dashboard.orders.check-print-address');
    }
}
