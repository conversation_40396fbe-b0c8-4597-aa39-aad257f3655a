<?php

namespace App\Livewire\Dashboard\Orders;

use Livewire\Component;
use Livewire\Attributes\On; 

class OrdersCountNotreadSpan extends Component
{
    public $count = 0;

    #[On('orders-count-notread')]
    public function getCount($count){
        $this->count = $count;
    }

    public function render()
    {
        return view('livewire.dashboard.orders.orders-count-notread-span');
    }
}
