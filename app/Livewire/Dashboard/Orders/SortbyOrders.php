<?php

namespace App\Livewire\Dashboard\Orders;

use Livewire\Component;

class SortbyOrders extends Component
{
    public function SortByNewest($sortby){
        $this->SortBy($sortby);
    }

    public function SortByOldest($sortby){
        $this->SortBy($sortby);
    }

    public function SortByHighestPrice($sortby){
        $this->SortBy($sortby);
    }
    
    public function SortBy($sortby){
        $this->dispatch('sortby-orders', sortby: $sortby);
    }

    public function render()
    {
        return view('livewire.dashboard.orders.sortby-orders');
    }
}
