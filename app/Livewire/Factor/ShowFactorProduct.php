<?php

namespace App\Livewire\Factor;

use App\Jobs\SendPayamakTransactionJob;
use App\Models\Discount;
use App\Models\Invoice;
use App\Models\InvoiceProduct;
use App\Models\InvoiceProductDetail;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Models\ShortLinkPayment;
use App\Models\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Locked;
use Livewire\Component;

class ShowFactorProduct extends Component
{
    use LivewireAlert;

    #[Locked]
    public $discount_amount = 0;

    #[Locked]
    public $percentage = 0;

    public $data = [
        'productId' => null,
        'userId' => null,
        'detailId' => null,
        'total_factor' => 0,
        'discount_amount' => 0,
        'discount_code' => null,
        'payment_factor' => 0,
        'discount_payment' => 0,
        // 'percentage' => null,
    ];

    public $form = [
        'productId' => null,
        'productDetailId' => null,
        'userId' => null,
        'fullname' => null,
        'description' => null,
        'phone' => null,
        'amount' => 0,
        'send' => 'post',
        'zipcode' => null,
        'total_factor' => 0,
        'discount_code' => null,
        'payment_factor' => 0,
        'money_post' => 0,
    ];

    public function payment()
    {

        // $this->form['amount'] = (float) str_replace(',', '', $this->form['amount']);

        // اعتبارسنجی داده‌های دریافتی (با استفاده از dot notation برای دسترسی به عناصر آرایه)
        $validated = $this->validate([
            'form.fullname' => 'required|min:3|max:1000',
            'form.description' => 'required|min:3|max:10000',
            'form.phone' => 'required|numeric',
            // 'form.amount' => 'required|numeric|min:100000',
            'form.send' => 'required',
            'form.zipcode' => 'required|numeric',
            // در صورت لزوم اعتبارسنجی بقیه فیلدها مثل productId و detailId هم اضافه شود
        ], [
            'form.fullname.required' => 'وارد کردن نام و نام خانوادگی الزامی است.',
            'form.fullname.min' => 'نام و نام خانوادگی باید حداقل ۳ کاراکتر باشد.',
            'form.fullname.max' => 'نام و نام خانوادگی نمی‌تواند بیش از ۱۰۰۰ کاراکتر باشد.',

            'form.description.required' => 'وارد کردن آدرس الزامی است.',
            'form.description.min' => 'آدرس باید حداقل ۳ کاراکتر باشد.',
            'form.description.max' => 'آدرس نمی‌تواند بیش از ۱۰۰۰۰ کاراکتر باشد.',

            'form.phone.required' => 'وارد کردن شماره تماس الزامی است.',
            'form.phone.numeric' => 'شماره تماس باید فقط شامل اعداد باشد.',

            // 'form.amount.required' => 'وارد کردن مبلغ الزامی است.',
            // 'form.amount.numeric' => 'مبلغ باید یک عدد باشد.',
            // 'form.amount.min' => 'مبلغ وارد شده باید حداقل ۱۰۰,۰۰۰ تومان باشد.',

            'form.send.required' => 'انتخاب نوع ارسال الزامی است.',

            'form.zipcode.required' => 'وارد کردن کد پستی الزامی است.',
            'form.zipcode.numeric' => 'کد پستی باید فقط شامل اعداد باشد.',
        ]);

        $send = 0;
        if ($this->form['send'] == 'post') {
            $send = 1500000;
        }

        // دریافت اطلاعات محصول با استفاده از مقادیر داده شده
        $productView = DB::table('product_details_view')
            ->where('product_id', $this->data['productId'])
            ->where('product_detail_id', $this->data['detailId'])
            ->first();

        $productDetail = ProductDetail::where('product_id', $this->data['productId'])
            ->where('id', $this->data['detailId'])
            ->first();

        // تعیین مبلغ محصول (در صورتی که مقدار ثابتی وجود داشته باشد)

        $amount = $productDetail->product->fixed_amount == null
            ? $productDetail->amount
            : $productDetail->product->fixed_amount;

        if ($amount == null) {
            $this->alert('error', 'خطا در سرور', [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
                'text' => 'اطلاعات ارسال شده اشتباه می باشد',
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

            return;

        }

        if (isset($productDetail)) {
            $invoice = Invoice::create([
                'user_id' => unHashId($this->data['userId']) ?? 1,
                'total_factor' => (float) str_replace(',', '', $this->data['total_factor']),
                'total_payment' => (float) str_replace(',', '', $this->data['payment_factor']),
                'fullname' => $this->form['fullname'],
                'phone' => $this->form['phone'],
                'description' => $this->form['description'].' '.getSendStatus($this->form['send']),
                'address' => $this->form['description'],
                'zipcode' => $this->form['zipcode'],
                'post_type' => getSendStatus($this->form['send']),
                'post' => true,
                'money_post' => (float) $send,
                'gold18k' => getGold18k(),
                'discount' => (float) $this->data['discount_payment'],
                'image' => $productDetail->product->gallery?->pluck('url')->first(),
            ]);

            if ($productView->fixed_amount == null) {
                ProductDetail::where('product_id', $productDetail->product->id)
                    ->whereId($productDetail->id)
                    ->update([
                        'reserved_count' => 1 + (int) $productDetail->reserved_count,
                    ]);

            } else {
                Product::whereId($productDetail->product->id)->update([
                    'reserved_count' => 1 + (int) $productDetail->product->reserved_count,
                ]);
            }

            $invoiceProduct = InvoiceProduct::create([
                'invoice_id' => $invoice->id,
                'product_id' => $productView->product_id,
                'title_fa' => $productView->product_title_fa,
                'title_eng' => $productView->product_title_eng,
                'category_id' => $productView->category_id,
                'for_id' => $productView->for_id,
                'description' => $productDetail->description,
                'slug' => $productDetail->product->slug,
                'count' => 1,
            ]);

            InvoiceProductDetail::create([
                'invoice_id' => $invoice->id,
                'product_id' => $invoiceProduct->id,
                'product_detail_id' => $productDetail->id,
                'eticket' => $productDetail->eticket,
                'weight' => $productDetail->weight,
                'cutie' => $productDetail->cutie,
                'profit' => $productDetail->profit,
                'color_id' => $productDetail->color_id,
                'construction_wages' => $productDetail->construction_wages,
                'branch_id' => $productDetail->branch_id,
                'count' => 1,
                'amount' => str_replace(',', '', $amount),
                'chain_size' => $productDetail->chain_size,
                'pluck' => $productDetail->pluck,
                'model' => $productDetail->model,
                'tax' => $productDetail->tax,
                'fixed_amount' => $productView->fixed_amount,
                'stone_price' => str_replace(',', '', $productDetail->stone_price),
            ]);

            $baseUrlProduction = env('APP_TIDAMODE_SHOP_URL');
            $url = $baseUrlProduction.'factor/'.hashId($invoice->id);

            $sortLink = generateUniqueShortLink(5);
            ShortLinkPayment::create([
                'user_id' => unHashId($this->data['userId']) ?? 1,
                'order_id' => $invoice->id,
                'link' => $url,
                'short' => $sortLink,
                'fullname' => $this->form['fullname'],
                'phone' => $this->form['phone'],
                'status' => 1,
            ]);

            $link = $baseUrlProduction.'f/'.$sortLink;
            // اگر بخواهید می‌توانید لینک کوتاه را در data یا متغیر دیگری ذخیره کنید

            // ارسال پیام payamak
            SendPayamakTransactionJob::dispatch($this->form['phone'], $this->form['fullname'], $link);

            return redirect()->route('factor-payment', $invoice->id);
        }
        $this->alert('error', 'خطا در سرور', [
            'position' => 'center',
            'timer' => 3000,
            'toast' => false,
            'text' => 'اطلاعات ارسال شده اشتباه می باشد',
            'timerProgressBar' => true,
            'showDenyButton' => true,
            'onDenied' => '',
            'denyButtonText' => 'بسیار خب متوجه شدم',
        ]);

    }

    private function calcDiscount($product, $user)
    {

        $discount = Discount::where('code', $this->data['discount_code'])->where('status', 'active')->firstOrFail();

        if ($product->fixed_amount === null || $product->fixed_amount == '') {

            $this->percentage = $discount->percentage / 100;

            $gold18k = (float) str_replace(',', '', $product->gold18k);
            $weight = (float) $product->weight;
            $constructionWagesPercent = (float) $product->construction_wages / 100;
            $profitPercent = (float) $product->profit;
            $taxPercent = (float) $product->tax / 100;

            $stone_price = 0;

            $wage = $weight * $constructionWagesPercent;

            // تخفیف روی سود اعمال میشه
            $discountedProfitPercent = ($profitPercent * $discount->percentage) / 100;

            // سود با در نظر گرفتن وزن + اجرت
            $profit = ($weight + $wage) * $discountedProfitPercent / 100;

            $totalPriceBeforeTax = $weight + $wage + $profit;
            $totalPrice = $totalPriceBeforeTax * $gold18k;

            $tax = ($totalPrice - ($weight * $gold18k)) * $taxPercent;

            $finalPrice = $totalPrice + $tax + $stone_price;

            $finalPrice = floor($finalPrice / 10000) * 10000;
            $this->data['discount_amount'] = $finalPrice;
            $this->discount_amount = $finalPrice;
        }
    }

    public function apply() {}

    public function clear()
    {
        $this->data['discount_code'] = null;
        $this->data['discount_payment'] = 0;
    }

    public function render()
    {

        $user = User::whereId(unHashId($this->data['userId']))->first();
        // $product = ProductDetail::where('product_id', $productId)->where('id', $detailId)->first();
        $product = DB::table('product_details_view')->
        where('product_id', $this->data['productId'])->where('product_detail_id', $this->data['detailId'])->
        select(['product_detail_id', 'weight', 'chain_size', 'amount', 'tax', 'construction_wages', 'profit', 'eticket', 'chain_size', 'updated_at', 'product_id', 'product_title_fa', 'fixed_amount', 'available_stock', 'category_title_fa', 'product_image_url', 'gold18k'])->first();

        if (is_null($product)) {
            return abort(404);
        }

        if (Arr::get($this->data, 'discount_code') !== null) {

            $this->calcDiscount($product, $user);
        }

        $this->data['total_factor'] = $product?->fixed_amount ? formatMoney($product?->fixed_amount) : formatMoney($product->amount);

        if ($this->form['send'] == 'post') {
            $this->form['money_post'] = 1500000;
        } else {
            $this->form['money_post'] = 0;
        }

        $total = (float) str_replace(',', '', $this->data['total_factor']);
        $this->data['discount_payment'] = $this->data['discount_amount'] != 0 && $this->data['discount_code'] != null ? $total - $this->data['discount_amount'] : 0;
        $this->data['payment_factor'] = $total + $this->form['money_post'] - $this->data['discount_payment'];

        return view('livewire.factor.show-factor-product', [
            'product' => $product,
            'user' => $user,
        ]);
    }
}
