<?php

namespace App\Livewire\Tidanerkh;

use Illuminate\Support\Facades\Storage;
use Livewire\Component;

class Index extends Component
{
    public float $weight = 0.89;

    public float $wage = 22;

    public float $profit = 7;

    public float $tax = 10;

    public float $attachmentCost = 0;

    public int $pricePerGram = 67529000;

    public float $finalPrice = 0;

    public function calculate()
    {
        $basePrice = $this->pricePerGram * $this->weight;

        $wagePrice = ($basePrice * $this->wage) / 100;
        $profitAmount = ($basePrice * $this->profit) / 100;
        $taxAmount = ($basePrice * $this->tax) / 100;

        $this->finalPrice = $basePrice + $wagePrice + $profitAmount + $taxAmount + $this->attachmentCost;
    }

    protected $listeners = ['webcam-captured' => 'handleWebcamCapture'];

    public function handleWebcamCapture($data)
    {
        $imageData = $data['image'];
        $image = str_replace('data:image/png;base64,', '', $imageData);
        $image = str_replace(' ', '+', $image);
        $imageName = 'webcam_'.time().'.png';

        Storage::disk('public')->put("webcam/$imageName", base64_decode($image));
        dd('webcam/'.$imageName);
        // redirect('https://tidamode.iran.liara.run/storage/'."webcam/$imageName");
    }

    public function render()
    {
        return view('livewire.tidanerkh.index');
    }
}
