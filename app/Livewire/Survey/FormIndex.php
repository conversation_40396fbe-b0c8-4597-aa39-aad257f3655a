<?php

namespace App\Livewire\Survey;

use App\Models\Sefaresh;
use App\Models\SurveyAnswer;
use App\Models\SurveyQuestion;
use App\Models\SurveyResponse;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;

class FormIndex extends Component
{
    use LivewireAlert;

    // لیست سوالات نظرسنجی
    public $questions;

    // آرایه‌ای برای نگهداری پاسخ‌های کاربر به سوالات
    public $answers = [];

    // مرحله فعلی فرم (سوال فعلی)
    public $currentStep = 0;

    public $orderId;

    // هنگام بارگذاری کامپوننت، سوالات نظرسنجی همراه با گزینه‌هایشان دریافت می‌شوند
    public function mount()
    {

        if (empty($this->orderId)) {

            return redirect('https://tidamode.ir');
        }

        $order = Sefaresh::whereId(unHashId($this->orderId))->first();
        if (! $order) {
            return redirect('https://tidamode.ir');
        }

        $this->questions = SurveyQuestion::with('options')->get();
    }

    // رفتن به مرحله بعد
    public function nextStep()
    {
        // اعتبارسنجی اینکه کاربر به سوال فعلی پاسخ داده باشد
        $this->validate([
            'answers.'.$this->currentStep => 'required',
        ]);

        $this->currentStep++;
    }

    // بازگشت به مرحله قبل
    public function previousStep()
    {
        if ($this->currentStep > 0) {
            $this->currentStep--;
        }
    }

    // ارسال نهایی فرم
    public function submit()
    {
        $rules = [];
        $messages = [];

        foreach ($this->questions as $index => $question) {
            if ($question->is_required) {
                $rules["answers.$index"] = 'required';
                $messages["answers.$index.required"] = 'لطفاً به این سؤال پاسخ دهید.';
            }
        }

        $this->validate($rules, $messages);

        $order = Sefaresh::whereId(unHashId($this->orderId))->first();
        if ($order) {
            // ایجاد یک ردیف جدید برای پاسخ‌دهنده
            $response = SurveyResponse::create([
                'survey_id' => $order->id,
                'user_id' => $order->user_id,
                'answers' => json_encode($this->questions),
            ]);

            // ذخیره پاسخ هر سوال
            foreach ($this->answers as $index => $answer) {
                $question = $this->questions[$index];
                SurveyAnswer::create([
                    'response_id' => $response->id,
                    'question_id' => $question->id,
                    'answer_text' => is_array($answer) ? json_encode($answer) : $answer,
                ]);
            }

            $order->survey = $response->id;
            $order->save();

        }

        // نمایش پیام موفقیت با LivewireAlert
        $this->alert('success', 'ثبت موفق', [
            'position' => 'center',
            'timer' => 3000,
            'toast' => false,
            'text' => 'از اینکه در نظرسنجی ما شرکت کردید، صمیمانه سپاسگزاریم. نظرات شما به بهبود خدمات ما کمک می‌کند.',
            'timerProgressBar' => true,
            'showDenyButton' => true,
            'onDenied' => '',
            'denyButtonText' => 'بسیار خب متوجه شدم',
        ]);

        $this->dispatch('surveySubmitted', surveyId: $this->orderId);

        // ریست‌کردن فرم برای استفاده مجدد
        // $this->reset('answers', 'currentStep');
        return redirect()->route('survey', $this->orderId);
    }

    // رندر کردن نمای فرم
    public function render()
    {
        return view('livewire.survey.form-index');
    }
}
