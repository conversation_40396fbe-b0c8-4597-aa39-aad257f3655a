<?php

namespace App\Livewire;

use Livewire\Component;
use thiagoalessio\TesseractOCR\TesseractOCR;
use Illuminate\Support\Facades\Http;
use App\Models\Sefaresh;
use App\Models\SefareshAnalysis;

class OrderOcrProcessor extends Component
{
    public $status = 'شروع نشده';
    public $currentOrderId = null;
    public $currentImageField = null;
    public $progress = 0;
    public $total = 0;

    public function processOrders()
    {
        $orders = Sefaresh::where('month', '4')->where('year', '1404')->get();
        $imageFields = ['image1', 'image2', 'image3', 'image4', 'image5', 'image6', 'image7'];

        $totalImages = 0;
        foreach ($orders as $order) {
            foreach ($imageFields as $field) {
                if (!empty($order->$field)) {
                    $totalImages++;
                }
            }
        }
        $this->total = $totalImages;
        $this->progress = 0;

        foreach ($orders as $order) {
            foreach ($imageFields as $field) {
                $imageUrl = $order->$field;
                if (empty($imageUrl))
                    continue;

                $this->currentOrderId = $order->id;
                $this->currentImageField = $field;
                $this->status = "دانلود تصویر {$field} برای سفارش {$order->id}";

                try {
                    // حذف فقط اسلش‌های اول
                    $imageUrl = ltrim($imageUrl, '/');

                    $response = Http::get('https://tdservice.ir/' . $imageUrl);

                    if ($response->ok()) {
                        $tempPath = storage_path('app/public/temp/' . basename($imageUrl));
                        file_put_contents($tempPath, $response->body());

                        $this->status = "استخراج متن از تصویر {$field} برای سفارش {$order->id}";
                        $text = (new TesseractOCR($tempPath))
                            ->lang('fas')
                            ->run();

                        // unlink($tempPath);

                        SefareshAnalysis::updateOrCreate(
                            [
                                'sefaresh_id' => $order->id,
                                'image_field' => $field,
                            ],
                            [
                                'extracted_text' => $text,
                            ]
                        );
                    } else {
                        \Log::error("دانلود تصویر {$imageUrl} برای سفارش {$order->id} موفق نبود.");
                    }
                } catch (\Exception $e) {
                    \Log::error("خطا در پردازش تصویر {$imageUrl} برای سفارش {$order->id}: " . $e->getMessage());
                }

                $this->progress++;
                sleep(5);

                $this->dispatch('progressUpdated', ['progress' => $this->progress, 'total' => $this->total]);

            }
        }

        $this->status = "تمام شد";
    }

    public function render()
    {

        return view('livewire.order-ocr-processor');
    }
}
