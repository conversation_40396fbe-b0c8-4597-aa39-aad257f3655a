<?php

namespace App\Livewire\Admin\Order\Online;

use App\Jobs\SendPayamakTransactionJob;
use App\Models\FactorItemEmpty;
use App\Models\FactorOrderEmpty;
use App\Models\Financial;
use App\Models\Invoice;
use App\Models\InvoiceProductDetail;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Models\Sefaresh;
use App\Models\ShortLinkPayment;
use App\Models\StatusHistory;
use App\Models\User;
use App\Models\ZinbalTransaction;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\On;
use Livewire\Component;

class ChangeStatusOrder extends Component
{
    use LivewireAlert;

    public $status;

    public $orderId;

    #[On('set-order-online')]
    public function setOrderOnline($orderId)
    {
        $this->orderId = $orderId;
    }

    public $baseUrlProduction = 'https://shop.tidamode.ir/';

    public function store()
    {

        try {
            if ($this->status == 'cancel') {

                $invoice = Invoice::where('id', $this->orderId)->first();
                if (isset($invoice) && $invoice != null && $invoice->status != 'reject') {
                    Invoice::where('id', $this->orderId)->update([
                        'status' => 'reject',
                    ]);

                    $invoice = Invoice::whereId($this->orderId)->first();
                    $invoiceDetails = InvoiceProductDetail::where('invoice_id', $this->orderId)->get();

                    foreach ($invoiceDetails as $item) {

                        // if ($item?->product?->product?->fixed_amount != null) {

                        //     Product::whereId($item->product->product->id)->update([
                        //         'reserved_count' => $item?->product?->product?->reserved_count - $item->count,
                        //         'out_stock' => $item?->product?->product?->out_stock - $item->count,
                        //     ]);

                        // } else {

                        ProductDetail::where('product_id', $item->product->product->id)->where('weight', $item->weight)->update([
                            'reserved_count' => null,
                            'out_stock' => null,
                        ]);
                        // }

                    }
                    Artisan::call('optimize:clear');
                    $this->alert('success', 'اطلاعات سفارش بروزشد', [
                        'position' => 'top-start',
                    ]);

                    return redirect()->route('admin-dashboard-order-online');
                }

                $this->alert('error', 'این سفارش قبلا کنسل شده است', [
                    'position' => 'top-start',
                ]);

            }

            if ($this->status == 'update') {

                $invoice = Invoice::whereId($this->orderId)->first();

                $shortModel = ShortLinkPayment::where('order_id', $this->orderId)->first();

                $link = $this->baseUrlProduction.'f/'.$shortModel?->short;

                $invoiceDetails = InvoiceProductDetail::where('invoice_id', $this->orderId)->get();
                foreach ($invoiceDetails as $item) {

                    // if ($item?->product?->product?->fixed_amount != null) {

                    //     Product::whereId($item->product->product->id)->update([
                    //         'reserved_count' => '1',
                    //     ]);

                    // } else {

                    ProductDetail::where('product_id', $item->product->product->id)->where('eticket', $item->eticket)->where('weight', $item->weight)->update([
                        'reserved_count' => '1',
                    ]);
                    // }

                    if ($item->weight != null) {
                        $moneyData = getMoneyInvoice($item, null, false);
                        $item->amount = $moneyData['money'];
                        $item->save();
                    }

                }

                Invoice::where('id', $this->orderId)->update([
                    'created_at' => now(),
                    'status' => 'pending',
                    'gold18k' => getGold18k(),
                ]);

                $this->alert('success', 'بروزرسانی سفارش با موفقیت ثبت شد', [
                    'position' => 'top-start',
                ]);

                if (app()->environment('production') && $invoice?->phone != null && $shortModel?->short != null) {
                    SendPayamakTransactionJob::dispatch($invoice?->phone, $invoice?->fullname, $link);
                }
                Artisan::call('optimize:clear');
                DB::commit();

                return redirect()->route('admin-dashboard-order-online');
            }

            if ($this->status == 'accept') {
                if (auth()->user()->level == 'admin') {
                    try {

                        DB::beginTransaction();

                        $invoice = Invoice::whereId($this->orderId)->first();

                        if ($invoice) {
                            $invoice->status = 'success';
                            $invoice->save();

                            $v = verta();
                            $code = '';

                            $c = Sefaresh::where('month', '=', $v->month)->where('year', '=', $v->year)->count();
                            if (isset($c)) {
                                $c += 1;
                                $code = 'G'.substr($v->year, 3).$v->month.'/'.$c;
                            }

                            $totalPayment = (float) str_replace(',', '', $invoice->total_payment);
                            $totalPayment = (float) str_replace(',', '', $invoice->total_payment) + (float) str_replace(',', '', $invoice->money_post);
                            $trackNumber = $invoice?->transaction?->tracking_number;
                            if ($trackNumber != null) {
                                $transaction = ZinbalTransaction::where('trackId', $trackNumber)->first();
                                if ($transaction) {
                                    $dataImage = [
                                        'User_Phone' => $invoice->phone,
                                        'TrackId' => $_GET['trackId'],
                                        'Amount' => formatMoney($invoice->total_payment).' Riral',
                                        'CardNumber' => $transaction->cardNumber,
                                        'PaidAt' => $transaction->paidAt,
                                        'PaidAt_Shamsi' => shamsiDate($transaction->paidAt),
                                    ];
                                    $filename = $_GET['trackId'].'_'.$invoice->phone;
                                    $imageResive = $this->generateImageResevie($dataImage, $filename);
                                }
                            }

                            $imageResive = null;

                            $order = Sefaresh::updateOrCreate(
                                [
                                    'code' => $code,
                                ],
                                [
                                    'invoice_id' => $this->orderId,
                                    'chat_id' => Str::uuid(),
                                    'user_id' => $invoice->user_id,
                                    'type' => 'multi',
                                    'type_construction' => 'آماده',
                                    'model' => $invoice->productDetails->first()?->product->title_fa,
                                    'sex' => 'طلا',
                                    'color' => '',
                                    'font' => '',
                                    'name_pluck' => '',
                                    'post_type' => $invoice->post_type,
                                    'tracking_code' => '',
                                    'tips' => '',
                                    'total_amount' => $invoice->total_payment,
                                    'deposit1' => $invoice->total_payment,
                                    'deposit2' => '',
                                    'remaining' => 0,
                                    'sefaresh_total' => $invoice->total_payment,
                                    'package_type' => '',
                                    'package_amount' => '',

                                    'fullname' => $invoice->fullname,
                                    'phone' => $invoice->phone,
                                    'address' => $invoice?->address,
                                    'codepost' => $invoice?->zipcode,

                                    'chain' => '',
                                    'size' => '',
                                    'size_wrist' => '',
                                    'size_ankle' => '',

                                    'order_register_date' => todays(),
                                    'customer_date' => '-',
                                    'last_status' => 'ready',
                                    'date_last_status' => todays(),

                                    'whatsapp' => 'سامانه',
                                    'phone_whatsapp' => '0',
                                    'product_code' => '0',

                                    'designer_id' => '',
                                    'manufacturer_id' => '',
                                    'dimensions' => '',

                                    'image1' => $imageResive,

                                    'year' => $v->year,
                                    'month' => $v->month,
                                    'day' => $v->day,

                                    'gram' => $invoice->productDetails->sum('weight'),
                                    'gold18k' => $invoice->gold18k,

                                ]);

                            $transaction = ZinbalTransaction::where('TrackId', $this->orderId)->update([
                                'orderId' => $order->id,
                            ]);

                            $userRefer = User::where('id', $invoice->user_id)->first();

                            // dd($data['money_post'], $data['total_factor'] + $data['money_post'], $data['total_payment']);
                            Financial::create(
                                [
                                    'sefaresh_id' => $order->id,
                                    'user_id' => $invoice->user_id,
                                    'chain' => '0',
                                    'package_amount' => '0',
                                    'amount_ready' => '0',
                                    'designing' => '0',
                                    'sender' => formatMoney($invoice->money_post),
                                    'total_amount' => $invoice->total_payment,
                                    'commission' => calcCommission($invoice->productDetails->sum('weight'), $invoice->gold18k),
                                    'packing' => '0',
                                    'profit' => '0',
                                    'tipsGold' => 'فروش حضوری به جمع سفارش: '.formatMoney($invoice->total_factor).' و هزینه ارسال: '.formatMoney($invoice->money_post).' به نام کارشناس: '.$userRefer?->fullname,
                                ]
                            );

                            $historys = StatusHistory::Create([
                                'user_id' => $invoice->user_id,
                                'sefaresh_id' => $order->id,
                                'last_status' => 'ready',
                                'date_last_status' => now(),
                            ]);

                            $invoices = Invoice::whereId($this->orderId)->get();

                            $factorLatestId = FactorOrderEmpty::where('order_id', $order->id)->latest()->first();
                            $factor = FactorOrderEmpty::create([
                                'user_id' => $order->user_id,
                                'order_id' => $order->id,
                                'fullname' => $order->fullname,
                                'phone' => $order->phone,
                                'factor_number' => isset($factorLatestId) && $factorLatestId != null ? $factorLatestId->factor_number : 1,
                                'factor_create_date' => todays(),
                                // 'subscribe_code' => $data['code'],
                                'factor_total' => $invoice->total_payment,
                                'factor_deposit' => $invoice->total_payment,
                                'factor_discount' => 0,
                                'total' => $invoice->total_payment,
                                'countWeight' => $invoice->productDetails->sum('weight'),
                                // 'post' => $data['post'],
                                'gold18kup' => $order->gold18k,
                                // 'factor_discount' => $invoice->discount,
                            ]);

                            $validCategories = ['jewelry', 'bracelet', 'ring', 'spoon', 'ankle_jewlery', 'jewelery', 'multi'];
                            foreach ($invoices as $invoice) {

                                $factor->factor_discount = $invoice->discount;
                                $factor->save();

                                foreach ($invoice->productDetails as $detail) {

                                    // if ($detail?->fixed_amount != null) {

                                    //     $product = Product::whereId($detail->product_id)->first();
                                    //     $count = ((int) ($product->reserved_count ?? 0)) + (int) $detail->count;
                                    //     // $product->reserved_count = ((int) ($product->reserved_count ?? 0)) - (int) $detail->count;
                                    //     $product->out_stock = $count == 0 ? 1 : null;
                                    //     $product->save();

                                    // } else {
                                    $product = ProductDetail::whereId($detail->product_detail_id)->first();
                                    // $product->reserved_count = 1;
                                    $product->out_stock = 1;
                                    $product->save();
                                    // }

                                    $category = $detail?->product?->category?->category;
                                    $type = in_array($category, $validCategories) ? $category : 'multi';

                                    Sefaresh::whereId($order->id)->update([
                                        'type' => $type,
                                        'color' => $detail?->color?->description,
                                        'order_type' => $detail?->product?->category?->title_eng,
                                        'image3' => $invoice->image,
                                        'size' => $detail->chain_size,
                                    ]);
                                    $amount = $detail->product->fixed_amount != null
                                        ? str_replace(',', '', $detail->product->fixed_amount)
                                        : str_replace(',', '', $detail->amount);

                                    $weight = $detail->product->fixed_amount != null ? null : $detail->weight;
                                    $construction_wages = $detail->product->fixed_amount != null ? null : $detail->construction_wages;
                                    $profit = $detail->product->fixed_amount != null ? null : $detail->profit;
                                    $eticket = $detail->product->fixed_amount != null ? null : $detail->eticket;

                                    FactorItemEmpty::create([
                                        'user_id' => $order->user_id,
                                        'code' => $order->code,
                                        'order_id' => $order->id,
                                        'factor_id' => $factor->id,
                                        'eticket' => $detail->eticket,
                                        'title' => $detail->product->title_fa,
                                        'gold18k' => $order->gold18k,
                                        'cutie' => '18',
                                        'weight' => $weight,
                                        // 'post' => 'yes',
                                        'construction_wages' => $construction_wages,
                                        'profit' => $profit,
                                        'total' => null,
                                        // 'factor_discount' => $invoice->discount,
                                    ]);

                                }
                            }

                            $postMoney = isset($invoice->money_post) ? str_replace(',', '', $invoice->money_post) : null;
                            if ($postMoney > 0 && $postMoney != null) {
                                FactorItemEmpty::create([
                                    'user_id' => $order->user_id,
                                    'code' => '-',
                                    'eticket' => '-',
                                    'cutie' => '0',
                                    'weight' => '0',
                                    'construction_wages' => '0',
                                    'profit' => '0',
                                    'tax' => '0',
                                    'order_id' => $order->id,
                                    'title' => 'هزینه ارسال پست',
                                    'factor_id' => $factor->id,
                                    'gold18k' => $order->gold18k,
                                    'post' => 'yes',
                                    'sender' => formatMoney($invoice->money_post),
                                    'total' => formatMoney($invoice->money_post),
                                ]);
                            }

                            // اگر تعداد آیتم‌های checkouts کمتر از ۵ بود، ردیف‌های خالی اضافه کن
                            if ($invoices->count() < 5) {
                                for ($i = 0; $i < 5 - $invoices->count(); $i++) {
                                    FactorItemEmpty::create([
                                        'user_id' => $order->user_id,
                                        'order_id' => $order->id,
                                        'factor_id' => $factor->id,
                                        // 'subscribe_id' => $order->id,
                                        'code' => null,
                                        'eticket' => null,
                                        'title' => null,
                                        'gold18k' => null,
                                        'cutie' => null,
                                        'weight' => null,
                                        'construction_wages' => null,
                                        'profit' => null,
                                        'tax' => null,
                                        'total' => null,
                                    ]);
                                }
                            }
                        }

                        $this->alert('success', 'بروزرسانی سفارش با موفقیت ثبت شد', [
                            'position' => 'top-start',
                        ]);

                        DB::commit();

                    } catch (\Exception $e) {

                        DB::Rollback();

                        $this->alert('error', 'خطا در ثبت', [
                            'position' => 'center',
                            'timer' => 30000,
                            'toast' => false,
                            'text' => $e->getMessage(),
                            'timerProgressBar' => true,
                            'showDenyButton' => true,
                            'onDenied' => '',
                            'denyButtonText' => 'بسیار خب متوجه شدم',
                        ]);

                    }
                } else {
                    $this->alert('error', 'خطا در ثبت', [
                        'position' => 'center',
                        'timer' => 30000,
                        'toast' => false,
                        'text' => 'شما دسترسی لازم برای تغییر وضعیت سفارش به تایید پرداخت را ندارید',
                        'timerProgressBar' => true,
                        'showDenyButton' => true,
                        'onDenied' => '',
                        'denyButtonText' => 'بسیار خب متوجه شدم',
                    ]);
                }
            }
        } catch (\Exception $e) {

            DB::Rollback();

            $this->alert('error', 'خطا در ثبت', [
                'position' => 'center',
                'timer' => 30000,
                'toast' => false,
                'text' => $e->getMessage(),
                'timerProgressBar' => true,
                'showDenyButton' => true,
                'onDenied' => '',
                'denyButtonText' => 'بسیار خب متوجه شدم',
            ]);

        }
    }

    public function render()
    {
        return view('livewire.admin.order.online.change-status-order');
    }
}
