<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FactorItemEmpty;
use App\Models\FactorOrderEmpty;
use App\Models\Invoice;
use App\Models\ZinbalTransaction;

class FactorController extends Controller
{
    public function index()
    {
        return view('dashboard.admin.factor.index');
    }

    public function factors()
    {
        return view('dashboard.admin.factor.factor-list');
    }

    public function print()
    {
        $factor = FactorOrderEmpty::where('order_id', null)->latest()->first();
        $items = FactorItemEmpty::where('order_id', null)->get();

        return view('dashboard.admin.factor.print', compact('factor', 'items'));
    }

    public function show_user_factor($factorId)
    {
        $transaction = ZinbalTransaction::where('factorId', $factorId)->where('result', 100)->latest()->first();

        $order = Invoice::with('productDetails', 'productDetails.product.gallery')->whereId($factorId)->firstorfail();

        return view('factor.show', compact('order', 'transaction'));
    }
}
