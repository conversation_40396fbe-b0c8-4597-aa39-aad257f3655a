<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\Sefaresh;
use Illuminate\Http\Request;
use App\Models\ShortLinkPayment;
use App\Models\ZinbalTransaction;

class ShortLinkController extends Controller
{
    public function index(Request $req, $request){
        $short = ShortLinkPayment::where('short', $request)->firstOrFail();
        $transaction = ZinbalTransaction::where('orderId', $short->order_id)->where('result', 100)->latest()->first();
        $order = Invoice::whereId($short->order_id)->firstorfail();
        return view('factor.show', compact('order', 'transaction'));
    }
}
