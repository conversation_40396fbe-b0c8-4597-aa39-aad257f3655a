<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller as Controller;
use Illuminate\Http\Response;

class BaseController extends Controller
{
    /**
     * success response method.
     *
     * @return \Illuminate\Http\Response
     */
    public function sendResponse($result, $message, $code = Response::HTTP_OK, $success = true)
    {
        $response = [
            'success' => $success,
            'data' => $message,
            'status' => Response::HTTP_OK,
        ];

        return response()->json($response, $code);
    }

    /**
     * return error response.
     *
     * @return \Illuminate\Http\Response
     */
    public function sendError($error, $errorMessages = [], $code = Response::HTTP_NOT_FOUND)
    {
        $response = [
            'success' => false,
        ];

        if (! empty($errorMessages)) {
            $response['data'] = $errorMessages;
        }

        $response['status'] = $code;

        return response()->json($response, $code);
    }
}
