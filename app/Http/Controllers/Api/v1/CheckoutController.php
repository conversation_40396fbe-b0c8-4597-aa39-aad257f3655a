<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\DeliveryResource;
use App\Models\Delivery;
use App\Models\DeliveryLocation;
use App\Models\Discount;
use App\Models\Product;
use App\Models\ProductDetail;
use App\Models\UserAddress;
use App\Models\UserDiscountUse;
use App\Service\Actions\SetOnlineShopOrderAction;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;

class CheckoutController extends BaseController
{
    protected $baseUrlProduction;

    public function __construct()
    {
        $this->middleware('auth:api');
        $this->baseUrlProduction = env('APP_TIDAMODE_SHOP_URL');
    }

    public function payment(Request $request)
    {

        $user = $request->user();

        if ($user->national_id == null || $user->national_id == 0) {
            return $this->sendError('خطای اعتبارسنجی', [
                'message' => 'شماره ملی خود را در پروفایل ثبت نمایید',
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        if (! isValidIranianNationalCode($user->national_id)) {
            return $this->sendError('خطای اعتبارسنجی', [
                'message' => 'فرمت شماره ملی ثبت شده اشتباه می باشد لطفا اصلاح کنید',
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $validator = Validator::make($request->all(), [
            'products' => 'required|array',
            'products.*.productId' => 'required|integer',
            'products.*.weightId' => 'nullable|integer',
            'products.*.count' => 'required|integer|min:1',
            'addressId' => 'required|integer',
            'deliveryMethodId' => 'required|integer',
            'discountCode' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->sendError('خطای اعتبارسنجی', [
                'message' => 'تمام اطلاعات مورد نیاز را وارد کنید. دقت کنید که فقط ایدی محصول و تعداد درخواستی بالای 1 عدد الزامیست. درصورتی که میخواهید موجودی محصول با ایدی گرم مورد نظر بررسی کنید ایدی گرم محصول را وارد کنید',
                'errors' => $validator->errors(),
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $products = $request->products;
        $addressId = $request->addressId;
        $deliveryMethodId = $request->deliveryMethodId;
        $discountCode = $request->discountCode;

        $totalAmount = 0;
        $errors = [];

        $responses = [];
        $productsData = [];
        $APP_TIDAMODE_SHOP_IMAGE_URL = env('APP_TIDAMODE_SHOP_IMAGE_URL');
        foreach ($products as $productItem) {

            $productId = unHashId($productItem['productId']);
            $weightId = isset($productItem['weightId']) ? unHashId($productItem['weightId']) : null;
            $countRequested = (int) $productItem['count'];

            if ($productId && ($weightId === null || $weightId == 0)) {
                // بررسی محصول با قیمت ثابت
                $product = Product::with('productCount', 'detail')->whereId($productId)->first();

                if ($product && $product->fixed_amount !== null) {
                    $countAvailable = (int) $product->getProductCount() - (int) $product->reserved_count;

                    if ($countAvailable > 0) {
                        $responses[] = [
                            'productId' => $productItem['productId'],
                            'message' => $countAvailable >= $countRequested ? 'تعداد درخواستی شما در انبار وجود دارد' : 'تنها '.$countAvailable.' عدد از این محصول در انبار موجود است',
                            'count' => min($countRequested, $countAvailable),
                            'hasCount' => true,
                        ];

                        if ($countAvailable < $countRequested) {
                            $errors[] = [
                                'productId' => $productItem['productId'],
                                'weightId' => $productItem['weightId'],
                                'message' => 'تنها '.$countAvailable.' عدد از این محصول در انبار موجود است',
                                'count' => min($countRequested, $countAvailable),
                                'hasCount' => false,
                            ];
                        } else {
                            $imageUrls = $product->gallery?->pluck('url')->take(1)->toArray();
                            $productsData[] = [
                                'title' => $product->title_fa,
                                'weight' => null,
                                'count' => $countRequested,
                                'amount' => (int) $countRequested * (float) str_replace(',', '', $product->fixed_amount) / 10,
                                'unit' => 'تومان',
                                'image' => $APP_TIDAMODE_SHOP_IMAGE_URL.$imageUrls[0],
                            ];
                        }

                        $totalAmount += (float) str_replace(',', '', $product->fixed_amount) * $countRequested;
                    } else {
                        $errors[] = [
                            'productId' => $productItem['productId'],
                            'message' => 'موجودی محصول مورد نظر صفر می‌باشد',
                            'count' => 0,
                            'hasCount' => false,
                        ];
                    }
                } else {
                    $errors[] = [
                        'productId' => $productItem['productId'],
                        'message' => 'محصول مورد نظر پیدا نشد یا موجودی ندارد، در صورتی که محصولی با قیمت ثابت باشد نیاز به وارد کردن ایدی وزن نیست',
                        'count' => 0,
                        'hasCount' => true,
                    ];
                }
            } elseif ($productId && $weightId !== null) {
                // بررسی محصول بر اساس وزن
                $productDetail = ProductDetail::whereId($weightId)->where('product_id', $productId)->first();

                if ($productDetail && $productDetail->fixed_amount === null) {
                    $countAvailable = (int) $productDetail->getProductDetailCount();

                    if ($countAvailable > 0) {
                        $responses[] = [
                            'productId' => $productItem['productId'],
                            'weightId' => $productItem['weightId'],
                            'message' => $countAvailable >= $countRequested ? 'تعداد درخواستی شما در انبار وجود دارد' : 'تنها '.$countAvailable.' عدد از این محصول در انبار موجود است',
                            'count' => min($countRequested, $countAvailable),
                            'hasCount' => $countAvailable >= $countRequested ? true : false,
                        ];

                        if ($countAvailable < $countRequested) {
                            $errors[] = [
                                'productId' => $productItem['productId'],
                                'weightId' => $productItem['weightId'],
                                'message' => 'تنها '.$countAvailable.' عدد از این محصول در انبار موجود است',
                                'count' => min($countRequested, $countAvailable),
                                'hasCount' => false,
                            ];
                        } else {
                            $imageUrls = $productDetail->product->gallery?->pluck('url')->take(1)->toArray();
                            $productsData[] = [
                                'title' => $productDetail->product->title_fa,
                                'weight' => $productDetail->weight,
                                'count' => $countRequested,
                                'amount' => (int) $countRequested * (float) str_replace(',', '', $productDetail->amount) / 10,
                                'unit' => 'تومان',
                                'image' => $APP_TIDAMODE_SHOP_IMAGE_URL.$imageUrls[0],
                            ];
                        }

                        $totalAmount += (float) str_replace(',', '', $productDetail->amount) * $countRequested;
                    } else {
                        $errors[] = [
                            'productId' => $productItem['productId'],
                            'weightId' => $productItem['weightId'],
                            'message' => 'موجودی محصول مورد نظر صفر می‌باشد',
                            'count' => 0,
                            'hasCount' => false,
                        ];
                    }
                } else {
                    $errors[] = [
                        'productId' => $productItem['productId'],
                        'weightId' => $productItem['weightId'],
                        'message' => 'محصول مورد نظر پیدا نشد یا اطلاعات وارد شده نادرست است',
                        'count' => 0,
                        'hasCount' => false,
                    ];
                }
            } else {
                $errors[] = [
                    // 'productId' => $productItem['productId'],
                    // 'weightId' => $productItem['weightId'],
                    'message' => 'در هنگام ارسال اطلاعات محصولات دقت کنید',
                    // 'count' => 0,
                    // 'hasCount' => false,
                ];
                // return $this->sendError('خطا در موجودی محصولات', ['message' => 'در هنگام ارسال اطلاعات محصولات دقت کنید'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

        }

        // return $this->sendResponse('لیست بررسی موجودی محصولات', ['products' => $responses]);

        // بررسی وجود خطا در موجودی محصولات
        // if (! empty($errors)) {
        //     return $this->sendError('خطا در موجودی محصولات', [
        //         'message' => $errors,
        //     ], Response::HTTP_UNPROCESSABLE_ENTITY);
        // }

        // // بررسی صحت آدرس
        $address = UserAddress::where('id', unHashId($addressId))->where('user_id', $user->id)->first();
        if (! $address || $address->user_id !== auth()->id()) {
            return $this->sendError('خطا در آدرس', [
                'message' => 'آدرس یافت نشد یا متعلق به کاربر نمی‌باشد.',
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        if (isset($deliveryMethodId) && $deliveryMethodId != null && $deliveryMethodId != 0) {

            $deliveryMethod = Delivery::whereId(unHashId($deliveryMethodId))->first();
            if (! $deliveryMethod) {
                return $this->sendError('خطا در نحوه ارسال', [
                    'message' => 'ایدی ارسال شده برای روش ارسال صحیح نمی باشد',
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            // // بررسی روش ارسال و موقعیت مکانی
            // $deliveryLocation = DeliveryLocation::where('delivery_id', $deliveryMethodId)
            //     ->where('province', $address->state_id)
            //     ->where('city', $address->city_id)
            //     ->first();
        } else {

            return $this->sendError('خطا در نحوه ارسال', [
                'message' => 'روش ارسال برای این مکان موجود نیست.',
            ], Response::HTTP_UNPROCESSABLE_ENTITY);

        }

        // // اعمال کد تخفیف در صورت وجود
        // if (isset($discountCode) && $discountCode != null && $discountCode != 0) {
        //     $discount = Discount::where('code', $discountCode)
        //         ->where('expiry_date', '>=', now())
        //         ->where('remaining_uses', '>', 0)
        //         ->first();

        //     if ($discount) {
        //         // بررسی استفاده کاربر از کد تخفیف
        //         $userDiscountUsed = UserDiscountUse::where('user_id', $user->id)
        //             ->where('discount_id', $discount->id)
        //             ->exists();

        //         if ($userDiscountUsed) {
        //             return $this->sendError('خطا در کد تخفیف', ['message' => 'شما قبلاً از این کد استفاده کرده‌اید.'], Response::HTTP_UNPROCESSABLE_ENTITY);
        //         }

        //         // اعمال تخفیف و به‌روزرسانی تعداد باقی‌مانده
        //         $totalAmount -= $discount->amount;
        //         $discount->decrement('remaining_uses');

        //         // ثبت استفاده از کد تخفیف
        //         UserDiscountUse::create([
        //             'user_id' => $user->id,
        //             'discount_id' => $discount->id,
        //             'used_at' => now(),
        //         ]);
        //     } else {
        //         return $this->sendError('خطا در کد تخفیف', ['message' => 'کد تخفیف معتبر نمی‌باشد.'], Response::HTTP_UNPROCESSABLE_ENTITY);
        //     }
        // }

        if (count($errors) > 0) {
            $data = [
                'errors' => $errors,

                // 'info' => [
                //     'fullname' => $address->fullname,
                //     'phone' => $address->phone,
                //     'address' => $address->address,
                // ],

                // 'products' => $responses,

            ];

            return $this->sendError('خطا در نحوه ارسال', [
                'message' => 'لطفا خطاهای زیر را رفع کنید سپس مجدد درخواست خود را ثبت نمایید',
                'errors' => $errors,
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $data = [
            'userId' => $user->id,
            'address' => $address->toArray(),
            'post' => $deliveryMethod->toArray(),
            'products' => $productsData,
            'responses' => $responses,
            'totalCheckout' => (float) ($totalAmount / 10),
            'totalFactor' => (float) ($totalAmount / 10) + (float) $deliveryMethod->base_amount,
        ];

        $response = SetOnlineShopOrderAction::handle($data);

        return $response;

        // $response = SetOnlineShopOrderAction::handle($data);

        // if ($response['status'] == true) {
        //     // // ارسال پاسخ موفقیت‌آمی
        //     return $this->sendResponse('سفارش با موفقیت ثبت شد', [
        //         'message' => 'سفارش شما با موفقیت ثبت شد',
        //         'description' => 'بدلیل نوسانات قیمت طلا ما مدت کوتاهی میتوانیم سفارشتون رو نگه داریم و در صورت عدم پرداخت ناچار به لغو اون هستیم',
        //         'orderId' => $response['HashInvoiceId'],
        //         'expires_at' => now()->addMinutes(120)->timestamp,
        //     ]);
        // }

        // return $this->sendError('خطا در نحوه ارسال', ['message' => 'روش ارسال برای این مکان موجود نیست.'], Response::HTTP_UNPROCESSABLE_ENTITY);

    }

    public function deliveryLocation()
    {
        $list = Delivery::select('id', 'title', 'base_amount')->get();

        return $this->sendResponse('لیست نحوه ارسال مرسوله ها', DeliveryResource::collection($list));
    }

    private function generatePaymentLink($invoice)
    {
        return route('payment.process', ['invoice' => $invoice->id]);
    }
}
