<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Models\Product;
use App\Models\ProductDetail;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;

class CartController extends BaseController
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * @authenticated
     */
    public function review(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'products' => 'required|array|min:1',
            'products.*.productId' => 'required|integer',
            'products.*.weightId' => 'nullable|integer',
            'products.*.count' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return $this->sendError('خطای اعتبارسنجی', [
                'message' => 'تمام اطلاعات مورد نیاز را وارد کنید. دقت کنید که فقط ایدی محصول و تعداد درخواستی بالای 1 عدد الزامیست. درصورتی که میخواهید موجودی محصول با ایدی گرم مورد نظر بررسی کنید ایدی گرم محصول را وارد کنید',
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $responses = [];
        foreach ($request->products as $productItem) {
            $productId = unHashId($productItem['productId']);
            $weightId = isset($productItem['weightId']) ? unHashId($productItem['weightId']) : null;
            $countRequested = (int) $productItem['count'];

            if ($productId && ($weightId === null || $weightId == 0)) {
                // بررسی محصول با قیمت ثابت
                $product = Product::with('productCount', 'detail')->whereId($productId)->first();

                if ($product && $product->fixed_amount !== null) {
                    $countAvailable = (int) $product->getProductCount() - (int) $product->reserved_count;

                    if ($countAvailable > 0) {
                        $responses[] = [
                            'productId' => $productItem['productId'],
                            'message' => $countAvailable >= $countRequested ? 'تعداد درخواستی شما در انبار وجود دارد' : 'تنها '.$countAvailable.' عدد از این محصول در انبار موجود است',
                            'count' => min($countRequested, $countAvailable),
                            'hasCount' => false,
                        ];
                    } else {
                        $responses[] = [
                            'productId' => $productItem['productId'],
                            'message' => 'موجودی محصول مورد نظر صفر می‌باشد',
                            'count' => 0,
                            'hasCount' => false,
                        ];
                    }
                } else {
                    $responses[] = [
                        'productId' => $productItem['productId'],
                        'message' => 'محصول مورد نظر پیدا نشد یا موجودی ندارد، در صورتی که محصولی با قیمت ثابت باشد نیاز به وارد کردن ایدی وزن نیست',
                        'count' => 0,
                        'hasCount' => false,
                    ];
                }
            } elseif ($productId && $weightId !== null) {
                // بررسی محصول بر اساس وزن
                $productDetail = ProductDetail::whereId($weightId)->where('product_id', $productId)->first();

                if ($productDetail && $productDetail->fixed_amount === null) {
                    $countAvailable = (int) $productDetail->getProductDetailCount();

                    if ($countAvailable > 0) {
                        $responses[] = [
                            'productId' => $productItem['productId'],
                            'weightId' => $productItem['weightId'],
                            'message' => $countAvailable >= $countRequested ? 'تعداد درخواستی شما در انبار وجود دارد' : 'تنها '.$countAvailable.' عدد از این محصول در انبار موجود است',
                            'count' => min($countRequested, $countAvailable),
                            'hasCount' => $countAvailable >= $countRequested ? true : false,
                        ];
                    } else {
                        $responses[] = [
                            'productId' => $productItem['productId'],
                            'weightId' => $productItem['weightId'],
                            'message' => 'موجودی محصول مورد نظر صفر می‌باشد',
                            'count' => 0,
                            'hasCount' => false,
                        ];
                    }
                } else {
                    $responses[] = [
                        'productId' => $productItem['productId'],
                        'weightId' => $productItem['weightId'],
                        'message' => 'محصول مورد نظر پیدا نشد یا اطلاعات وارد شده نادرست است',
                        'count' => 0,
                        'hasCount' => false,
                    ];
                }
            }
        }

        return $this->sendResponse('لیست بررسی موجودی محصولات', ['products' => $responses]);
    }
}
