<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\UserAddressResource;
use App\Models\UserAddress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class UserAddressController extends BaseController
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    public function index(Request $request)
    {
        $user = $request->user();

        return $this->sendResponse('پروفایل کاربر',
            UserAddressResource::collection($user->address),
        );
    }

    public function create(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'fullname' => 'required|string|min:3|max:255',
            'phone' => 'required|string|min:10|max:15',
            'address' => 'required|string|min:3|max:9999',
            'stateId' => 'required|integer',
            'cityId' => 'required|integer',
            'district' => 'required|string|min:2|max:255',
            'plaque' => 'required|string|min:1|max:10',
            'unit' => 'required|string|min:1|max:10',
            'zipcode' => 'required|string|min:1|max:10',
            'is_receiver' => 'required|boolean',

            'lat' => ['nullable', 'string', function ($attribute, $value, $fail) {
                if (! is_null($value) && (strlen($value) < 3 || strlen($value) > 20)) {
                    $fail('عرض جغرافیایی باید بین ۳ تا ۲۰ کاراکتر باشد.');
                }
            }],

            'lng' => ['nullable', 'string', function ($attribute, $value, $fail) {
                if (! is_null($value) && (strlen($value) < 3 || strlen($value) > 20)) {
                    $fail('طول جغرافیایی باید بین ۳ تا ۲۰ کاراکتر باشد.');
                }
            }],
        ], [
            'fullname.required' => 'نام و نام خانوادگی الزامی است.',
            'fullname.string' => 'نام و نام خانوادگی باید یک متن باشد.',
            'fullname.min' => 'نام و نام خانوادگی باید حداقل ۳ کاراکتر باشد.',
            'fullname.max' => 'نام و نام خانوادگی نباید بیشتر از ۲۵۵ کاراکتر باشد.',

            'phone.required' => 'شماره تلفن الزامی است.',
            'phone.string' => 'شماره تلفن باید یک متن باشد.',
            'phone.min' => 'شماره تلفن باید حداقل ۱۰ رقم باشد.',
            'phone.max' => 'شماره تلفن نمی‌تواند بیشتر از ۱۵ رقم باشد.',

            'address.required' => 'آدرس الزامی است.',
            'address.string' => 'آدرس باید یک متن باشد.',
            'address.min' => 'آدرس باید حداقل ۳ کاراکتر باشد.',
            'address.max' => 'آدرس نمی‌تواند بیشتر از ۹۹۹۹ کاراکتر باشد.',

            'stateId.required' => 'استان الزامی است.',
            'stateId.integer' => 'شناسه استان باید عدد باشد.',

            'cityId.required' => 'شهر الزامی است.',
            'cityId.integer' => 'شناسه شهر باید عدد باشد.',

            'district.required' => 'منطقه الزامی است.',
            'district.string' => 'منطقه باید یک متن باشد.',
            'district.min' => 'منطقه باید حداقل ۲ کاراکتر باشد.',
            'district.max' => 'منطقه نمی‌تواند بیشتر از ۲۵۵ کاراکتر باشد.',

            'plaque.required' => 'پلاک الزامی است.',
            'plaque.string' => 'پلاک باید یک متن باشد.',
            'plaque.min' => 'پلاک باید حداقل ۱ کاراکتر باشد.',
            'plaque.max' => 'پلاک نمی‌تواند بیشتر از ۱۰ کاراکتر باشد.',

            'unit.required' => 'واحد الزامی است.',
            'unit.string' => 'واحد باید یک متن باشد.',
            'unit.min' => 'واحد باید حداقل ۱ کاراکتر باشد.',
            'unit.max' => 'واحد نمی‌تواند بیشتر از ۱۰ کاراکتر باشد.',

            'zipcode.required' => 'کد پستی الزامی است.',
            'zipcode.integer' => 'کد پستی باید عدد باشد.',
            'zipcode.digits' => 'کد پستی باید دقیقاً ۱۰ رقم باشد.',

            'is_receiver.required' => 'مشخص کردن گیرنده الزامی است.',
            'is_receiver.boolean' => 'مقدار گیرنده باید درست یا نادرست باشد.',

            'lat.string' => 'عرض جغرافیایی باید یک متن باشد.',
            'lng.string' => 'طول جغرافیایی باید یک متن باشد.',
        ]);

        if ($validator->fails()) {
            return $this->sendError('خطای اعتبارسنجی', ['message' => 'تمام اطلاعات مورد نیاز را وارد کنید'], 422);
        }
        $user = $request->user();
        UserAddress::create([
            'fullname' => $request->is_receiver ?? $request->fullname,
            'phone' => $request->is_receiver ?? $request->phone,
            'user_id' => $user->id,
            'address' => $request->address,
            'stateId' => $request->stateId,
            'district' => $request->district,
            'plaque' => $request->plaque,
            'unit' => $request->unit,
            'cityId' => $request->cityId,
            'zipcode' => $request->zipcode,
            'is_receiver' => $request->is_receiver,
            'lat' => $request->lat,
            'lng' => $request->lng,

        ]);

        return $this->sendResponse('ثبت موفق', [
            'message' => 'اطلاعات با موفقیت ثبت شد',
        ]);
    }

    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'addressId' => 'required|integer',
            'fullname' => 'required|string|min:3|max:255',
            'phone' => 'required|string|min:10|max:15',
            'address' => 'required|string|min:3|max:9999',
            'stateId' => 'required|integer',
            'cityId' => 'required|integer',
            'district' => 'required|string|min:2|max:255',
            'plaque' => 'required|string|min:1|max:10',
            'unit' => 'required|string|min:1|max:10',
            'zipcode' => 'required|string|min:1|max:10',
            'is_receiver' => 'required|boolean',
            'lat' => 'string',
            'lng' => 'string',
        ]);

        if ($validator->fails()) {
            return $this->sendError('خطای اعتبارسنجی', ['message' => 'تمام اطلاعات مورد نیاز را وارد کنید'], 422);
        }
        $user = $request->user();

        UserAddress::whereId(unHashId($request->addressId))->where('user_id', $user->id)->update([
            'fullname' => $request->fullname,
            'phone' => $request->phone,
            'user_id' => $user->id,
            'address' => $request->address,
            'stateId' => $request->stateId,
            'district' => $request->district,
            'plaque' => $request->plaque,
            'unit' => $request->unit,
            'cityId' => $request->cityId,
            'zipcode' => $request->zipcode,
            'is_receiver' => $request->is_receiver,
            'lat' => $request->lat,
            'lng' => $request->lng,
        ]);

        return $this->sendResponse('ثبت موفق', [
            'message' => 'اطلاعات با موفقیت بروزرسانی شد',
        ]);
    }

    public function delete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'addressId' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return $this->sendError('خطای اعتبارسنجی', ['message' => 'آدرس مورد نظر را انتخاب کنید'], 422);
        }
        $user = $request->user();
        $address = UserAddress::whereId(unHashId($request->addressId))->where('user_id', $user->id)->first();
        if ($address) {
            $address->delete();

            return $this->sendResponse('ثبت موفق', [
                'message' => 'اطلاعات با موفقیت حذف شد',
            ]);
        }

        return $this->sendError('خطای اعتبارسنجی', ['message' => 'آدرس مورد نظر یافت نگردید یا برای کاربر جاری نمی باشد'], 422);
    }
}
