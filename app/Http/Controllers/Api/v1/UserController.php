<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Resources\UserResource;
use App\Models\User;
use <PERSON><PERSON><PERSON>rin\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use <PERSON><PERSON><PERSON><PERSON>\LivewireRateLimiting\WithRateLimiting;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class UserController extends BaseController
{
    use WithRateLimiting;

    public function __construct()
    {
        $this->middleware('auth:api');
    }

    #[Group('User')]
    public function profile(Request $request)
    {

        return $this->sendResponse('پروفایل کاربر', [
            new UserResource(Auth::user()),
        ]);

    }

    public function update(Request $request)
    {
        try {
            $user = $request->user();
            $validator = Validator::make($request->all(), [
                'fullname' => ['nullable', 'string', function ($attribute, $value, $fail) {
                    if (! is_null($value) && strlen($value) < 3) {
                        $fail('نام کامل باید حداقل ۳ کاراکتر باشد.');
                    }
                }, 'max:255'],

                'landline_number' => 'nullable',

                'national_id' => 'required|digits:10',

                'education' => ['nullable', 'string', function ($attribute, $value, $fail) {
                    if (! is_null($value) && strlen($value) < 3) {
                        $fail('تحصیلات باید حداقل ۳ کاراکتر باشد.');
                    }
                }, 'max:32'],

                'date_of_birth' => ['nullable', 'string', function ($attribute, $value, $fail) {
                    if (! is_null($value) && strlen($value) < 5) {
                        $fail('تاریخ تولد باید حداقل ۵ کاراکتر باشد.');
                    }
                }, 'max:20'],

                'email' => 'nullable|email',

                'job' => ['nullable', 'string', function ($attribute, $value, $fail) {
                    if (! is_null($value) && strlen($value) < 3) {
                        $fail('شغل باید حداقل ۳ کاراکتر باشد.');
                    }
                }, 'max:100'],

                'password' => ['nullable', 'string', function ($attribute, $value, $fail) {
                    if (! is_null($value) && strlen($value) < 8) {
                        $fail('رمز عبور باید حداقل ۸ کاراکتر باشد.');
                    }
                }],
            ], [
                'national_id.required' => 'کد ملی الزامی است.',
                'national_id.digits' => 'کد ملی باید دقیقا ۱۰ رقم باشد.',
                'email.email' => 'ایمیل وارد شده معتبر نیست.',
            ]);

            if ($validator->fails()) {
                return $this->sendError('خطای اعتبارسنجی', ['erros' => $validator->errors()], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            // بررسی تکراری بودن ایمیل
            if ($request->email !== $user->email) {
                $existingUser = User::where('email', $request->email)->first();
                if ($existingUser) {
                    return $this->sendError('خطای اعتبارسنجی', ['message' => 'پست الکترونیک تکراری می باشد، این پست الکترونیک برای شخص دیگری ثبت شده است'], Response::HTTP_UNPROCESSABLE_ENTITY);
                }

                $user->email = $request->email;
            }

            if (! isValidIranianNationalCode($request->national_id)) {
                return $this->sendError('خطای اعتبارسنجی', [
                    'message' => 'فرمت شماره ملی ثبت شده اشتباه می باشد لطفا اصلاح کنید',
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            // بروزرسانی اطلاعات
            $user->fullname = $request->fullname;
            $user->landline_number = $request->landline_number;
            $user->national_id = $request->national_id;
            $user->education = $request->education;
            $user->date_of_birth = $request->date_of_birth;
            $user->job = $request->job;
            $user->password = bcrypt($request->password);
            $user->save();

            return $this->sendResponse('موفقیت', [
                'message' => 'بروزرسانی اطلاعات با موفقیت انجام شد',
            ]);
        } catch (TooManyRequestsException $exception) {
            throw ValidationException::withMessages([
                'message' => "درخواست شما کمتر از 1 دقیقه پیش ارسال شده است، برای درخواست بعدی {$exception->secondsUntilAvailable} ثانیه صبر کنید",
            ]);
        }
    }
}
