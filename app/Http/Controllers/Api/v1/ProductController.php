<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Http\Requests\ProductIndexReques;
use App\Http\Resources\CommentResource;
use App\Http\Resources\ProductResource;
use App\Http\Resources\ProductsResource;
use App\Models\Category;
use App\Models\Product;
use App\Models\ProductDetail;
use DanHarrin\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use DanHarrin\LivewireRateLimiting\WithRateLimiting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ProductController extends BaseController
{
    use WithRateLimiting;
    // public function index(Request $request){

    //      $products = Product::latest()->paginate(3);

    //      $pagination = [
    //         'total_products' => $products->total(),
    //         // 'per_page' => $products->perPage(),
    //         'current_page' => $products->currentPage(),
    //         'last_page' => $products->lastPage(),
    //         'next_page_url' => $products->nextPageUrl(),
    //         'prev_page_url' => $products->previousPageUrl()
    //     ];

    //      return $this->sendResponse('لیست محصولات', [
    //          'products' => ProductsResource::collection($products),
    //          'pagination' => $pagination
    //      ]);
    // }

    /**
     * @unauthenticated
     */
    public function index(ProductIndexReques $request)
    {

        $page = $request->query('page', 1);
        $perPage = $request->query('per_page', 18);

        $categories = $request->query('categories');

        $min_price = $request->query('min_price');
        $max_price = $request->query('max_price');
        $sort = $request->query('sort');

        $query = Product::query();
        $query->with(['details', 'category', 'gallery', 'detail']);
        if ($categories) {

            $categories = explode(',', $categories);

            $categoryIds = Category::whereIn('title_eng', $categories)->pluck('id')->toArray();

            if (! empty($categoryIds)) {
                $query->whereIn('category_id', $categoryIds);
            }

        }

        if ($min_price != null && $max_price != null) {

            $query->where(function ($query) use ($min_price, $max_price) {
                $query->whereHas('details', function ($query) use ($min_price, $max_price) {
                    if ($min_price) {
                        // تبدیل min_price به ریال و اعمال فیلتر
                        $query->whereRaw('CAST(REPLACE(amount, \',\', \'\') AS FLOAT) >= ?', [$min_price * 10]);
                    }
                    if ($max_price) {
                        // تبدیل max_price به ریال و اعمال فیلتر
                        $query->whereRaw('CAST(REPLACE(amount, \',\', \'\') AS FLOAT) <= ?', [$max_price * 10]);
                    }
                })->orWhereBetween(DB::raw("CAST(REPLACE(fixed_amount, ',', '') AS UNSIGNED)"), [$min_price * 10, $max_price * 10]);
            });

        }

        if ($sort) {
            if ($sort == 'cheapest') {
                $query->whereHas('details', function ($query) {
                    $query->whereRaw('CAST(REPLACE(amount, \',\', \'\') AS FLOAT) >= 1000000');
                })->orderByRaw("
                    LEAST(
                        IFNULL(CAST(REPLACE(fixed_amount, ',', '') AS UNSIGNED), 99999999999999),
                        (SELECT MIN(CAST(REPLACE(amount, ',', '') AS FLOAT)) FROM product_details WHERE product_details.product_id = products.id)
                    ) ASC
                ");
            } elseif ($sort == 'most_expensive') {
                $query->whereHas('details', function ($query) {
                    $query->whereRaw('CAST(REPLACE(amount, \',\', \'\') AS FLOAT) >= 1000000');
                })->orderByRaw("
                    LEAST(
                        IFNULL(CAST(REPLACE(fixed_amount, ',', '') AS UNSIGNED), 99999999999999),
                        (SELECT MIN(CAST(REPLACE(amount, ',', '') AS FLOAT)) FROM product_details WHERE product_details.product_id = products.id)
                    ) DESC
                ");
            } elseif ($sort == 'proven') {
                $query->whereHas('details', function ($query) {
                    $query->havingRaw("fixed_amount IS NOT NULL AND fixed_amount != ''");
                });

            }
        } else {
            $query->latest();
        }

        $products = $query->latest()->paginate($perPage, ['*'], 'page', $page);

        $pagination = [
            'total_products' => $products->total(),
            'current_page' => $products->currentPage(),
            'last_page' => $products->lastPage(),
            'next_page_url' => $products->nextPageUrl(),
            'next_page_number' => $products->nextPageUrl() ? (int) $request->query('page', $products->currentPage() + 1) : null,
            'prev_page_url' => $products->previousPageUrl(),
            'prev_page_number' => $products->previousPageUrl() ? (int) $request->query('page', $products->currentPage() - 1) : null,
        ];

        // return $products;

        return $this->sendResponse('لیست محصولات', [
            'products' => ProductsResource::collection($products),
            'pagination' => $pagination,
        ]);
    }

    /**
     * @unauthenticated
     */
    public function show($slug)
    {

        $product = Product::with('details', 'gallery')->where('slug', $slug)->first();

        // return $product->toArray();
        if ($product) {
            return $this->sendResponse('جزئیات محصول',
                new ProductResource($product)
            );
        }

        return $this->sendError('پیدا نشدن محصول', ['message' => 'محصول مورد نظر پیدا نشد'], 422);
    }

    /**
     * @unauthenticated
     */
    public function categories(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'type' => 'nullable|string|in:menu,all,null', // nullable اضافه شد
        ]);

        // دریافت تمام دسته‌بندی‌ها
        $categories = Category::get()->map(function ($category) {
            return [
                'type' => $category->type,
                'title_eng' => $category->title_eng,
                'title_fa' => $category->title_fa,
                'image' => $category->image,
            ];
        })->toArray();

        // فیلتر کردن بر اساس نوع درخواستی
        $filteredCategories = match ($request->type) {
            'menu' => array_values(array_filter($categories, fn ($cat) => $cat['type'] === 'menu')),
            'null' => array_values(array_filter($categories, fn ($cat) => $cat['type'] === '')),
            'all', null => $categories, // هر دو حالت all و null همه دسته‌بندی‌ها را برمی‌گردانند
            default => $categories, // حالت پیش‌فرض برای احتیاط
        };

        return $this->sendResponse('دسته بندی محصولات', $filteredCategories);

    }

    /**
     * @unauthenticated
     */
    public function views(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'productId' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->sendError('خطای اعتبارسنجی', ['message' => 'تمام اطلاعات مورد نیاز را وارد کنید'], 422);
        }

        return $this->sendError('مشاهده محصول', ['message' => $request->all()], 200);

    }

    /**
     * @authenticated
     */
    public function review(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'productId' => 'required|integer',
            'weightId' => 'nullable|integer',
            'count' => 'required|integer|min:1',

        ]);

        if ($validator->fails()) {
            return $this->sendError('خطای اعتبارسنجی', ['message' => 'تمام اطلاعات مورد نیاز را وارد کنید. دقت کنید که فقط ایدی محصول و تعداد درخواستی بالای 1 عدد الزامیست. درصورتی که میخواهید موجودی محصول با ایدی گرم مورد نظر بررسی کنید ایدی گرم محصول را وارد کنید'], 422);
        }

        if ($request->productId != null && $request->weightId == null || $request->weightId == 0) {

            $product = Product::with('productCount', 'detail')->whereId(unHashId($request->productId))->first();

            if ($product && $product->fixed_amount != null) {

                $count = (int) $product->getProductCount() - (int) $product->reserved_count;

                if ($count != 0) {
                    if ($count >= $request->count) {
                        return $this->sendResponse('لیست محصولات', [
                            'message' => 'تعداد درخواستی شما در انبار وجود دارد',
                            'count' => $request->count,
                            'hasCount' => true,
                        ]);
                    }

                    return $this->sendResponse('موجود نبود محصول', [
                        'message' => 'درخواست شما '.$request->count.' می باشد که تنها فقط '.$count.' در گالری موجود می باشد',
                        'hasCount' => false,
                    ]);
                    // return $this->sendError('موجود نبودن', ['message' => 'تعداد موجودی محصول بر اساس وزن '.$product->detail->weight.' به تعداد '.$product->detail->count.' موجود است، درخواست شما بیشتر از تعداد موجودی می باشد'], 422);
                }

                return $this->sendResponse('موجود نبود محصول', [
                    'message' => 'موجودی محصول مورد نظر صفر می باشد',
                    'hasCount' => false,
                ]);

                // return $this->sendError('پیدا نشدن محصول', ['message' => 'جزئیات محصول مورد نظر پیدا نشد'], 422);
            }

            return $this->sendResponse('پیدا نشدن محصول', [
                'message' => 'محصول مورد نظر پیدا نشد. دقت بفرمایید اگر محصولی با قیمت ثابت باشد موجودی را به شما بازگشت میدهیم در غیر اینصورت بایستی ایدی وزن محصول را هم وارد کنید',
                'hasCount' => false,
            ]);
        }

        if ($request->productId != null && $request->weightId != null) {

            $productDetail = ProductDetail::whereId(unHashId($request->weightId))->where('product_id', unHashId($request->productId))->first();

            if ($productDetail && $productDetail->fixed_amount == null) {

                $count = (int) $productDetail->getProductDetailCount();

                if ($count != 0) {
                    if ($count >= $request->count) {
                        return $this->sendResponse('لیست محصولات', [
                            'message' => 'تعداد درخواستی شما در انبار وجود دارد',
                            'count' => (int) $request->count,
                            'hasCount' => true,
                        ]);
                    }

                    return $this->sendResponse('موجود نبود محصول', [
                        'message' => 'درخواست شما '.$request->count.' می باشد که تنها فقط '.$count.' در گالری موجود می باشد',
                        'hasCount' => false,
                    ]);
                    // return $this->sendError('موجود نبودن', ['message' => 'تعداد موجودی محصول بر اساس وزن '.$product->detail->weight.' به تعداد '.$product->detail->count.' موجود است، درخواست شما بیشتر از تعداد موجودی می باشد'], 422);
                }

                return $this->sendResponse('موجود نبود محصول', [
                    'message' => 'موجودی محصول مورد نظر صفر می باشد',
                    'hasCount' => false,
                ]);

                // return $this->sendError('پیدا نشدن محصول', ['message' => 'جزئیات محصول مورد نظر پیدا نشد'], 422);
            }

            return $this->sendResponse('پیدا نشدن محصول', [
                'message' => 'اگر ایدی محصول ثابت را وارد میکنید فیلد ایدی وزن محصول را خالی بگذارید که موجودی درستی به شما بازگشت بدهیم',
                'hasCount' => false,
            ]);
        }

    }

    /**
     * @unauthenticated
     */
    public function comments($productId)
    {
        $product = Product::whereId(unHashId($productId))->first();
        if ($product) {
            if ($product->comments) {

            }

            return $this->sendResponse('لیست کامنت ها', [
                'comments' => CommentResource::collection($product->comments),
                'count' => $product->comments->count(),
            ]);
        }

        return $this->sendError('پیدا نشدن محصول', ['message' => 'محصول مورد نظر پیدا نشد'], 422);
    }

    /**
     * @unauthenticated
     */
    public function comment_create(Request $request)
    {

        try {
            $this->rateLimit(1, 60);
            $validator = Validator::make($request->all(), [
                'productId' => 'required',
                'fullname' => 'required|string|min:3|max:999',
                'body' => 'required|string|min:5|max:999999',
            ]);

            if ($validator->fails()) {
                return $this->sendError('خطای اعتبارسنجی', ['message' => 'تمام اطلاعات مورد نیاز را وارد کنید'], 422);
            }

            if ($request->phone != null) {
                $phone = faTOen($request->phone);
                $phone = trim(preg_replace('/\s+/', '', $phone));
                if (! preg_match('/^09[0-9]{9}$/', $phone)) {
                    return $this->sendError('خطای اعتبارسنجی', ['message' => 'فرمت شماره موبایل اشتباه است'], 422);
                }
            }

            if ($request->email !== null) {
                if (! filter_var($request->email, FILTER_VALIDATE_EMAIL)) {
                    return $this->sendError('خطای اعتبارسنجی', ['message' => 'فرمت پست الکترونیک اشتباه است'], 422);
                }
            }

            $product = Product::whereId(unHashId($request->productId))->first();
            if (isset($product)) {
                $product->comments()->create([
                    'fullname' => $request->fullname,
                    'phone' => $request->phone ?? null,
                    'email' => $request->email ?? null,
                    'body' => $request->body,
                    'commentable_id' => $product->id,
                    'commentable_type' => 'App\Models\Product',
                ]);

                return $this->sendResponse('لیست کامنت ها', [
                    'message' => 'کامنت محصول با موفقیت ثبت شد',
                ]);
            }

            return $this->sendError('پیدا نشدن محصول', ['message' => 'محصول مورد نظر پیدا نشد'], 422);
        } catch (TooManyRequestsException $exception) {
            return $this->sendError('پیدا نشدن محصول', ['message' => "درخواست شما کمتر از 1 دقیقه پیش ارسال شده است، برای درخواست بعدی  {$exception->secondsUntilAvailable} ثانیه صبر کنید"], 429);
            // $this->exError = 'چند دقیقه ی دیگر درخواست بعدی خود را ثبت کنید';
            // throw ValidationException::withMessages([
            //     'message' => "درخواست شما کمتر از 1 دقیقه پیش ارسال شده است، برای درخواست بعدی  {$exception->secondsUntilAvailable} ثانیه صبر کنید",
            // ]);
        }
    }
}
