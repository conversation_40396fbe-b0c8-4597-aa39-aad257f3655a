<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Api\BaseController;
use App\Models\Token as UserToken;
use App\Models\User;
use Carbon\Carbon;
use <PERSON><PERSON><PERSON><PERSON>\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use <PERSON><PERSON><PERSON><PERSON>\LivewireRateLimiting\WithRateLimiting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Laravel\Passport\RefreshToken;
use Lara<PERSON>\Passport\Token as AccessToken;
use Symfony\Component\HttpFoundation\Response;

class AuthController extends BaseController
{
    use WithRateLimiting;

    public function __construct()
    {
        $this->middleware('auth:api', ['except' => ['auth', 'verification']]);
    }

    /**
     * @unauthenticated
     */
    public function auth(Request $request)
    {
        try {

            $this->rateLimit(20, 300);

            $validator = Validator::make($request->all(), [
                'phone' => 'required|digits:11',
            ]);

            if ($validator->fails()) {
                return $this->sendError('خطای اعتبارسنجی', ['message' => 'شماره موبایل را وارد کنید'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $phone = faTOen($request->phone);
            $phone = trim(preg_replace('/\s+/', '', $phone));
            if (! preg_match('/^09[0-9]{9}$/', $phone)) {
                return $this->sendError('خطای اعتبارسنجی', ['message' => 'فرمت شماره موبایل اشتباه است'], Response::HTTP_UNPROCESSABLE_ENTITY);
            }

            $token_check = UserToken::where('phone', $phone)->where('status', 0)->latest()->first();
            if ($token_check) {
                $currentTime = now();
                $tokenTime = $token_check->created_at;

                if (app()->environment('production')) {
                    if ($tokenTime->diffInMinutes($currentTime) > 3) {
                        return $this->createToken($request, $phone);
                    }

                    return $this->createToken($request, $phone, 'کمتر از 3 دقیقه پیامک برای شما ارسال شده است، برای درخواست بعدی 3 دقیقه پیش بایستی صبر کنید', false);
                } else {
                    if ($tokenTime->diffInMinutes($currentTime) > 3) {
                        return $this->createToken($request, $phone);
                    }

                    return $this->createToken($request, $phone, 'کمتر از 3 دقیقه پیامک برای شما ارسال شده است، برای درخواست بعدی 3 دقیقه پیش بایستی صبر کنید', false);
                }
                // return $this->sendError('تکرار اعتبارسنجی', [
                //     'message' => 'کمتر از 30 ثانیه پیامک برای شما ارسال شده است، برای درخواست بعدی 30 ثانیه پیش بایستی صبر کنید'],
                //     Response::HTTP_UNPROCESSABLE_ENTITY);

            }

            return $this->createToken($request, $phone, 'کمتر از 30 ثانیه پیامک برای شما ارسال شده است، برای درخواست بعدی 30 ثانیه پیش بایستی صبر کنید');
        } catch (TooManyRequestsException $exception) {
            return $this->sendError('خطای اعتبارسنجی', ['message' => "تلاش های زیادی برای ورود انجام شده، لطفاً  {$exception->secondsUntilAvailable} ثانیه صبر کنید و سپس دوباره اقدام به ورود کنید"], Response::HTTP_UNPROCESSABLE_ENTITY);
            // throw ValidationException::withMessages([
            //     'message' => "تلاش های زیادی برای ورود انجام شده، لطفاً  {$exception->secondsUntilAvailable} ثانیه صبر کنید و سپس دوباره اقدام به ورود کنید",
            // ]);
        }
    }

    private function createToken($request, $phone, $message = '', $sendToken = true)
    {

        if ($sendToken) {
            UserToken::where('phone', $phone)->update([
                'status' => 1,
            ]);

            $token_number = rand(10000, 99999);
            $token = str()->random(600);
            UserToken::create([
                'access_token' => $token,
                'token' => '12345',
                'phone' => $phone,
                // 'status' => 0,
                // 'source' => 'test',
                'ip' => $request->ip(),
                // 'agent' => $request->userAgent(),
                // 'header' => $request->header('X-Application-Token'),
            ]);

            return $this->sendResponse('ارسال کد اعتبارسنجی', [
                'message' => 'کدفعالسازی برای شماره موبایل ارسال شد',
                'token' => $token,
                'expire_time' => 180,
            ]);
        }

        $token = UserToken::where('phone', $phone)->where('status', 0)->latest()->first();
        if ($token) {
            $createdAtPlus3Minutes = \Carbon\Carbon::parse($token->created_at)->addMinutes(3);

            $remainingSeconds = max(now()->diffInSeconds($createdAtPlus3Minutes, false), 0);

            return $this->sendResponse('ارسال کد اعتبارسنجی', [
                'message' => $message,
                'token' => $token->access_token,
                'expire_time' => intval($remainingSeconds),
            ], Response::HTTP_OK, false);
        }

    }

    /**
     * @unauthenticated
     */
    public function verification(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'token' => 'required',
            'code' => 'required',
            'phone' => 'required',
        ]);

        if ($validator->fails()) {
            // @status 422
            return $this->sendError('خطای اعتبارسنجی', ['message' => 'تمام فیلدها را کامل پر کنید'], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $phone = faTOen($request->phone);
        $phone = trim(preg_replace('/\s+/', '', $phone));

        if (! preg_match('/^09[0-9]{9}$/', $phone)) {
            // @status 422
            return $this->sendError('خطای اعتبارسنجی', ['message' => 'فرمت شماره موبایل اشتباه است'], Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $token_check = UserToken::where('phone', $phone)->where('status', 0)->latest()->first();

        if ($token_check) {

            if ($token_check->access_token == $request->token && $token_check->token == $request->code) {

                $user = User::where('mobile', $phone)->latest()->first();
                if (! $user) {
                    $user = User::create([
                        'username' => 'username_'.$phone,
                        'mobile' => $phone,
                    ]);
                }

                if ($token_check) {
                    $currentTime = now();
                    $tokenTime = $token_check->created_at;

                    if ($tokenTime->diffInMinutes($currentTime) > 3) {
                        $token_check->status = 1;
                        $token_check->save();

                        // @status 422
                        return $this->sendError('تکرار اعتبارسنجی', [
                            'message' => 'این توکن و درخواست منقضی شده است'],
                            Response::HTTP_UNPROCESSABLE_ENTITY);
                    }

                }

                Auth::login($user, true);

                $tokens = $user->tokens->pluck('id');

                if ($tokens->isNotEmpty()) {
                    AccessToken::whereIn('id', $tokens)
                        ->update(['revoked' => true]);
                    RefreshToken::whereIn('access_token_id', $tokens)->update(['revoked' => true]);
                }

                $resultToken = $user->createToken('khalafiyar Token');

                $token_check->status = 1;
                $token_check->save();

                // @status 200
                return $this->sendResponse('اطلاعات درست می باشد', [
                    'message' => 'با موفقیت لاگین شدین',
                    'access_token' => $resultToken->accessToken,
                    'token_type' => 'Bearer',
                    'wallet' => $user?->wallet ?? 0,
                    'expire_at' => Carbon::parse($resultToken->token->expire_at)->timestamp,
                ])->cookie('access_token', $resultToken->accessToken, 1440, null, null, config('app.env') === 'production', true, 'Strict');

            }

            return $this->sendError('خطای اعتبارسنجی', ['message' => 'اطلاعات ارسال شده اشتباه می باشد'], Response::HTTP_BAD_REQUEST);
        }

        return $this->sendError('خطای اعتبارسنجی', ['message' => 'اطلاعات ارسال شده اشتباه می باشد'], Response::HTTP_BAD_REQUEST);
    }

    public function logout(Request $request)
    {
        $request->user()->token()->revoke();

        return $this->sendResponse('اطلاعات درست می باشد', [
            'message' => 'با موفقیت خروج کردین',
        ])->cookie(cookie()->forget('access_token'));

    }

    // public function refresh()
    // {
    //     return response()->json([
    //         'user' => Auth::user(),
    //         'authorisation' => [
    //             'token' => Auth::refresh(),
    //             'type' => 'bearer',
    //         ],
    //     ]);
    // }
}
