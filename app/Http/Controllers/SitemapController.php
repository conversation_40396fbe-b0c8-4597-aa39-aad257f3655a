<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Http;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

class SitemapController extends Controller
{
    public function index()
    {
        // ارسال درخواست به API و دریافت داده‌ها
        $response = Http::get('https://tidamode.iran.liara.run/api/v1/products');

        // بررسی وضعیت پاسخ
        if ($response->successful()) {
            // فرض کنید که داده‌های API لیستی از محصولات باشد
            $products = $response->json();

            // ساخت یک شیء Sitemap
            $sitemap = Sitemap::create();

            foreach ($products['data']['products'] as $product) {
                // اضافه کردن هر محصول به سایت مپ
                $sitemap->add(Url::create("https://shop.tidamode.ir/product/{$product['slug']}"));
            }

            // ذخیره سایت مپ در فایل
            $sitemap->writeToFile(storage_path('app/public/sitemap.xml'));

            $filePath = storage_path('app/public/sitemap.xml');

            // ارسال فایل به کاربر
            return response()->file($filePath);

            return response()->json(['message' => 'Sitemap created successfully']);
        }

        // در صورت عدم موفقیت در دریافت داده‌ها از API
        return response()->json(['error' => 'Failed to fetch products from API'], 500);
    }
}
