<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArticlesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => hashId($this->id),
            'author' => $this->user->fullname,
            'title' => $this->title,
            'slug' => $this->slug,
            'category_eng' => $this->category_eng,
            'category_fa' => $this->category_fa,
            'summary' => $this->summary,
        ];
    }
}
