<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserAddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => hashId($this->id),
            'address' => $this->address,
            'district' => $this->district,
            'plaque' => $this->plaque,
            'unit' => $this->unit,
            'is_receiver' => $this->is_receiver == 1 ? true : false,
            'state' => $this->state($this->state_id),
            'city' => $this->city($this->city_id),
            'zipcode' => $this->zipcode,
            'fullname' => $this->fullname,
            'phone' => $this->phone,
        ];
    }

    public function state($stateId)
    {
        return 'خوزستان';
    }

    public function city($cityId)
    {
        return  'اهواز';
    }
}
