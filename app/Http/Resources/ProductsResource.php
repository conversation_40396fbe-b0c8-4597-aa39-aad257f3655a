<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $url_asset = env('APP_ASSET_URL');

        $count = 0;
        if ($this->fixed_amount != null) {
            $count = (int) $this->getProductCount();
        } elseif ($this->detail) {
            $count = $this->detail?->getProductDetailCount();
        }

        return [
            'id' => hashId($this->id),
            'title' => $this->title_fa,
            'title_eng' => $this->title_eng,
            'slug' => $this->slug,
            'category' => $this->category->title_fa,
            'category_eng' => $this->category->title_eng,
            'count' => $count < 0 ? 0 : $count,
            // 'gold18k' => $gold18k,
            // 'amount_rial' => $this->fixed_amount !== null && $this->fixed_amount !== ''
            // ? (float) str_replace(',', '', $this->fixed_amount)
            // : ($this->details->first() && $this->details->first()->amount !== null && $this->details->first()->amount !== ''
            //     ? (float) str_replace(',', '', $this->details->first()->amount)
            //     : null),
            'amount' => $this->fixed_amount !== null && $this->fixed_amount !== ''
                        ? (float) str_replace(',', '', $this->fixed_amount) / 10
                        : ($this->details->first() && $this->details->first()->amount !== null && $this->details->first()->amount !== ''
                            ? (float) str_replace(',', '', $this->details->first()->amount) / 10
                            : null),
            'unit' => 'تومان',
            'image' => $this->gallery->first() ? [
                'url' => $url_asset.$this->gallery->first()->url,
                'format' => $this->gallery->first()->format,
            ] : null,
        ];
    }
}
