<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        $url_asset = env('APP_ASSET_URL');

        $count = 0;
        if ($this->fixed_amount != null) {
            $count = (int) $this->getProductCount() - (int) $this->reserved_count;
        } else {
            $count = $this->detail->getProductDetailCount() - (int) $this->detail->reserved_count;
        }

        return [
            'id' => hashId($this->id),
            'title' => $this->title_fa,
            'title_eng' => $this->title_eng,
            'slug' => $this->slug,
            'category_fa' => $this->category->title_fa,
            'category_eng' => $this->category->title_eng,
            'cutie' => $this->fixed_amount == null ? (int) $this->detail->cutie : null,
            // 'type_fa' => $this->type_fa,
            // 'type_eng' => $this->type_eng,
            'color_fa' => $this->detail?->color?->value,
            'color_eng' => $this->detail?->color?->key,
            'for' => $this->for?->value,
            'amount' => $this->fixed_amount != null
            ? (float) str_replace(',', '', $this->fixed_amount) / 10
            : ($this->details->first() ? (float) str_replace(',', '', $this->details->first()->amount) / 10 : 0),
            'unit' => 'تومان',
            'description' => $this->description,
            'meta_description' => $this->meta_description,
            'meta_keywords' => $this->meta_keywords,
            'count' => $count,
            'gender' => 'طلا',
            'collection' => '',
            'type' => '',
            'gallery' => $this->gallery->isNotEmpty()
            ? $this->gallery->map(function ($item) use ($url_asset) {
                return [
                    'url' => $url_asset.$item->url,
                    'format' => $item->format,
                ];
            })
            : [],

            'weights' => $this->fixed_amount == null
            ? $this->details->map(function ($item) {
                return [
                    'id' => hashId($item->id),
                    'weight' => $item->weight,
                    'count' => $item->ProductCounts->sum('count'),
                    'amount' => (float) str_replace(',', '', $item->amount) / 10,
                    'unit' => 'تومان',

                ];
            }) : [],
        ];
    }
}
