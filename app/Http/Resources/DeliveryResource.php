<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeliveryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => hashId($this->id),
            'title' => $this->title,
            'amount' => (float) str_replace(',', '', $this->base_amount),
            'unit' => 'تومان',
            // 'image' => '',
        ];
    }
}
