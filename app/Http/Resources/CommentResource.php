<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CommentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'fullname' => $this->fullname,
            'body' => $this->body,
            // 'image' => $this->user->avatar,
            'created_at_ago' => get_ago($this->created_at),
            'created_at_date' => shamsiDate($this->created_at),
        ];
    }
}
