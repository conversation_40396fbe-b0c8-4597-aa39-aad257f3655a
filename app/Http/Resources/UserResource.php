<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'fullname' => $this->fullname,
            'phone' => $this->mobile,
            'landline_number' => $this->landline_number ?? null,
            'national_id' => $this->national_id ?? null,
            'education' => $this->education,
            'date_of_birth' => $this->date_of_birth,
            'job' => $this->job,
            'email' => $this->email,
            'wallet' => 0,
        ];

    }
}
