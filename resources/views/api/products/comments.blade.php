<div
    class="mb-4 rounded-lg bg-white p-5 shadow-md"
    id="product-comments"
>

    <div
        class="mb-3 flex flex-col gap-3 rounded-md bg-gray-100 p-3"
        dir="rtl"
    >
        <p class="text-base font-bold">برگشت کامنت های ثبت شده برای محصول</p>
    </div>
    <div
        class="mb-6 items-center gap-3 md:flex"
        style="font-family: tahoma !important"
    >
        <h3 class="w-20 rounded-lg bg-green-500 px-3 py-1 text-white">[ GET ]</h3>
        <p class="font-semibold">/api/v1/product/<span class="text-red-500">13563</span>/comments</p>
    </div>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Success Response:</p>
    <pre class="rounded"><code>{
    "success": true,
    "data": {
        "comments": [
            {
                "fullname": "سیدعلی موسوی",
                "body": "این یک کامنت تست می باشد",
                "image": "https://tidamode.iran.liara.run/assets/images/avatar.jpg"
            },
            {
                "fullname": "سپهر کلانتریان",
                "body": "این کامنت برای سپهر است، تست است",
                "image": "https://tidamode.iran.liara.run/assets/images/avatar.jpg"
            }
        ],
        "count": 2
    },
    "status": 200
}</code></pre>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >NotFound Response:</p>
    <pre class="rounded"><code>{
    "success": false,
    "data": {
        "message": "محصول مورد نظر پیدا نشد"
    },
    "status": 422
}</code></pre>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Count Response:</p>
    <pre class="rounded"><code>{
    "success": true,
    "data": {
        "comments": [],
        "count": 0
    },
    "status": 200
}</code></pre>

</div>

<div
    class="mb-4 rounded-lg bg-white p-5 shadow-md"
    id="create-comment-product"
>

    <div
        class="mb-3 flex flex-col gap-3 rounded-md bg-gray-100 p-3"
        dir="rtl"
    >
        <p class="text-base font-bold">ایجاد کامنت برای محصول، ایدی محصول بعلاوه سایر پارمترها که در زیر آورده شده
            است ارسال کنید</p>
        <p class="text-base">- فقط سه پارمتر اولی الزامیست مابقی اختیاری</p>
    </div>
    <div
        class="mb-6 items-center gap-3 md:flex"
        style="font-family: tahoma !important"
    >
        <h3 class="w-24 rounded-lg bg-red-500 px-3 py-1 text-white">[ POST ]</h3>
        <p class="font-semibold">/api/v1/product/comment/create</p>
    </div>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Request:</p>
    <pre class="rounded"><code>{
    "productId" : "13563",
    "fullname" : "سیدعلی موسوی",
    "body" : "این یک تست کامنت می باشد"
    "phone" : "09215831866",
    "email" : "<EMAIL>",
}</code></pre>
    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Success Response:</p>
    <pre class="rounded"><code>{
    "success": true,
    "data": {
        "message": "کامنت محصول با موفقیت ثبت شد"
    },
    "status": 200
}</code></pre>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Validation Response 1:</p>
    <pre class="rounded"><code>{
    "success": false,
    "data": {
        "message": "فرمت شماره موبایل اشتباه است"
    },
    "status": 422
}</code></pre>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Validation Response 2:</p>

    <pre class="rounded"><code>{
    "success": false,
    "data": {
        "message": "فرمت پست الکترونیک اشتباه است"
    },
    "status": 422
}</code></pre>
</div>
