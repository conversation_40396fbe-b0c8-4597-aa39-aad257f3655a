<div
    class="mb-4 rounded-lg bg-white p-5 shadow-md"
    id="address"
>

    <div
        class="mb-3 flex flex-col gap-3 rounded-md bg-gray-100 p-3"
        dir="rtl"
    >
        <p class="text-base font-bold">دریافت آدرس های یک کاربر ، دقت شود لیست استان ها و شهرها بهمراه آیدی هاشون
            بایستی بصورت جیسون در دل پروژه نکست قرار بگیرد</p>
        <p class="text-base font-bold">لیست شهرها و استان ها بصورت جیسون توی دل پروژه تون قرار بدین و از این دیتا
            استفاده کنید ایدی استان و شهرستان ها در سمت سرور از این فایل خوانده و نمایش داده می شود <a
                class="text-base text-blue-500"
                href="/storage/province_cities.sql"
                target="_blank"
            >لینک دانلود</a></p>
    </div>
    <div
        class="mb-6 items-center gap-3 md:flex"
        style="font-family: tahoma !important"
    >
        <span class="rounded-lg bg-green-500 px-3 py-1 text-white">[ GET ]</span>
        <p class="font-semibold">/api/v1/user/address</p>
    </div>
    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Header Request:</p>
    <pre class="rounded"><code>{
    "authorization" : "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************MdtFZx20Mr_t5bKq3L_5HBS0a3gn3Aw0g-Kq3FwkZM8bmxYh3L9vZLl0r2ZRBbOlxozQVAtD52HDAV4AR80IbiE2mH_-lwSmX02-8TqNOhoxOM3W--sqT2i6DXFt32mVUsRxtO7Cz2mD6Y_hgEK3w7wUgrYRyH4Pdm0p66Q4lBX6D5i_-81HY1d138VtNyne-r32jiIzdoxkcK1KAlzR8-8eCOA4PUi4fGlkXnC3LpG5Pf_-hH8V3zru6K1okFxKgFbqN_-hAPZBPvufqsM0QR0oXxSKcKXQm9klTu6WnKpdxqJCMijogI8kaNqftmYeGpwbGbHX-Y6UPGBFxu0tAp7unWfBboW1mg7NbJwXEFGxnuv5mZ1oHhv4OQU"
}</code></pre>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Success Response:</p>
    <pre class="rounded"><code>{
  "success": true,
  "data": [

      {
        "id": 12821, // ایدی آدرس در هنگام ساخت لینک پرداخت نیاز است
        "address": "اصفهان فولادشهر",
        "district": "dsadsadasdsadsda",  // محله (ناحیه)
        "plaque": "4324", // پلاک
        "unit": 4324, // واحد
        "is_receiver": false, // گیرنده خودم هستم یا خیر
        "state": "خوزستان",
        "city": "اهواز",
        "zipcode": "1234567890", // کدپستی
        "fullname": "", // نام گیرنده
        "phone": "" // شماره تماس گیرنده
      }

  ],
  "status": 200
}</code></pre>

</div>

<div
    class="mb-4 rounded-lg bg-white p-5 shadow-md"
    id="create-address"
>

    <div
        class="mb-3 flex flex-col gap-3 rounded-md bg-gray-100 p-3"
        dir="rtl"
    >
        <p class="text-base font-bold">ایجاد آدرس برای کاربر، آیتم های مورد نظر همراه کوکی اکسس توکن ارسال شود،
            بایستی ایدی استان و شهرها بصورت جیسون در دل پروژه نکست قرار گیرد، در سمت سرور ایدی ذخیره می شود، در
            بازگشت آدرس استان و شهر بصورت حروف بازگشت داده می شود نه عدد</p>
        <p class="text-base font-bold">لیست شهرها و استان ها بصورت جیسون توی دل پروژه تون قرار بدین و از این دیتا
            استفاده کنید ایدی استان و شهرستان ها در سمت سرور از این فایل خوانده و نمایش داده می شود <a
                class="text-base text-blue-500"
                href="/storage/province_cities.sql"
                target="_blank"
            >لینک دانلود</a></p>
    </div>
    <div
        class="mb-6 items-center gap-3 md:flex"
        style="font-family: tahoma !important"
    >
        <span class="rounded-lg bg-red-500 px-3 py-1 text-white">[ POST ]</span>
        <p class="font-semibold">/api/v1/user/address/create</p>
    </div>
    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Header Request:</p>
    <pre class="rounded"><code>{
    "authorization" : "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************MdtFZx20Mr_t5bKq3L_5HBS0a3gn3Aw0g-Kq3FwkZM8bmxYh3L9vZLl0r2ZRBbOlxozQVAtD52HDAV4AR80IbiE2mH_-lwSmX02-8TqNOhoxOM3W--sqT2i6DXFt32mVUsRxtO7Cz2mD6Y_hgEK3w7wUgrYRyH4Pdm0p66Q4lBX6D5i_-81HY1d138VtNyne-r32jiIzdoxkcK1KAlzR8-8eCOA4PUi4fGlkXnC3LpG5Pf_-hH8V3zru6K1okFxKgFbqN_-hAPZBPvufqsM0QR0oXxSKcKXQm9klTu6WnKpdxqJCMijogI8kaNqftmYeGpwbGbHX-Y6UPGBFxu0tAp7unWfBboW1mg7NbJwXEFGxnuv5mZ1oHhv4OQU"
}</code></pre>
    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Request:</p>
    <pre class="rounded"><code>{
    "fullname": "سیدعلی",
    "phone": "09397192230",
    "address": "اصفهان - دروازه شیراز - دانشگاه اصفهان",
    "stateId": 0, // ایدی استان (لیست sql استان و شهرها به شما داده شده است در گروه و به ... تحویل داده شد)
    "cityId": 0, // ایدی شهر (لیست sql استان و شهرها به شما داده شده است در گروه و به ... تحویل داده شد)
    "district": "", // واحد (ناحیه)
    "plaque": "", // پلاک
    "unit": "", // واحد
    "zipcode": "", // کدپستی
    "is_receiver": true, // گیرنده خودم هستم یا خیر
    "lat": "string",  // نقشه lat
    "lng": "string" // نقشه lng
}</code></pre>
    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Success Response:</p>
    <pre class="rounded"><code>{
    "success": true,
        "data": {
        "message": "اطلاعات با موفقیت ثبت شد"
    },
    "status": 200
}</code></pre

            class="my-2 font-bold"
            style="font-family: tahoma !important"
            >Validation Response:</p>
            <pre class="rounded"><code>{
    "success": false,
        "data": {
        "message": "تمام اطلاعات مورد نیاز را وارد کنید"
    },
    "status": 422
}</code></pre>
</div>

<div
    class="mb-4 rounded-lg bg-white p-5 shadow-md"
    id="update-address"
>

    <div
        class="mb-3 flex flex-col gap-3 rounded-md bg-gray-100 p-3"
        dir="rtl"
    >
        <p class="text-base font-bold">بروزرسانی اطلاعات آدرس، فیلدهای الزامی در زیر را بهمراه آیدی از قبل هش شده
            توسط سرور بهمراه تغییرات سمت سرور ارسال شود</p>
        <p class="text-base font-bold">لیست شهرها و استان ها بصورت جیسون توی دل پروژه تون قرار بدین و از این دیتا
            استفاده کنید ایدی استان و شهرستان ها در سمت سرور از این فایل خوانده و نمایش داده می شود <a
                class="text-base text-blue-500"
                href="/storage/province_cities.sql"
                target="_blank"
            >لینک دانلود</a></p>
    </div>
    <div
        class="mb-6 items-center gap-3 md:flex"
        style="font-family: tahoma !important"
    >
        <span class="rounded-lg bg-orange-500 px-3 py-1 text-white">[ PUT ]</span>
        <p class="font-semibold">/api/v1/user/address/update</p>
    </div>
    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Header Request:</p>
    <pre class="rounded"><code>{
    "authorization" : "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************MdtFZx20Mr_t5bKq3L_5HBS0a3gn3Aw0g-Kq3FwkZM8bmxYh3L9vZLl0r2ZRBbOlxozQVAtD52HDAV4AR80IbiE2mH_-lwSmX02-8TqNOhoxOM3W--sqT2i6DXFt32mVUsRxtO7Cz2mD6Y_hgEK3w7wUgrYRyH4Pdm0p66Q4lBX6D5i_-81HY1d138VtNyne-r32jiIzdoxkcK1KAlzR8-8eCOA4PUi4fGlkXnC3LpG5Pf_-hH8V3zru6K1okFxKgFbqN_-hAPZBPvufqsM0QR0oXxSKcKXQm9klTu6WnKpdxqJCMijogI8kaNqftmYeGpwbGbHX-Y6UPGBFxu0tAp7unWfBboW1mg7NbJwXEFGxnuv5mZ1oHhv4OQU"
}</code></pre>
    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Request:</p>
    <pre class="rounded"><code>{
    "addressId": 0, // ایدی آدرس که قراره بروزرسانی کنیم
    "fullname": "سیدعلی موسوی",
    "phone": "09215831866",
    "address": "اصفهان - فولادشهر",
    "stateId": 0, // ایدی استان (لیست sql استان و شهرها به شما داده شده است در گروه و به ... تحویل داده شد)
    "cityId": 0, // ایدی شهر (لیست sql استان و شهرها به شما داده شده است در گروه و به ... تحویل داده شد)
    "district": "", // واحد (ناحیه)
    "plaque": "", // پلاک
    "unit": "", // واحد
    "zipcode": "", // کدپستی
    "is_receiver": true, // گیرنده خودم هستم یا خیر
    "lat": "string",  // نقشه lat
    "lng": "string" // نقشه lng
}</code></pre>
    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Success Response:</p>
    <pre class="rounded"><code>{
    "success": true,
        "data": {
        "message": "اطلاعات با موفقیت بروزرسانی شد"
    },
    "status": 200
}</code></pre>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Validation Response:</p>
    <pre class="rounded"><code>{
    "success": false,
        "data": {
        "message": "تمام اطلاعات مورد نیاز را وارد کنید"
    },
    "status": 422
}</code></pre>
</div>

<div
    class="mb-4 rounded-lg bg-white p-5 shadow-md"
    id="remove-address"
>

    <div
        class="mb-3 flex flex-col gap-3 rounded-md bg-gray-100 p-3"
        dir="rtl"
    >
        <p class="text-base font-bold">برای حذف آدرس، کافیست آیدی آدرس بعلاوه کوکی اکسس توکن همراه درخواست ارسال
            شود
        </p>
        <p class="text-base font-bold">لیست شهرها و استان ها بصورت جیسون توی دل پروژه تون قرار بدین و از این دیتا
            استفاده کنید ایدی استان و شهرستان ها در سمت سرور از این فایل خوانده و نمایش داده می شود <a
                class="text-base text-blue-500"
                href="/storage/province_cities.sql"
                target="_blank"
            >لینک دانلود</a></p>
    </div>
    <div
        class="mb-6 items-center gap-3 md:flex"
        style="font-family: tahoma !important"
    >
        <h3 class="w-28 rounded-lg bg-red-500 px-3 py-1 text-white">[ DELETE ]</h3>
        <p class="font-semibold">/api/v1/user/address/delete</p>
    </div>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Header Request:</p>
    <pre class="rounded"><code>{
    "authorization" : "Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************MdtFZx20Mr_t5bKq3L_5HBS0a3gn3Aw0g-Kq3FwkZM8bmxYh3L9vZLl0r2ZRBbOlxozQVAtD52HDAV4AR80IbiE2mH_-lwSmX02-8TqNOhoxOM3W--sqT2i6DXFt32mVUsRxtO7Cz2mD6Y_hgEK3w7wUgrYRyH4Pdm0p66Q4lBX6D5i_-81HY1d138VtNyne-r32jiIzdoxkcK1KAlzR8-8eCOA4PUi4fGlkXnC3LpG5Pf_-hH8V3zru6K1okFxKgFbqN_-hAPZBPvufqsM0QR0oXxSKcKXQm9klTu6WnKpdxqJCMijogI8kaNqftmYeGpwbGbHX-Y6UPGBFxu0tAp7unWfBboW1mg7NbJwXEFGxnuv5mZ1oHhv4OQU"
}</code></pre>
    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Request:</p>
    <pre class="rounded"><code>{
    "addressId" : 1234
}</code></pre>
    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Success Response:</p>
    <pre class="rounded"><code>{
    "success": true,
    "data": {
        "message": "اطلاعات با موفقیت حذف شد"
    },
    "status": 200
}</code></pre>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Validation Response 1:</p>
    <pre class="rounded"><code>{
    "success": false,
    "data": {
        "message": "آدرس مورد نظر را انتخاب کنید"
    },
    "status": 422
}</code></pre>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Validation Response 2:</p>

    <pre class="rounded"><code>{
    "success": false,
    "data": {
        "message": "آدرس مورد نظر یافت نگردید یا برای کاربر جاری نمی باشد"
    },
    "status": 422
}</code></pre>
</div>
