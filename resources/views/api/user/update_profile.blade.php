<div
    class="mb-4 rounded-lg bg-white p-5 shadow-md"
    id="user-update"
>
    <div
        class="mb-3 flex flex-col gap-3 rounded-md bg-gray-100 p-3"
        dir="rtl"
    >
        <p class="text-base font-bold">بروزرسانی اطلاعات کاربر، کوکی اکسس توکن بصورت خودکار همراه درخواست ارسال شود
        </p>
    </div>
    <div
        class="mb-6 items-center gap-3 md:flex"
        style="font-family: tahoma !important"
    >
        <span class="rounded-lg bg-red-500 px-3 py-1 text-white">[ POST ]</span>
        <p class="font-semibold">/api/v1/user/update</p>
    </div>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Header Request:</p>
    <pre class="rounded"><code>{
    "authorization" : "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.eyJhdWQiOiI5Y2JjNzZlNS1mNWIwLTRlYWMtYTRlOC0wMzMyYTE2OTFiMTAiLCJqdGkiOiIyZmRiMzdhNTgxMzFiMzVmZWY5MGY2YmI0MTdlNjY5ODhlZWIyZmExMjk2MWZmYWM1ZWI3ZGQ3YzI2NTRlODRhMDUxN2U5NmY2NTI1NGQxMCIsImlhdCI6MTcyMzQyMDIxMy4zMTg0MDYsIm5iZiI6MTcyMzQyMDIxMy4zMTg0MTEsImV4cCI6MTc1NDk1NjIxMy4yOTMzNDMsInN1YiI6IjY0Iiwic2NvcGVzIjpbXX0.5G-eQcvhc3GaApamYUHhXC9XfjwPe6JIIqo_usG61kGOP4jXLZ5SVkbPTKMP_VejKc2u8JSvQiJjhLD_2yrJaF_FgwqPvqegv3BDxxY-OF5yTikWVp7PMtxbCqa3ehRr1HvkkH7VW7iRNp3wEgyfkHnAYYG_113rlT44uZkepqY8jOM_qx8uO9vzVMcmIfPTRhGKCnvr7G4bJmLklmg7pSMjjQnRaAMZIO8X7mQy2GMdtFZx20Mr_t5bKq3L_5HBS0a3gn3Aw0g-Kq3FwkZM8bmxYh3L9vZLl0r2ZRBbOlxozQVAtD52HDAV4AR80IbiE2mH_-lwSmX02-8TqNOhoxOM3W--sqT2i6DXFt32mVUsRxtO7Cz2mD6Y_hgEK3w7wUgrYRyH4Pdm0p66Q4lBX6D5i_-81HY1d138VtNyne-r32jiIzdoxkcK1KAlzR8-8eCOA4PUi4fGlkXnC3LpG5Pf_-hH8V3zru6K1okFxKgFbqN_-hAPZBPvufqsM0QR0oXxSKcKXQm9klTu6WnKpdxqJCMijogI8kaNqftmYeGpwbGbHX-Y6UPGBFxu0tAp7unWfBboW1mg7NbJwXEFGxnuv5mZ1oHhv4OQU"
}</code></pre>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Request:</p>
    <pre class="rounded"><code>{
    "fullname": "سیدعلی موسوی",
    "landline_number": "06142824414", // اختیاریه
    "national_id": "5269785927", //اختیاریه ولی سعی کنید بگیرید چون برای ساخت لینک پرداخت فروشگاه الزامی میشه ولی برای بروزرسانی پروفایل اختیاری قرار داده شده
    "education": "کارشناسی ارشد",
    "date_of_birth": "1366/03/29", // اختیاریه ولی به نحوه و مرتب بودن دقت کنید
    "email": "<EMAIL>", // اختیاریه ولی اگر فرستادی بررسی میشه تکراری نباشه
    "job": "برنامه نویس",
    "password": "12345789" // این رو از کاربر بگیرید برای دفعات بعدی لاگین ممکنه سیستم دیگه پیامک نده و از شما بپرسه که رمز عبورت چیه اگر یادش رفت بوده بایستی بازیابی پیامکی بزنه این فیچر آینده س ولی کارهای لازم صورت گرفته مثه همین که رمزعبور برای خودش تعریف کنه و..
}</code></pre>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Success Response:</p>
    <pre class="rounded"><code>{
    "success": true,
    "data": {
        "message": "بروزرسانی اطلاعات با موفقیت انجام شد"
    },
    "status": 200
}</code></pre>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Error Response:</p>
    <pre class="rounded"><code>{
  "success": false,
  "data": {
    "message": "دسترسی غیرمجاز. لطفاً ابتدا وارد شوید."
  },
  "status": 401
}</code></pre>

    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Validation Response:</p>
    <pre class="rounded"><code>{
    "success": false,
    "data": {
        "message": "تمام فیلدها را کامل پر کنید"
    },
    "status": 422
}

</code></pre>
    <p
        class="my-2 font-bold"
        style="font-family: tahoma !important"
    >Validation Response:</p>
    <pre class="rounded"><code>{
  "success": false,
  "data": {
    "erros": {
      "national_id": [
        "کد ملی باید دقیقا ۱۰ رقم باشد."
      ],
      "password": [
        "رمز عبور باید حداقل ۸ کاراکتر باشد."
      ]
    }
  },
  "status": 422
}

</code></pre>
</div>
