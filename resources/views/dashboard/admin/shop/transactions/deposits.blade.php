@extends('layouts.dashboard')

@section('title', ' - لیست فیش های واریزی سفارشات')

@section('content')
    <div x-data="{
        showImageModal: false,
        imageUrl: '',
        changeModal: false,
        showImageFullScreen(imageUrl) { this.showImageModal = true, this.imageUrl = imageUrl },
        selectedItems: [],
    
        toggleItem(id) {
            const index = this.selectedItems.indexOf(id);
            if (index > -1) {
                this.selectedItems.splice(index, 1);
            } else {
                this.selectedItems.push(id);
            }
        },
    
        redirectToPrint() {
            if (this.selectedItems.length > 0) {
                const baseUrl = '{{ route('admin-dashboard-prints-order') }}';
                const queryString = this.selectedItems.map(item => `ids[]=${item}`).join('&');
                window.open(`${baseUrl}?${queryString}`, '_blank');
            }
        }
    }">
        <main
            class="relative mx-auto pb-20 md:px-8 md:pt-6"
            x-data="{ items: 1000, ZoomOutMapIcon: false }"
        >
            <div class="px-3 md:px-0">
                <div class="">

                    <div class="table-wrp block w-full overflow-x-auto pb-5 dark:bg-gray-800">
                        <livewire:dashboard.shop.transactions.deposit-list />
                    </div>
                </div>

            </div>

        </main>

        <div
            class="fixed bottom-0 left-0 right-0 top-0 z-[9999] h-full w-full bg-gray-900/80 md:py-2"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            x-show="showImageModal"
            x-cloak
        >
            <div
                class="fade-scale relative h-full w-full"
                x-show="showImageModal"
                x-transition:enter="transition ease-out duration-100"
                x-transition:enter-start="opacity-0 scale-75"
                x-transition:enter-end="opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-100"
                x-transition:leave-start="opacity-100 scale-100"
                x-transition:leave-end="opacity-0 scale-75"
            >
                <div
                    class="relative mx-auto h-full max-w-xl overflow-hidden bg-white md:rounded-xl"
                    @click.away="showImageModal = false; lock = false"
                >
                    <div class="mb-3 flex justify-between p-3 dark:border-gray-800">
                        <div>
                            <h1 class="text-base font-bold text-gray-700 dark:text-gray-200 md:text-xl">مشاهده عکس</h1>
                        </div>
                        <button
                            class="mr-auto inline-flex h-8 w-8 items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-500 transition-all hover:bg-gray-200 hover:text-gray-700 dark:hover:bg-gray-600 dark:hover:text-white"
                            type="button"
                            @click="showImageModal = false; lock = false"
                        >
                            <svg
                                class="h-5 w-5"
                                aria-hidden="true"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <div class="flex h-full items-center justify-center overflow-hidden">
                        <img
                            class="max-h-full max-w-full object-contain"
                            alt="تصویر"
                            :src="imageUrl"
                        >
                    </div>

                </div>

            </div>
        </div>

        <div
            class="fixed left-0 top-0 z-[1001] flex h-screen w-screen items-start justify-center bg-gray-900/75 md:h-full md:w-full"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            x-show="changeModal"
            x-cloak
        >
            <div
                class="fade-scale relative top-10 h-auto w-full max-w-md rounded-xl bg-white p-5 shadow-xl dark:bg-gray-900"
                x-show="changeModal"
                x-transition:enter="transition ease-out duration-100"
                x-transition:enter-start="opacity-0 scale-75"
                x-transition:enter-end="opacity-100 scale-100"
                x-transition:leave="transition ease-in duration-100"
                x-transition:leave-start="opacity-100 scale-100"
                x-transition:leave-end="opacity-0 scale-75"
            >
                <div>
                    <div class="mb-3 flex justify-between p-3 pb-0 pt-0 dark:border-gray-800">
                        <div>
                            <h1 class="text-base font-bold text-gray-700 dark:text-gray-200">چاپ / تغییر وضعیت گروهی</h1>
                        </div>
                        <button
                            class="mr-auto inline-flex h-8 w-8 items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-500 transition-all hover:bg-gray-200 hover:text-gray-700 dark:hover:bg-gray-600 dark:hover:text-white"
                            type="button"
                            @click="changeModal = false; lock = false"
                        >
                            <svg
                                class="h-5 w-5"
                                aria-hidden="true"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                </div>
                <livewire:dashboard.orders.change-print-group />
            </div>
        </div>
    </div>

@stop
