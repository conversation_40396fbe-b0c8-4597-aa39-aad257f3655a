<div class="mt-auto flex w-full flex-col items-center gap-2 max-md:flex-col max-md:gap-2 max-md:pb-10">
    @php
        $discount =
            $percentage > 0 ? auth()->user()->sumCheckout() - auth()->user()->sumDiscountCheckout($percentage) : 0;
    @endphp
    @if (!$data['getUserDeatail'] && !$data['complate'])

        <div
            class="flex w-full justify-between max-md:border-t max-md:border-t-gray-200 max-md:py-3 max-md:pb-2 md:py-1">
            <p class="whitespace-nowrap pb-1 text-base font-bold max-md:text-sm">جمع کل سفارش
                {{-- @if ($data['paymentOnlineChecked'] && $data['PostPayekOnline'])
                    <span>+ هزینه ارسال</span>
                @endif --}}
            </p>
            <span class="block shrink-0 items-center gap-0.5 whitespace-nowrap text-base font-bold">
                <span
                    class="whitespace-nowrap text-base font-bold">{{ formatMoney(auth()->user()->sumCheckout()) }}</span>
                <span class="whitespace-nowrap px-2 text-base text-gray-600 dark:text-gray-300">ریال</span>
            </span>
        </div>
        @if ($data['paymentOnlineChecked'] && $data['PostPayekOnline'])
            <div
                class="flex w-full justify-between max-md:border-t max-md:border-t-gray-200 max-md:py-3 max-md:pb-2 md:py-1">
                <p class="whitespace-nowrap pb-1 text-base font-bold max-md:text-sm">هزینه ارسال
                    {{-- @if ($data['paymentOnlineChecked'] && $data['PostPayekOnline'])
                    <span>+ هزینه ارسال</span>
                @endif --}}
                </p>
                <span class="block shrink-0 items-center gap-0.5 whitespace-nowrap text-base font-bold">
                    <span
                        class="whitespace-nowrap text-base font-bold">{{ formatMoney($data['MoneyPostOnline']) }}</span>
                    <span class="whitespace-nowrap px-2 text-base text-gray-600 dark:text-gray-300">ریال</span>
                </span>
            </div>
        @endif
        <div
            class="flex w-full justify-between max-md:border-t max-md:border-t-gray-200 max-md:py-3 max-md:pb-2 md:py-1">
            <p class="whitespace-nowrap pb-1 text-base font-bold max-md:text-sm">تخفیف
                {{-- @if ($data['paymentOnlineChecked'] && $data['PostPayekOnline'])
                    <span>+ هزینه ارسال</span>
                @endif --}}
            </p>
            <span class="block shrink-0 items-center gap-0.5 whitespace-nowrap text-base font-bold">
                <span class="whitespace-nowrap text-base font-bold">{{ formatMoney($discount) }}</span>
                <span class="whitespace-nowrap px-2 text-base text-gray-600 dark:text-gray-300">ریال</span>
            </span>
        </div>
        <div
            class="flex w-full justify-between max-md:border-t max-md:border-t-gray-200 max-md:py-3 max-md:pb-2 md:py-1">
            <p class="whitespace-nowrap pb-1 text-base font-bold max-md:text-sm">قابل پرداخت
                {{-- @if ($data['paymentOnlineChecked'] && $data['PostPayekOnline'])
                    <span>+ هزینه ارسال</span>
                @endif --}}
            </p>
            <span class="block shrink-0 items-center gap-0.5 whitespace-nowrap text-base font-bold">
                <span
                    class="whitespace-nowrap text-base font-bold">{{ formatMoney(auth()->user()->sumCheckout($data['MoneyPostOnline']) - $discount) }}</span>
                <span class="whitespace-nowrap px-2 text-base text-gray-600 dark:text-gray-300">ریال</span>
            </span>
        </div>
    @endif
    <div class="w-full">
        {{-- <div class="py-3">
            <div class="flex items-center justify-between">
                <span class="text-base font-bold">هزینه کل سفارش:</span>
                <span class="text-base font-bold">{{  }}</span>
            </div>
        </div> --}}
        @if ($data['factorCreate'])
            <div class="py-3">
                <button
                    class="flex w-full items-center justify-center rounded-lg bg-yellow-300 p-3 text-gray-800 transition-all hover:bg-yellow-400 disabled:cursor-not-allowed disabled:bg-gray-200 disabled:text-gray-400 disabled:hover:bg-gray-200 md:p-5"
                    type="button"
                    wire:loading.attr="disabled"
                    @if (isset($carts) && $carts->count() == 0) disabled @else wire:click="addFactor" @endif
                >
                    <span class="text-sm font-bold text-gray-800 md:text-base">رفتن به فاکتور سبدخرید</span>
                </button>
            </div>
        @else
            @if (!$data['getUserDeatail'] && !$data['complate'])
                <div
                    class="w-full border-t border-gray-200 bg-white py-3 max-md:fixed max-md:bottom-0 max-md:left-0 max-md:px-2 max-md:pb-16">
                    <div class="flex w-full justify-between max-md:py-3 max-md:pb-2 md:hidden md:py-1">
                        <p class="whitespace-nowrap pb-1 text-base font-bold max-md:text-sm">قابل پرداخت
                            {{-- @if ($data['paymentOnlineChecked'] && $data['PostPayekOnline'])
                    <span>+ هزینه ارسال</span>
                @endif --}}
                        </p>
                        <span class="block shrink-0 items-center gap-0.5 whitespace-nowrap text-base font-bold">
                            <span
                                class="whitespace-nowrap text-base font-bold">{{ formatMoney(auth()->user()->sumCheckout($data['MoneyPostOnline']) - $discount) }}</span>
                            <span class="whitespace-nowrap px-2 text-base text-gray-600 dark:text-gray-300">ریال</span>
                        </span>
                    </div>
                    <div class="w-full py-3">
                        <button
                            class="flex w-full items-center justify-center rounded-lg bg-green-500 p-3 text-white transition-all hover:bg-green-600 disabled:cursor-not-allowed disabled:bg-gray-200 disabled:text-gray-400 disabled:hover:bg-gray-200 md:p-5"
                            type="button"
                            wire:loading.attr="disabled"
                            wire:click="stepTow"
                            @if (isset($carts) && $carts->count() == 0) disabled @else wire:click="createFactor" @endif
                        >
                            <span class="text-sm md:text-base">مرحله بعد ثبت مشخصات</span>
                        </button>
                    </div>
                </div>
            @elseif($data['getUserDeatail'] && !$data['complate'])
                <div
                    class="w-full border-t border-gray-200 bg-white py-3 max-md:fixed max-md:bottom-0 max-md:left-0 max-md:px-2 max-md:pb-16">
                    <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
                        <div>
                            <div class="flex w-full justify-between max-md:py-3 max-md:pb-2 md:py-1">
                                <p class="whitespace-nowrap pb-1 text-base font-bold max-md:text-sm">قابل پرداخت
                                    {{-- @if ($data['paymentOnlineChecked'] && $data['PostPayekOnline'])
                    <span>+ هزینه ارسال</span>
                @endif --}}
                                </p>
                                <span class="block shrink-0 items-center gap-0.5 whitespace-nowrap text-base font-bold">
                                    <span
                                        class="whitespace-nowrap text-base font-bold">{{ formatMoney(auth()->user()->sumCheckout($data['MoneyPostOnline']) - $discount) }}</span>
                                    <span
                                        class="whitespace-nowrap px-2 text-base text-gray-600 dark:text-gray-300">ریال</span>
                                </span>
                            </div>
                            <div class="py-3">
                                <button
                                    class="flex w-full items-center justify-center rounded-lg bg-green-500 p-3 text-white transition-all hover:bg-green-600 disabled:cursor-not-allowed disabled:bg-gray-200 disabled:text-gray-400 disabled:hover:bg-gray-200 md:p-5"
                                    type="button"
                                    wire:loading.attr="disabled"
                                    wire:click="createFactor"
                                >
                                    <span class="text-sm text-white md:text-base">ایجاد پیش فاکتور خرید</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            @else
                @include('dashboard.admin.shop.checkout.finish')
            @endif
        @endif
    </div>
</div>
