<!DOCTYPE html>
<html lang="fa">

    <head>
        <meta charset="UTF-8">
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0"
        >

        <!-- Open Graph tags for preview -->
        <meta
            property="og:title"
            content="{{ $product->product_title_fa }}"
        >
        <meta
            property="og:description"
            content="قیمت: {{ $product->amount }} | وزن: {{ $product->weight }}"
        >
        <meta
            property="og:image"
            content="https://tdservice.ir/{{ $product->product_image_url }}"
        >
        <meta
            property="og:type"
            content="تیدامد"
        >
        <meta
            property="og:url"
            content="https://tdservice.ir/p/{{ hashId($product->product_detail_id) }}"
        >
        <meta
            http-equiv="X-UA-Compatible"
            content="ie=edge"
        >
        @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/js/gold18k.js'])
        <style>
            @font-face {
                font-family: 'IRANSansWeb';
                src: url('/../assets/fonts/IRANSansWeb.eot');
                /* IE */
                src: url('/../assets/fonts/IRANSansWeb.eot?#iefix') format('embedded-opentype'),
                    /* IE */
                    url('/../assets/fonts/IRANSansWeb.woff') format('woff'),
                    /* Modern Browsers */
                    url('/../assets/fonts/IRANSansWeb.ttf') format('truetype');
                /* Safari, Android, iOS */
                font-weight: normal;
            }

            body {
                direction: rtl;
                font-family: "IRANSansWeb", sans-serif !important;
                background-size: cover;
                min-height: 100vh;
                margin: 0;
            }

            @media (max-width: 640px) {

                /* Hide background on mobile */
                body {
                    background: white;
                }
            }
        </style>
        <title>تیدامد - {{ $product->product_title_fa }}</title>
    </head>

    <body class="container mx-auto">
        @php
            if (!isset($product?->eticket)) {
                $count = $product->available_stock - $product?->pd_out_stock;
            } else {
                $count = $product->available_stock;
            }
            $count_out_stock = $product?->pd_out_stock ?? $product?->p_out_stock;

        @endphp
        <div class="mx-auto max-w-md bg-gray-100 pb-8">
            <img
                class="mx-auto"
                src="https://tdservice.ir/{{ $product->product_image_url }}"
                alt="{{ $product->product_title_fa }}"
                width="400"
                height="400"
            >
            <div class="p-4">
                <div class="flex flex-col gap-2 py-3">
                    <div>
                        <p class="pt-4 text-xl font-bold text-gray-800 max-md:text-base">
                            {{ $product?->product_title_fa }}
                        </p>

                    </div>

                    <div class="mt-3 flex flex-col gap-3 max-md:pb-32">
                        <div class="flex items-center gap-2">
                            <svg
                                class="size-6"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z"
                                />
                            </svg>
                            <div class="flex items-center gap-1.5">
                                <span class="text-base text-gray-500">دسته بندی:</span>
                                <p class="text-base font-bold">
                                    {{ $product?->category_title_fa }}
                                </p>
                            </div>
                        </div>

                        <div class="flex items-center gap-1.5">
                            <span class="text-base text-gray-500">مناسب:</span>
                            <p class="text-bas font-bold">{{ $product?->for_value }}</p>
                        </div>

                        <div class="flex items-center gap-1.5">
                            <span class="text-base text-gray-500">رنگ محصول:</span>
                            <p class="text-bas font-bold">{{ $product?->color }}</p>
                        </div>
                        <div class="flex items-center gap-1.5">
                            <span class="text-base text-gray-500">سایز (زنجیر - گردنبند و..):</span>
                            <p class="text-bas font-bold">{{ $product?->chain_size }}</p>
                        </div>
                        <div class="flex items-center gap-1.5">
                            <span class="text-base text-gray-500">قیمت سنگ:</span>
                            <p class="text-bas font-bold">{{ formatMoney($product?->stone_price) }}</p>
                        </div>
                        <div class="flex items-center gap-1.5">
                            <span class="text-base text-gray-500">شعبه فروش:</span>
                            <p class="text-bas font-bold">{{ $product?->branch }}</p>
                        </div>

                        <div class="flex items-center gap-1.5">
                            <span class="text-base text-gray-500">اتیکت:</span>
                            <p class="text-bas font-bold">{{ $product?->eticket }}</p>
                        </div>
                        @if ($product?->fixed_amount == null)
                            <div class="grid grid-cols-3 gap-2 rounded-lg md:grid-cols-5 md:bg-gray-100">
                                <div class="flex items-center gap-1.5">
                                    <span class="text-base text-gray-500">وزن:</span>
                                    <p class="text-bas font-bold">{{ $product?->weight }}</p>
                                </div>
                                <div class="flex items-center gap-1.5">
                                    <span class="text-base text-gray-500">مالیات:</span>
                                    <p class="text-bas font-bold">{{ $product?->tax }}</p>
                                </div>
                                <div class="flex items-center gap-1.5 md:col-span-2">
                                    <span class="text-base text-gray-500">اجرت ساخت:</span>
                                    <p class="text-bas font-bold">{{ $product?->construction_wages }}</p>
                                </div>
                                <div class="flex items-center gap-1.5">
                                    <span class="text-base text-gray-500">درصد سود:</span>
                                    <p class="text-bas font-bold">{{ $product?->profit }}</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-1.5">
                                <span class="text-base text-gray-500">قیمت طلا محاسبه شده:</span>
                                <p class="text-bas font-bold">{{ $product?->gold18k }} ریال</p>
                            </div>
                        @endif
                        @if ($count_out_stock != null)
                            <div class="flex items-center gap-1.5">
                                <span
                                    class="flex cursor-not-allowed rounded-md px-2 py-1.5 text-base text-red-600 max-md:px-1"
                                >
                                    <span class="flex items-center gap-2">
                                        <span class="text-sm md:text-base">عدم موجودی</span>
                                    </span>
                                </span>
                            </div>
                        @else
                            <div class="flex items-center gap-1.5 bg-gray-200 p-3">
                                <span class="text-base text-gray-500">قیمت محصول:</span>
                                <p class="text-bas font-bold">
                                    {{ $product?->fixed_amount != null ? formatMoney($product?->fixed_amount) : formatMoney($product->amount) }}
                                    ریال
                                </p>
                            </div>
                        @endif
                        <div class="py-3">
                            <div class="grid grid-cols-1 gap-2 rounded-lg md:bg-gray-100">
                                <div class="flex flex-col gap-1.5">
                                    <span class="text-base text-gray-500">شماره کارت:</span>
                                    <p class="text-bas font-bold">{{ $setting['card_number'] }}</p>
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <span class="text-base text-gray-500">شماره شبا:</span>
                                    <p class="text-bas font-bold">{{ $setting['sheba_card_number'] }}</p>
                                </div>
                                <div class="flex flex-col gap-1.5">
                                    <span class="text-base text-gray-500">نام صاحب حساب:</span>
                                    <p class="text-bas font-bold">{{ $setting['fullname_card_number'] }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="p-4 pb-10 pt-10 text-center">
                            <div class="flex flex-col gap-2">
                                <p class="pb-6 text-base font-bold">آدرس :جنت آباد جنوبی، نرسیده به چهارراه لاله، پاساژ
                                    الماس،
                                    طبقه
                                    زیر
                                    همکف،
                                    واحد6</p>
                                <p class="pb-6 text-base font-bold">آدرس : تهران ، چهار راه پونک ، پاساژ همیلا سنتر ،
                                    طبقه اول ،
                                    پلاک ۱۲۷ ،
                                    گالری
                                    تیدا مد</p>
                            </div>
                            <a
                                class="text-base font-bold"
                                href="tel:02171053350"
                            >شماره تماس: <span class="text-red-500">02171053350</span></a>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>

</html>
