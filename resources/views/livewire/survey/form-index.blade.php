<div>
    <div x-data="{ showSurvey: localStorage.getItem('survey_filled_{{ $orderId }}') !== '1', currentStep: @entangle('currentStep') }">
        @if (session()->has('message'))
            <div class="alert alert-success">{{ session('message') }}</div>
        @endif

        <div>
            <template x-if="showSurvey">
                <div>
                    <form>
                        <form
                            class="relative mt-8"
                            wire:submit.prevent="submit"
                        >
                            <div class="h-full w-full bg-white opacity-35"></div>
                            @foreach ($questions as $index => $question)
                                @if ($index === $currentStep)
                                    <div class="fade-in-horiz mb-4">
                                        <label class="mb-6 block font-bold">{{ $question->question_text }}</label>

                                        @if ($question->question_type === 'radio')
                                            @foreach ($question->options as $option)
                                                <label class="mb-6 flex items-center gap-2">
                                                    <input
                                                        class="h-6 w-6"
                                                        type="radio"
                                                        value="{{ $option->option_text }}"
                                                        wire:model="answers.{{ $index }}"
                                                    >
                                                    <span class="pr-2">{{ $option->option_text }}</span>
                                                </label>
                                            @endforeach
                                        @else
                                            <textarea
                                                class="w-full rounded-lg border p-2"
                                                rows="10"
                                                wire:model="answers.{{ $index }}"
                                            ></textarea>
                                        @endif

                                        @error('answers.' . $index)
                                            <span class="text-sm text-red-500">لطفا به این سؤال پاسخ دهید.</span>
                                        @enderror
                                    </div>
                                @endif
                            @endforeach

                            <div class="mt-12 flex justify-between">
                                @if ($currentStep > 0)
                                    <button
                                        class="rounded-lg bg-gray-500 px-4 py-2 text-white transition-all hover:bg-gray-600"
                                        type="button"
                                        wire:click="previousStep"
                                    >
                                        <div>
                                            <div class="flex items-center gap-6">
                                                <svg
                                                    class="size-6"
                                                    wire:loading.remove
                                                    wire:target="previousStep"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="m8.25 4.5 7.5 7.5-7.5 7.5"
                                                    />
                                                </svg>
                                                <svg
                                                    class="size-6"
                                                    role="status"
                                                    aria-hidden="true"
                                                    wire:loading
                                                    wire:target="previousStep"
                                                    viewBox="0 0 100 101"
                                                    fill="none"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                    <path
                                                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                                        fill="#E5E7EB"
                                                    />
                                                    <path
                                                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                                        fill="currentColor"
                                                    />
                                                </svg>
                                                <span>قبلی</span>

                                    </button>
                                @else
                                    <button
                                        class="rounded-lg bg-white px-4 py-2 text-white"
                                        type="button"
                                        wire:click="previousStep"
                                    >قبلی</button>
                                @endif

                                @if ($currentStep < count($questions) - 1)
                                    <button
                                        class="rounded-lg bg-blue-600 px-4 py-2 text-white transition-all hover:bg-blue-500"
                                        type="button"
                                        wire:click="nextStep"
                                    >
                                        <div class="flex items-center gap-6">
                                            <span>بعدی</span>
                                            <svg
                                                class="size-6"
                                                wire:loading.remove
                                                wire:target="nextStep"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M15.75 19.5 8.25 12l7.5-7.5"
                                                />
                                            </svg>
                                            <svg
                                                class="size-6"
                                                role="status"
                                                aria-hidden="true"
                                                wire:loading
                                                wire:target="nextStep"
                                                wire:loading
                                                viewBox="0 0 100 101"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                            >
                                                <path
                                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                                    fill="#E5E7EB"
                                                />
                                                <path
                                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                                    fill="currentColor"
                                                />
                                            </svg>
                                        </div>
                                    </button>
                                @else
                                    <button
                                        class="w-48 rounded-lg bg-green-600 px-4 py-2 text-white transition-all hover:bg-green-500"
                                        type="submit"
                                    >ارسال</button>
                                @endif
                            </div>
                        </form ::contentReference[oaicite:48]{index=48}>
                </div>
            </template>

            <template x-if="!showSurvey">
                <div class="p-8">
                    <div class="flex items-center justify-center">
                        <span
                            class="flex h-24 w-24 items-center justify-center rounded-full bg-green-500 p-1.5 text-white"
                        >
                            <svg
                                class="size-12"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                />
                            </svg>

                        </span>
                    </div>
                    <p class="mt-8 text-base text-green-700">از اینکه در نظرسنجی ما شرکت کردید، صمیمانه سپاسگزاریم.
                        نظرات شما
                        به بهبود خدمات ما کمک می‌کند.
                    </p>
                </div>
            </template>
        </div>
    </div>
    @push('script')
        <script>
            Livewire.on('surveySubmitted', ({
                surveyId
            }) => {
                localStorage.setItem(`survey_filled_${surveyId}`, '1');
                console.log(`Survey ${surveyId} marked as filled.`);
            });
        </script>
    @endpush
</div>
