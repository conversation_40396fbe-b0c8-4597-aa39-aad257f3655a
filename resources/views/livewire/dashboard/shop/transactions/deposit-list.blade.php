<div>
    <div wire:ignore>
        @include('dashboard.admin.orders.fillter')
    </div>
    <div class="mt-3">
        <div
            class="mb-2 mt-3 w-full flex-row-reverse items-center justify-between overflow-hidden md:flex"
            wire:loading.remove
        >

            <span>
                {{ $orders->links(data: ['dark' => true]) }}
            </span>
            <span class="rounded-xl bg-white p-2.5 px-4 text-sm text-gray-700 max-md:hidden">تعداد کل سفارشات:
                {{ $orders->total() }}</span>
        </div>
        <div
            class="relative py-3"
            wire:loading
        >
            <div class="flex items-center gap-3 rounded-md bg-white px-6 py-3">
                <svg
                    class="inline h-6 w-6 animate-spin text-red-700"
                    role="status"
                    aria-hidden="true"
                    wire:loading
                    viewBox="0 0 100 101"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="#E5E7EB"
                    />
                    <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentColor"
                    />
                </svg>
                <span class="text-base font-bold"> درحال دریافت اطلاعات از سرور ...</span>
            </div>
        </div>

        @php
            $image_server_url_1 = 'https://shop.tidamode.ir/';
            $image_server_url_2 = env('APP_TIDAMODE_SHOP_IMAGE_URL');
            $image_server_url_3 = env('APP_ASSET_URL');
        @endphp

        @foreach ($orders as $item)
            <div
                class="group relative mb-3 overflow-hidden rounded-md bg-white shadow-xl"
                wire:loading.remove
                wire:key="{{ Str::uuid() }}"
            >
                {{-- <div class="absolute bottom-2 left-2 w-full p-3 transition-opacity duration-300">

            </div> --}}

                <div
                    class="flex items-center rounded-t-md border-b border-gray-200 bg-gray-100 p-2 max-md:flex-wrap max-md:items-start max-md:gap-1.5 md:justify-between">

                    <label class="flex flex-wrap items-center gap-2">
                        <div class="md:pr-6">
                            <livewire:dashboard.orders.check-print-address
                                key="{{ now()->timestamp . '-' . $item->id }}"
                                :orderId="$item->id"
                                :checked="$item->checked"
                            />
                        </div>
                        @if ($item->sex == 'طلا')
                            <span
                                class="rounded-lg bg-yellow-400 px-3 py-1 text-sm text-gray-800">{{ $item->code }}</span>
                        @else
                            <span
                                class="rounded-lg bg-gray-300 px-3 py-1 text-sm text-gray-800">{{ $item->code }}</span>
                        @endif
                        <span class="text-gray-300">|</span>
                        <span class="font-bold text-green-600">{{ getNameRoute($item->type)['text'] }} -
                            {{ $item->name_pluck }} - {{ $item->model }} - {{ $item->sex }}</span>
                        {{-- <input name="checkmark-{{ $item->id }}" type="checkbox" id="{{ $item->id }}" onclick="checkbox(this)" {{ $item->checkmark == '1' ? 'checked="checked"' : ''  }} > --}}
                        {{-- <span class="checkmark"></span> --}}
                    </label>

                    <div class="flex flex-wrap items-center gap-6">
                        <div class="flex items-center gap-2">
                            @if ($item->post_type != '-' && $item->post_type != null)
                                <div
                                    class="@if ($item->post_type == 'پست') bg-red-500 text-white @elseif($item->post_type == 'حضوری') bg-green-500 text-white @elseif($item->post_type == 'پیک') bg-yellow-300 text-gray-700  @else bg-gray-800 text-white @endif rounded-md px-3 py-0.5">
                                    <span class="text-xs">نوع ارسال: <span
                                            class="text-xs">{{ $item->post_type }}</span></span>
                                </div>
                            @endif
                            <button
                                class="rounded-md bg-blue-600 px-3 py-1 text-white transition-all hover:bg-blue-500 max-md:hidden"
                            >
                                <span class="flex items-center gap-2">
                                    <svg
                                        class="size-5"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="m21 7.5-2.25-1.313M21 7.5v2.25m0-2.25-2.25 1.313M3 7.5l2.25-1.313M3 7.5l2.25 1.313M3 7.5v2.25m9 3 2.25-1.313M12 12.75l-2.25-1.313M12 12.75V15m0 6.75 2.25-1.313M12 21.75V19.5m0 2.25-2.25-1.313m0-16.875L12 2.25l2.25 1.313M21 14.25v2.25l-2.25 1.313m-13.5 0L3 16.5v-2.25"
                                        />
                                    </svg>
                                    <span class="text-xs">انتخاب سازنده</span>
                                </span>
                            </button>
                        </div>
                        <span class="flex items-center gap-1 max-md:hidden">
                            <img
                                src="{{ $item->user->avatar != '' ? $item->user->avatar : '/assets/images/avatar.jpg' }}"
                                style="border-radius: 50%;width: 40px;height: 40px;padding: 2px;border: 2px solid #9155d9;"
                                loding="lazy"
                            >
                            <label
                                class="font0bold text-sm"
                                for="sefaresh"
                                style="padding: 5px;"
                            >سفارش گیرنده: <span class="font-bold">{{ $item->user->fullname }}</span></label>
                        </span>

                    </div>

                </div>
                <div class="grid grid-cols-1 gap-3 p-5 max-md:px-0">
                    <div class="items-center gap-3 max-md:grid max-md:grid-cols-2 md:col-span-4 md:flex">
                        <div class="rounded-md border-2 border-dashed border-gray-200 p-2">
                            <div class="rounded-xl p-2 text-center">
                                <button
                                    class="overflow-hidden rounded-md"
                                    type="button"
                                >
                                    <img
                                        class="h-40 w-40 rounded-md transition-all hover:scale-110"
                                        src="{{ $item->image1 ? $image_server_url_1 . ltrim($item->image1, '/') : '/assets/images/document.png' }}"
                                        alt="order image"
                                        loading="lazy"
                                        width="150"
                                        height="150"
                                        @click="showImageFullScreen($event.target.src)"
                                        onerror="this.onerror=null;this.src='{{ $item->image1 ? $image_server_url_2 . ltrim($item->image1, '/') : '/assets/images/document.png' }}'"
                                    />
                                </button>

                            </div>
                        </div>
                        <div class="rounded-md border-2 border-dashed border-gray-200 p-2">
                            <div class="rounded-xl p-2 text-center">
                                <button
                                    class="overflow-hidden rounded-md"
                                    type="button"
                                >
                                    <img
                                        class="h-40 w-40 rounded-md transition-all hover:scale-110"
                                        src="{{ $item->image2 ? $image_server_url_1 . ltrim($item->image2, '/') : '/assets/images/document.png' }}"
                                        alt="order image"
                                        loading="lazy"
                                        width="150"
                                        height="150"
                                        @click="showImageFullScreen($event.target.src)"
                                        onerror="this.onerror=null;this.src='{{ $item->image2 ? $image_server_url_2 . ltrim($item->image2, '/') : '/assets/images/document.png' }}'"
                                    />
                                </button>

                            </div>
                        </div>
                        <div class="rounded-md border-2 border-dashed border-gray-200 p-2">
                            <div class="rounded-xl p-2 text-center">
                                <button
                                    class="overflow-hidden rounded-md"
                                    type="button"
                                >
                                    <img
                                        class="h-40 w-40 rounded-md transition-all hover:scale-110"
                                        src="{{ $item->image3 ? $image_server_url_1 . ltrim($item->image3, '/') : '/assets/images/document.png' }}"
                                        alt="order image"
                                        loading="lazy"
                                        width="150"
                                        height="150"
                                        @click="showImageFullScreen($event.target.src)"
                                        onerror="this.onerror=null;this.src='{{ $item->image3 ? $image_server_url_2 . ltrim($item->image3, '/') : '/assets/images/document.png' }}'"
                                    />
                                </button>

                            </div>
                        </div>
                        <div class="rounded-md border-2 border-dashed border-gray-200 p-2">
                            <div class="rounded-xl p-2 text-center">
                                <button
                                    class="overflow-hidden rounded-md"
                                    type="button"
                                >
                                    <img
                                        class="h-40 w-40 rounded-md transition-all hover:scale-110"
                                        src="{{ $item->image4 ? $image_server_url_1 . ltrim($item->image4, '/') : '/assets/images/document.png' }}"
                                        alt="order image"
                                        loading="lazy"
                                        width="150"
                                        height="150"
                                        @click="showImageFullScreen($event.target.src)"
                                        onerror="this.onerror=null;this.src='{{ $item->image4 ? $image_server_url_2 . ltrim($item->image4, '/') : '/assets/images/document.png' }}'"
                                    />
                                </button>

                            </div>
                        </div>
                        <div class="rounded-md border-2 border-dashed border-gray-200 p-2">
                            <div class="rounded-xl p-2 text-center">
                                <button
                                    class="overflow-hidden rounded-md"
                                    type="button"
                                >
                                    <img
                                        class="h-40 w-40 rounded-md transition-all hover:scale-110"
                                        src="{{ $item->image5 ? $image_server_url_1 . ltrim($item->image5, '/') : '/assets/images/document.png' }}"
                                        alt="order image"
                                        loading="lazy"
                                        width="150"
                                        height="150"
                                        @click="showImageFullScreen($event.target.src)"
                                        onerror="this.onerror=null;this.src='{{ $item->image5 ? $image_server_url_2 . ltrim($item->image5, '/') : '/assets/images/document.png' }}'"
                                    />
                                </button>

                            </div>
                        </div>
                        <div class="rounded-md border-2 border-dashed border-gray-200 p-2">
                            <div class="rounded-xl p-2 text-center">
                                <button
                                    class="overflow-hidden rounded-md"
                                    type="button"
                                >
                                    <img
                                        class="h-40 w-40 rounded-md transition-all hover:scale-110"
                                        src="{{ $item->image6 ? $image_server_url_1 . ltrim($item->image6, '/') : '/assets/images/document.png' }}"
                                        alt="order image"
                                        loading="lazy"
                                        width="150"
                                        height="150"
                                        @click="showImageFullScreen($event.target.src)"
                                        onerror="this.onerror=null;this.src='{{ $item->image6 ? $image_server_url_2 . ltrim($item->image6, '/') : '/assets/images/document.png' }}'"
                                    />
                                </button>

                            </div>
                        </div>
                        <div class="rounded-md border-2 border-dashed border-gray-200 p-2">
                            <div class="rounded-xl p-2 text-center">
                                <button
                                    class="overflow-hidden rounded-md"
                                    type="button"
                                >
                                    <img
                                        class="h-40 w-40 rounded-md transition-all hover:scale-110"
                                        src="{{ $item->image7 ? $image_server_url_1 . ltrim($item->image7, '/') : '/assets/images/document.png' }}"
                                        alt="order image"
                                        loading="lazy"
                                        width="150"
                                        height="150"
                                        @click="showImageFullScreen($event.target.src)"
                                        onerror="this.onerror=null;this.src='{{ $item->image7 ? $image_server_url_2 . ltrim($item->image7, '/') : '/assets/images/document.png' }}'"
                                    />
                                </button>

                            </div>
                        </div>

                    </div>
                    <span class="flex items-center gap-1 md:hidden">
                        <img
                            src="{{ $item->user->avatar != '' ? $item->user->avatar : '/assets/images/avatar.jpg' }}"
                            style="border-radius: 50%;width: 40px;height: 40px;padding: 2px;border: 2px solid #9155d9;"
                            loding="lazy"
                        >
                        <label
                            class="font0bold text-sm"
                            for="sefaresh"
                            style="padding: 5px;"
                        >سفارش گیرنده: <span class="font-bold">{{ $item->user->fullname }}</span></label>
                    </span>
                    <div class="md:col-span-2">
                        <div class="flex flex-col gap-2">
                            <div>
                                <span class="ml-1 font-bold">کدسفارش:</span><span>
                                    @if ($item->sex == 'طلا')
                                        <span
                                            class="rounded-lg bg-yellow-400 px-3 text-sm text-gray-800">{{ $item->code }}</span>
                                    @else
                                        <span
                                            class="rounded-lg bg-gray-300 px-3 text-sm text-gray-800">{{ $item->code }}</span>
                                    @endif
                                </span>
                            </div>
                            <div>
                                <span class="ml-1 font-bold">نوع سفارش:</span>
                                <span class="text-sm text-blue-600">{{ getNameRoute($item->type)['text'] }}</span>
                            </div>
                            <div>
                                <span class="ml-1 font-bold">نام پلاک:</span>
                                <span class="text-sm text-blue-600">{{ $item->name_pluck }}</span>
                            </div>
                            <div>
                                <span class="ml-1 font-bold">مدل:</span>
                                <span class="text-sm text-blue-600">{{ $item->model }}</span>
                            </div>
                            <div>
                                <span class="ml-1 font-bold">جنس:</span>
                                <span class="text-sm text-blue-600">{{ $item->sex }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="md:col-span-2">
                        <div class="flex flex-col gap-2">
                            <div>
                                <span class="ml-1 font-bold">نام گیرنده:</span>
                                <span class="text-sm text-blue-600">{{ $item->fullname }}</span>
                            </div>
                            <div>
                                <span class="ml-1 font-bold">شماره تماس:</span>
                                <span class="text-sm text-blue-600">{{ $item->phone }}</span>
                            </div>
                            <div>
                                <span class="ml-1 font-bold">سایز:</span>
                                <span class="text-sm text-blue-600">{{ $item->size != '' ? $item->size : '-' }}</span>
                            </div>
                            <div>
                                <span class="ml-1 font-bold">وضعیت:</span>
                                <span
                                    class="{{ color_status($item->last_status) }} rounded-xl px-3 text-sm">{{ status_last($item->last_status) }}</span>
                            </div>
                            <div><span class="ml-1">تاریخ سفارش:</span><span
                                    class="text-sm text-blue-600">{{ $item->date_last_status }}</span></div>
                        </div>
                    </div>
                    <div class="md:col-span-2">
                        <div class="flex flex-col gap-2">
                            <div><span class="ml-1 font-bold">بیعانه اول:</span>
                                <span class="text-sm text-blue-600">{{ formatMoney($item->deposit1) }}</span>
                            </div>
                            <div><span class="ml-1 font-bold">بیعانه دوم:</span>
                                <span class="text-sm text-blue-600">{{ formatMoney($item->deposit2) }}</span>
                            </div>
                            <div><span class="ml-1 font-bold">باقی مانده:</span>
                                <span class="text-sm text-blue-600">{{ formatMoney($item->remaining) }}</span>
                            </div>
                            <div>
                                <hr>
                            </div>
                            <div class="rounded-md bg-blue-500 p-2 text-center text-white">
                                <span class="ml-1 font-bold">جمع
                                    کل:</span><span>{{ formatMoney($item->total_amount) }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="h-6 w-full items-center justify-between gap-2 p-5 md:col-span-12 md:flex">
                        <p class="text-base text-red-500">توضیحات:‌ <span
                                class="text-base text-gray-700">{{ $item->tips }}</span></p>
                        <a
                            class="rounded-xl bg-gray-100 px-6 py-1 transition-all hover:bg-gray-300 max-md:absolute max-md:bottom-2 max-md:left-2"
                            href="{{ route('admin-dashboard-show-order', $item->id) }}"
                            target="_blank"
                        >
                            <span class="text-xs text-gray-600">جزئیات سفارش</span>
                        </a>
                    </div>
                </div>

            </div>
        @endforeach

        <div
            class="mt-3 overflow-hidden p-3"
            wire:loading.remove
        >

            {{ $orders->links(data: ['dark' => true]) }}

        </div>
    </div>

</div>
