<div class="relative p-4">
    @include('layouts.tools.loading-dot')
    <h2 class="mb-4 text-lg font-semibold">سوال و پاسخ نظرسنجی</h2>

    @if ($responses && $responses->count())
        <ul class="space-y-3">
            @foreach ($responses as $response)
                {{-- @dd($response->toArray()); --}}
                <li class="mb-3 rounded border p-3 shadow-sm">
                    <ul class="pl-5">
                        {{-- @dd($response->answerItems) --}}
                        @foreach ($response->answerItems as $answer)
                            <li class="mb-3">
                                <p class="text-base font-bold">
                                    {{ $answer['question']['question_text'] ?? 'سوال نامشخص' }}
                                </p>
                                <p class="mt-2 text-base text-green-500">
                                    {{ is_array($answer['answer_text']) ? implode(', ', $answer['answer_text']) : $answer['answer_text'] }}
                                </p>
                            </li>
                        @endforeach
                    </ul>
                    <div class="rounded border bg-gray-100 p-3">
                        <p class="text-gray-500">
                            <span>تاریخ نظرسنجی:</span>
                            {{ shamsiDate($response['created_at']) }}
                        </p>
                    </div>
                </li>
            @endforeach
        </ul>
    @else
        <p class="text-gray-500">هیچ پاسخی برای این نظرسنجی ثبت نشده است.</p>
    @endif
</div>
