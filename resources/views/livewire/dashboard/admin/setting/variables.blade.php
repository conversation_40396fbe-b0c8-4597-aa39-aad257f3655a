<form
    class="fade-in-horiz relative mt-6"
    wire:submit="save"
>
    @include('layouts.tools.loading')
    <div class="grid grid-cols-1 gap-3 pt-4 md:grid-cols-4">
        <div class="md:col-span-2">
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="card_number"
            >شماره کارت واریزی<span class="rounded-xl p-0.5 px-2 text-xs text-gray-600">(محصولات بدون اجرت)
                </span>:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="card_number"
                    type="text"
                    wire:model="data.card_number"
                >
            </div>
        </div>
        <div class="md:col-span-2">
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="sheba_card_number"
            >شبا کارت واریزی<span class="rounded-xl p-0.5 px-2 text-xs text-gray-600">(محصولات بدون اجرت)
                </span>:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="sheba_card_number"
                    type="text"
                    wire:model="data.sheba_card_number"
                >
            </div>
        </div>
        <div class="md:col-span-2">
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="fullname_card_number"
            >به نام<span class="rounded-xl p-0.5 px-2 text-xs text-gray-600">(محصولات بدون اجرت)
                </span>:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="fullname_card_number"
                    type="text"
                    wire:model="data.fullname_card_number"
                >
            </div>
        </div>
        <div class="md:col-span-2">
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="shop_name"
            >نام فروشگاه:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="shop_name"
                    type="text"
                    wire:model="data.shop_name"
                >
            </div>
        </div>
        <div class="md:col-span-2">
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="shop_phone"
            >شماره تماس:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="shop_phone"
                    type="text"
                    wire:model="data.shop_phone"
                >
            </div>
        </div>
        <div class="md:col-span-2">
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="shop_zipcode"
            >کدپستی:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="shop_zipcode"
                    type="text"
                    wire:model="data.shop_zipcode"
                >
            </div>

        </div>
        <div class="md:col-span-4">
            <label
                class="block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="shop_address"
            >آدرس فروشگاه:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="shop_address"
                    type="text"
                    wire:model="data.shop_address"
                >
            </div>
        </div>
        <div class="mt-3 md:col-span-4">
            <label class="inline-flex cursor-pointer items-center">
                <input
                    class="peer sr-only"
                    type="checkbox"
                    wire:model="data.is_shop_open"
                >
                <div
                    class="peer relative h-6 w-11 rounded-full bg-gray-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-red-500 peer-checked:after:-translate-x-full peer-checked:after:border-white peer-focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-red-800 rtl:peer-checked:after:translate-x-full">
                </div>
                <span class="ms-3 text-sm font-bold text-gray-900 dark:font-normal dark:text-white">نمایش اطلاعات
                    فروشگاه در صفحه اصلی چاپ آدرس مشتری</span>
            </label>
        </div>
    </div>

    <div class="flex flex-row-reverse items-center justify-between max-md:mt-16 md:pt-6">
        <button
            class="mt-3 rounded-xl bg-red-500 px-6 py-3 text-white transition-all hover:bg-red-600 disabled:bg-gray-200 disabled:text-gray-500 max-md:px-3 max-md:py-2"
            type="submit"
        >
            <div class="flex items-center justify-center">
                <svg
                    class="inline h-6 w-6 animate-spin text-red-700"
                    role="status"
                    aria-hidden="true"
                    wire:loading
                    viewBox="0 0 100 101"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="#E5E7EB"
                    />
                    <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentColor"
                    />
                </svg>
                <span class="mr-3 text-sm max-md:text-sm">ذخیره اطلاعات</span>
            </div>
        </button>
    </div>
</form>
