<form wire:submit="store">
    <div class="grid grid-cols-1 gap-3 md:grid-cols-3">
        <div class="md:col-span-3">
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                for="discount_title"
            >عنوان تخفیف (به فارسی مثال: تخفیف روز مادر):</label>
            <input
                class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-right text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="discount_title"
                type="text"
                wire:model="discount.title"
                dir="ltr"
            >
            @error('discount.title')
                <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                            class="text-sm font-bold text-red-600"
                        >{{ $message }}</span></span></div>
            @enderror
        </div>
        <div class="md:col-span-2">
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                for="discount_code"
            >کدتخفیف (میتواند شامل حروف و اعداد باشد):</label>
            <input
                class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-left text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="discount_code"
                type="text"
                wire:model="discount.code"
                dir="ltr"
            >
            @error('discount.code')
                <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                            class="text-sm font-bold text-red-600"
                        >{{ $message }}</span></span></div>
            @enderror
        </div>
        <div>
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                for="discount_count"
            >تعداد قابل استفاده (عدد):</label>
            <input
                class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="discount_count"
                type="text"
                wire:model="discount.count"
                dir="ltr"
            >
            @error('discount.count')
                <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                            class="text-sm font-bold text-red-600"
                        >{{ $message }}</span></span></div>
            @enderror
        </div>
        <div>
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                for="minimumـprice"
            >حداقل مبلغ تخفیف (ریال):</label>
            <input
                class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="minimumـprice"
                type="text"
                wire:model="discount.minimumـprice"
                onkeyup="javascript:this.value=Comma(this.value);"
                dir="ltr"
            >
            @error('discount.minimumـprice')
                <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                            class="text-sm font-bold text-red-600"
                        >{{ $message }}</span></span></div>
            @enderror
        </div>
        <div>
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                for="discount_maximumـprice"
            >حداکثر مبلغ (ریال):</label>
            <input
                class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="discount_maximumـprice"
                type="text"
                wire:model="discount.maximumـprice"
                onkeyup="javascript:this.value=Comma(this.value);"
                dir="ltr"
            >
            @error('discount.maximumـprice')
                <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                            class="text-sm font-bold text-red-600"
                        >{{ $message }}</span></span></div>
            @enderror
        </div>
        <div>
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                for="discount_percentage"
            >درصد تخفیف (از سود):</label>
            <input
                class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="discount_percentage"
                type="text"
                wire:model="discount.percentage"
                dir="ltr"
            >
            @error('discount.percentage')
                <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                            class="text-sm font-bold text-red-600"
                        >{{ $message }}</span></span></div>
            @enderror
        </div>
        <div>
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                for="weight"
            >از گرم به بالا:</label>
            <input
                class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="weight"
                type="text"
                wire:model="discount.weight"
                dir="ltr"
            >
            @error('discount.weight')
                <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                            class="text-sm font-bold text-red-600"
                        >{{ $message }}</span></span></div>
            @enderror
        </div>
        <div>
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                for="discount_start_date_time"
            >شروع از تاریخ و ساعت:</label>
            <input
                class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="discount_start_date_time"
                data-jdp
                type="text"
                wire:model="discount.start_date_time"
                dir="ltr"
            >
            @error('discount.start_date_time')
                <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                            class="text-sm font-bold text-red-600"
                        >{{ $message }}</span></span></div>
            @enderror
        </div>
        <div>
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                for="discount_end_date_time"
            >تا تاریخ و ساعت:</label>
            <input
                class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="discount_end_date_time"
                data-jdp
                type="text"
                wire:model="discount.end_date_time"
                dir="ltr"
            >
            @error('discount.end_date_time')
                <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                            class="text-sm font-bold text-red-600"
                        >{{ $message }}</span></span></div>
            @enderror
        </div>
        <div>
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                for="discount_status"
            >وضعیت:</label>
            <select
                class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="discount_status"
                wire:model="discount.status"
            >
                <option selected>--انتخاب کنید--</option>
                <option value="active">فعال</option>
                <option value="deactive">غیرفعال</option>
            </select>
            @error('discount_status')
                <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                            class="text-sm font-bold text-red-600"
                        >{{ $message }}</span></span></div>
            @enderror
        </div>
    </div>
    <div class="mt-10 flex flex-row-reverse items-center">
        <button
            class="flex items-center justify-center rounded-lg bg-green-600 px-6 py-3 text-white transition-all hover:bg-green-500 disabled:bg-gray-200 disabled:text-gray-400 max-md:w-full"
            type="submit"
            wire:target="store"
        >
            <svg
                class="ml-3 inline h-6 w-6 animate-spin text-green-600 dark:text-green-600"
                role="status"
                aria-hidden="true"
                wire:loading
                wire:target="store"
                viewBox="0 0 100 101"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                    fill="#E5E7EB"
                />
                <path
                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                    fill="currentColor"
                />
            </svg>
            <span>ثبت نهایی فرم</span>
        </button>
    </div>
    </div>
</form>
