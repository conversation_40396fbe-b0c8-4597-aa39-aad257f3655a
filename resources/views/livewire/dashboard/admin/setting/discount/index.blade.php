<div class="fade-in-horiz">
    <div class="text-gray-700">
        <div class="overflow-x-auto">
            <table class="w-full whitespace-nowrap">
                <thead class="bg-gray-100">
                    <tr
                        class="mb-3 rounded border border-gray-100 focus:outline-none"
                        tabindex="0"
                    >

                        <th class="border border-gray-200 p-3 text-center"><span class="text-sm">عنوان</span>
                        </th>
                        <th class="border border-gray-200 p-3 text-center"><span class="text-sm">کدتخفیف</span>
                        </th>
                        <th class="border border-gray-200 p-3 text-center"><span class="text-sm">درصد تخفیف</span>
                        </th>
                        <th class="border border-gray-200 p-3 text-center"><span class="text-sm">محدودیت استفاده</span>
                        </th>

                        <th class="border border-gray-200 p-3 text-center"><span class="text-sm">شروع اعتبار</span></th>
                        <th class="border border-gray-200 p-3 text-center"><span class="text-sm">پایان اعتبار</span>
                        </th>
                        <th class="border border-gray-200 p-3 text-center"><span class="text-sm">وضعیت</span></th>
                        <th class="border border-gray-200 p-3 text-center"><span class="text-sm">تنظیمات</span>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($discounts as $key => $item)
                        <tr
                            class="h-16 rounded border transition-all hover:bg-gray-100 focus:outline-none"
                            tabindex="{{ $key }}"
                        >
                            <td class="px-2 text-center">
                                <span class="text-sm">{{ $item->title }}</span>
                            </td>
                            <td class="px-2 text-center">
                                <span class="text-sm">{{ $item->code }}</span>
                            </td>
                            <td class="px-2 text-center">
                                <span class="text-sm">{{ $item->percentage }}</span>
                            </td>
                            <td class="px-2 text-center">
                                <span class="text-sm">{{ $item->total_uses }}</span>
                            </td>
                            <td class="px-2 text-center">
                                <span class="text-sm">{{ $item->start_date }}</span>
                            </td>
                            <td class="px-2 text-center">
                                <span class="text-sm">{{ $item->start_date }}</span>
                            </td>
                            <td class="px-2 text-center">
                                @if ($item->status == 'active')
                                    <span
                                        class="flex items-center justify-center rounded-xl bg-green-500 px-1 text-white"
                                    >فعال</span>
                                @else
                                    <span
                                        class="flex items-center justify-center rounded-xl bg-gray-100 px-1 text-gray-500"
                                    >غیرفعال</span>
                                @endif
                            </td>
                            <td class="px-2 text-center">
                                <button
                                    class="px-4 py-2"
                                    type="button"
                                    @click="editDiscountModal = true, lock = true"
                                    wire:click="$dispatch('set-discount', { discountId : {{ $item->id }} })"
                                >
                                    <svg
                                        class="size-6"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M6.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM18.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"
                                        />
                                    </svg>

                                </button>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

</div>
