<div class="fade-in-horiz mt-6">
    <h2 class="mb-6 border-b border-gray-200 pb-2 text-base font-bold">محاسبه پورسانت کالا</h2>
    <form
        class="grid grid-cols-2 gap-3 md:grid-cols-6"
        wire:submit="calculator"
        wire:ignore
    >
        <div>
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="weight"
            >وزن:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-200 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="weight"
                    type="text"
                    wire:model="data.weight"
                >
            </div>
        </div>
        <div class="md:col-span-2">
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="gold18k"
            >نرخ محاسبه طلا <span class="text-xs text-gray-500">(ریال)</span>:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-200 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="gold18k"
                    type="text"
                    onkeyup="javascript:this.value=Comma(this.value);"
                    wire:model="data.gold18k"
                >
            </div>
        </div>
        <div>
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="construction_wages"
            >اجرت ساخت:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-200 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="construction_wages"
                    type="text"
                    wire:model="data.construction_wages"
                >
            </div>
        </div>
        <div>
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="profit"
            >سود:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-200 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="profit"
                    type="text"
                    wire:model="data.profit"
                >
            </div>
        </div>
        <div>
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="tax"
            >مالیات:</label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-200 dark:border-gray-600 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="tax"
                    type="text"
                    wire:model="data.tax"
                >
            </div>
        </div>
        <div class="md:col-span-2">
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="total"
            >جمع کل <span class="text-xs text-gray-500">(ریال)</span> </label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-100 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="total"
                    type="text"
                    disabled
                    wire:model="data.total"
                >
            </div>
        </div>
        <div class="md:col-span-2">
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="product_price"
            >هزینه کالا <span class="text-xs text-gray-500">(ریال)</span> </label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-100 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="product_price"
                    type="text"
                    disabled
                    wire:model="data.product_price"
                >
            </div>
        </div>
        <div class="md:col-span-2">
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="commission"
            >پورسانت <span class="text-xs text-gray-500">(ریال)</span></label>
            <div class="relative">
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-100 dark:border-gray-900 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="commission"
                    type="text"
                    disabled
                    wire:model="data.commission"
                >
            </div>
        </div>
        <div class="flex flex-row-reverse items-center justify-between max-md:col-span-2 max-md:mt-6 md:col-span-6">
            <button
                class="mt-3 rounded-xl bg-red-500 px-6 py-4 text-white transition-all hover:bg-red-600 disabled:bg-gray-200 disabled:text-gray-500 max-md:px-3 max-md:py-2"
                type="submit"
            >
                <div class="flex items-center justify-center">
                    <svg
                        class="inline h-6 w-6 animate-spin text-red-700"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="mr-3 text-sm max-md:text-sm">محاسبه پورسانت و مبلغ کالا</span>
                </div>
            </button>
        </div>
    </form>
</div>
