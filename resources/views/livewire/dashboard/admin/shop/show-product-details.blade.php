<div class="">
    @php
        $count_out_stock = $detail?->fixed_amount != null ? $detail?->p_out_stock : $detail?->pd_out_stock;
    @endphp

    <div class="flex justify-between p-3 dark:border-gray-800 md:p-5">
        <div class="">
            <h1 class="text-base text-gray-700 md:text-xl">{{ $detail?->product_title_fa }}</h1>
        </div>
        <button
            class="mr-auto inline-flex h-8 w-8 items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-500 transition-all hover:bg-gray-200 hover:text-gray-700 dark:hover:bg-gray-600 dark:hover:text-white"
            type="button"
            @click="$store.showProductDetails = false, lock = false"
        >
            <svg
                class="h-5 w-5"
                aria-hidden="true"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                ></path>
            </svg>
            <span class="sr-only">Close modal</span>
        </button>
    </div>
    <div class="relative h-auto overflow-y-auto p-5 max-md:max-h-[100vh]">
        @include('layouts.tools.loading-dot')

        <div class="w-full rounded-lg max-md:pb-32">
            @php
                $imageUrl = $detail?->product_image_url ?? asset('/assets/images/woocommerce-placeholder-400x400.png');
            @endphp
            <img
                class="mx-auto h-80 w-80 rounded-xl"
                src="{{ $image_server_url . $imageUrl }}"
                alt="{{ $detail?->product_title_fa }}"
                loading="lazy"
            >

            <div class="flex flex-col gap-2 py-3">
                <div>
                    <p class="pt-4 text-xl font-bold text-gray-800 max-md:text-base">{{ $detail?->product_title_fa }}
                    </p>

                </div>

                <div class="mt-3 flex flex-col gap-3 max-md:pb-32">
                    <div class="flex items-center gap-2">
                        <svg
                            class="size-6"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z"
                            />
                        </svg>
                        <div class="flex items-center gap-1.5">
                            <span class="text-base text-gray-500">دسته بندی:</span>
                            <p class="text-base font-bold">
                                {{ $detail?->category_title_fa }}
                            </p>
                        </div>
                    </div>

                    <div class="flex items-center gap-1.5">
                        <span class="text-base text-gray-500">مناسب:</span>
                        <p class="text-bas font-bold">{{ $detail?->for_value }}</p>
                    </div>

                    <div class="flex items-center gap-1.5">
                        <span class="text-base text-gray-500">رنگ محصول:</span>
                        <p class="text-bas font-bold">{{ $detail?->color }}</p>
                    </div>
                    <div class="flex items-center gap-1.5">
                        <span class="text-base text-gray-500">سایز (زنجیر - گردنبند و..):</span>
                        <p class="text-bas font-bold">{{ $detail?->chain_size }}</p>
                    </div>
                    <div class="flex items-center gap-1.5">
                        <span class="text-base text-gray-500">قیمت سنگ:</span>
                        <p class="text-bas font-bold">{{ formatMoney($detail?->stone_price) }}</p>
                    </div>
                    <div class="flex items-center gap-1.5">
                        <span class="text-base text-gray-500">شعبه فروش:</span>
                        <p class="text-bas font-bold">{{ $detail?->branch }}</p>
                    </div>

                    <div class="flex items-center gap-1.5">
                        <span class="text-base text-gray-500">اتیکت:</span>
                        <p class="text-bas font-bold">{{ $detail?->eticket }}</p>
                    </div>
                    @if ($detail?->fixed_amount == null)
                        @php
                            $settings = \App\Models\Setting::whereIn('type', ['profit_offer'])
                                ->pluck('body', 'type')
                                ->toArray();
                        @endphp
                        <div class="grid grid-cols-3 gap-2 rounded-lg md:grid-cols-5 md:bg-gray-100 md:p-2">
                            <div class="flex items-center gap-1.5">
                                <span class="text-base text-gray-500">وزن:</span>
                                <p class="text-bas font-bold">{{ $detail?->weight }}</p>
                            </div>
                            <div class="flex items-center gap-1.5">
                                <span class="text-base text-gray-500">مالیات:</span>
                                <p class="text-bas font-bold">{{ $detail?->tax }}</p>
                            </div>
                            <div class="flex items-center gap-1.5 md:col-span-2">
                                <span class="text-base text-gray-500">اجرت ساخت:</span>
                                <p class="text-bas font-bold">{{ $detail?->construction_wages }}</p>
                            </div>
                            <div class="flex items-center gap-1.5">
                                <span class="text-base text-gray-500">درصد سود:</span>
                                <p class="text-bas font-bold">
                                    {{ $settings['profit_offer'] == $detail?->profit ? $detail?->profit : $settings['profit_offer'] }}
                                </p>
                            </div>
                        </div>
                        <div class="flex items-center gap-1.5">
                            <span class="text-base text-gray-500">قیمت طلا محاسبه شده:</span>
                            <p class="text-bas font-bold">{{ $detail?->gold18k }} ریال</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        <div
            class="bottom-0 left-0 flex flex-row-reverse items-center gap-3 border-t border-gray-200 bg-white max-md:fixed max-md:w-full max-md:px-3 max-md:pb-16 md:py-4">

            @if ($count_out_stock != null)
                <span class="flex cursor-not-allowed rounded-md px-2 py-1.5 text-base text-red-600 max-md:px-1">
                    <span class="flex items-center gap-2">
                        <span class="text-sm md:text-base">عدم موجودی</span>
                    </span>
                </span>
            @else
                @if ($detail?->available_stock > 0)
                    <div class="flex w-full flex-col">
                        <div>
                            @if (
                                (optional($detail)->fixed_amount == 0 || optional($detail)->fixed_amount == null) &&
                                    (optional($detail)->construction_wages == null || optional($detail)->construction_wages == 0))
                                <div
                                    class="relative mt-2 w-full gap-2 rounded-lg bg-gray-100 px-2 py-3 text-right text-sm font-bold"
                                    x-data="{
                                        copyPaymentInfo() {
                                            const cover = 'https://tdservice.ir/p/{{ hashId($detail->product_detail_id) }}';
                                            const product = '{{ $detail->product_title_fa ?? '' }}';
                                            const eticket = '{{ $detail?->eticket ?? '' }}';
                                            const price = '{{ $detail->amount ?? '-' }}';
                                            const weight = '{{ $detail?->weight ?? '' }}';
                                            const construction_wages = '{{ $detail?->construction_wages ?? '' }} درصد';
                                            const tax = '{{ $detail?->tax ?? 0 }} درصد';
                                            const profit = '{{ $detail?->profit ?? '' }} درصد';
                                            const branch = '{{ $detail?->branch ?? '' }}';
                                            const card = '{{ $setting['card_number'] ?? '' }}';
                                            const name = '{{ $setting['fullname_card_number'] ?? '' }}';
                                            const sheba = '{{ $setting['sheba_card_number'] ?? '' }}';

                                            const ltr = '\u200E'; // Left-To-Right mark

                                            const text =
                                                `${product}\n` +
                                                `اتیکت محصول: ${ltr}${eticket}\n` +
                                                `وزن: ${ltr}${weight} گرم \n` +
                                                `اجرت: ${ltr}${construction_wages}\n` +
                                                `مالیات: ${ltr}${tax}\n` +
                                                `سود: ${ltr}${profit}\n` +
                                                `شعبه فروش: ${ltr}${branch}\n` +
                                                `قیمت محصول: ${ltr}${price} ریال \n\n` +
                                                `لینک محصول: ${ltr}${cover}\n\n` +
                                                `شماره کارت: ${ltr}${card}\n` +
                                                `بنام: ${name}\n` +
                                                `شماره شبا: ${ltr}${sheba}`;

                                            const textArea = document.createElement('textarea');
                                            textArea.value = text;
                                            document.body.appendChild(textArea);
                                            textArea.select();

                                            try {
                                                document.execCommand('copy');
                                                alert('اطلاعات با موفقیت کپی شد!');
                                            } catch (err) {
                                                alert('خطا در کپی اطلاعات');
                                            }

                                            document.body.removeChild(textArea);
                                        }
                                    }"
                                >
                                    <div class="text-right">

                                        <div class="flex flex-col items-start gap-1 text-sm max-md:text-xs">
                                            <div>شماره کارت: <span
                                                    dir="ltr">{{ $setting['card_number'] ?? '---' }}</span>
                                            </div>
                                            <div>بنام: {{ $setting['fullname_card_number'] ?? '---' }}</div>
                                            <div>شماره شبا: {{ $setting['sheba_card_number'] ?? '---' }}</div>
                                        </div>
                                    </div>

                                    <!-- دکمه کپی -->
                                    <button
                                        class="absolute bottom-2 left-2 mt-2 flex items-center gap-1 rounded-md bg-blue-600 px-3 py-1.5 text-xs text-white transition hover:bg-blue-700"
                                        @click="copyPaymentInfo"
                                    >
                                        <!-- آیکن (Heroicon یا Unicode) -->
                                        <svg
                                            class="h-4 w-4"
                                            fill="none"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8l6 6v8a2 2 0 01-2 2h-2M8 16v4a2 2 0 002 2h4a2 2 0 002-2v-4M8 16h8"
                                            ></path>
                                        </svg>
                                        <span>کپی اطلاعات</span>
                                    </button>
                                </div>
                            @endif
                        </div>
                        <div class="mt-3 flex w-full flex-row-reverse items-center justify-between">
                            @php
                                $APP_TIDAMODE_SHOP_URL = env('APP_TIDAMODE_SHOP_URL');
                            @endphp
                            <div class="flex w-full flex-row-reverse items-center gap-2">
                                <input
                                    class="hidden w-full rounded-md border-2 border-gray-100 p-2 pl-8 text-sm text-gray-500 outline-none ring-0 focus:bottom-0 focus:ring-0 max-md:hidden"
                                    type="hidden"
                                    value="{{ $APP_TIDAMODE_SHOP_URL }}p/{{ $detail->product_id }}/{{ $detail->product_detail_id }}/{{ hashId(auth()->user()->id) }}"
                                    readonly
                                    x-ref="copyInput"
                                >
                                <button
                                    class="rounded-md bg-gray-100 px-3 py-2.5 text-gray-500 max-md:mt-3"
                                    type="button"
                                    @click="() => {
                                    let textArea = document.createElement('textarea');
                                    textArea.value = $refs.copyInput.value;
                                    document.body.appendChild(textArea);
                                    textArea.select();
                                    try {
                                        document.execCommand('copy');
                                        alert('آدرس با موفقیت کپی شد!');
                                    } catch (err) {
                                        alert('خطا در کپی کردن آدرس');
                                    }
                                    document.body.removeChild(textArea);
                                }"
                                >
                                    <svg
                                        class="size-6"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"
                                        />
                                    </svg>

                                </button>
                                <livewire:dashboard.admin.shop.add-cart-button
                                    :key="$detail->product_detail_id . '-' . $detail->product_id"
                                    :productId="$detail->product_id"
                                    :weightId="$detail->product_detail_id"
                                    :maxCount="$detail->available_stock"
                                />
                            </div>
                            <span
                                class="dark:font-noraml px-3 text-lg font-semibold text-gray-800 dark:font-normal dark:text-gray-100"
                            >
                                @if ($detail->amount != $detail->amount_after_offer)
                                    <span
                                        class="text-base text-red-500"
                                        style="text-decoration: line-through;"
                                    >
                                        {{ formatMoney($detail->amount_after_offer) }}
                                    </span>
                                @else
                                    <span class="dark:gray-300 pr-1 text-sm font-normal text-gray-500">ریال</span>
                                @endif
                                {{ $detail?->fixed_amount != null ? formatMoney($detail?->fixed_amount) : formatMoney($detail->amount) }}
                            </span>
                            {{-- @endif --}}
                        </div>
                    </div>
                @else
                    <span class="flex cursor-not-allowed rounded-md px-2 py-1.5 text-base text-red-600 max-md:px-1">
                        <span class="flex items-center gap-2">
                            <span>
                                <svg
                                    class="size-6 max-md:size-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"
                                    />
                                </svg>
                            </span>
                            <span class="text-sm max-md:text-xs">محصول رزرو شده</span>
                        </span>
                    </span>
                @endif
            @endif

        </div>
    </div>

</div>
