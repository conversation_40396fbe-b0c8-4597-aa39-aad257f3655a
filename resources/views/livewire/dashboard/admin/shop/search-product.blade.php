<div>
    <div
        class="flex items-center border-0 border-b-2 border-[#00887e] dark:border-[#00887e]"
        wire:ignore
    >
        <button
            type="button"
            @click="search = false; lock = false"
        >
            <svg
                class="h-6 w-6 shrink-0 text-gray-600 dark:text-gray-300"
                data-drawer-hide="drawer-bottom-search"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"
                ></path>
            </svg>
        </button>
        <div class="relative mr-auto w-full">
            <input
                class="z-50 block w-full border-0 bg-white p-3 pr-2 text-base text-gray-900 outline-none ring-0 placeholder:text-sm placeholder:text-gray-500 focus:border-[#00887e] focus:ring-0 focus:ring-[#00887e] dark:bg-gray-800 dark:text-white dark:placeholder:text-gray-500"
                id="default-search"
                type="text"
                x-model="searchText"
                autocomplete="off"
                x-ref="searchText"
                wire:model.live.debounce.500ms="title"
                placeholder="عنوان محصول خود را جستجو کنید ..."
            >

            <!-- لودینگ -->
            <div
                class="absolute left-2 top-1/2 z-10 h-5 w-5 -translate-y-1/2 transform items-center justify-center"
                wire:loading
                wire:target="title"
            >
                <svg
                    class="inline h-5 w-5 animate-spin text-[#00887e] dark:text-[#00887e]"
                    role="status"
                    aria-hidden="true"
                    wire:target="total"
                    viewBox="0 0 100 101"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="#E5E7EB"
                    />
                    <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentColor"
                    />
                </svg>
            </div>
            <span
                class="absolute left-2 top-1/2 z-10 h-6 w-6 -translate-y-1/2 transform cursor-pointer items-center justify-center rounded-full bg-gray-900 dark:bg-gray-700"
                style="display: none;"
                wire:loading.remove
                x-show="searchText.length > 0"
                @click="
    searchText = '';
    $refs.searchText.value = '';
    $wire.set('title', '');
    $refs.searchText.focus();
"
            >
                <svg
                    class="h-6 w-6 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M6 18 18 6M6 6l12 12"
                    />
                </svg>
            </span>

        </div>
    </div>
    <ul class="mt-3 flex h-full flex-col gap-0 overflow-y-auto pb-32">
        @forelse ($products as $item)
            @php
                if (!isset($item?->eticket)) {
                    $count = $item->available_stock - $item?->pd_out_stock;
                } else {
                    $count = $item->available_stock;
                }
                $count_out_stock = $item?->pd_out_stock ?? $item?->p_out_stock;

            @endphp
            <li class="bordr-gray-100 border-b p-2 transition-all hover:bg-gray-100">
                <a
                    class="text-sm"
                    href="https://shop.tidamode.ir/product/{{ hashId($item->product_id) }}/{{ $item->product_slug }}?referer={{ hashId(auth()->user()->id) }}"
                    target="_blank"
                    {{-- href="{{ route('admin.products.show', ['productId' => hashId($item->product_id), 'slug' => $item->product_slug]) }}" --}}
                >
                    <div>
                        <div>
                            <h2 class="text-base font-bold">{{ $item->product_title_fa }}</h2>
                            @if (isset($count_out_stock) && $count_out_stock != null && $count == 0)
                                <span
                                    class="flex flex-row-reverse rounded-md px-2 py-1.5 text-base text-red-600 max-md:px-1"
                                >
                                    <span class="text-sm">عدم موجودی</span>
                                </span>
                            @else
                                <div
                                    class="flex w-full flex-row-reverse gap-3 pt-3 max-md:gap-1 max-md:text-left md:flex-row-reverse md:items-center">

                                    <span
                                        class="dark:font-noraml text-sm font-semibold text-gray-800 dark:font-normal dark:text-gray-100"
                                    ><span
                                            class="dark:gray-300 pr-1 text-sm font-normal text-gray-500 md:text-base">ریال</span>
                                        {{ $item?->fixed_amount != null && $item?->fixed_amount > 0 ? formatMoney($item?->fixed_amount) : formatMoney($item?->min_amount) }}
                                    </span>
                                    <span>-</span>
                                    <span
                                        class="dark:font-noraml text-sm font-semibold text-gray-800 dark:font-normal dark:text-gray-100 max-md:text-xs"
                                    ><span
                                            class="dark:gray-300 pr-1 text-sm font-normal text-gray-500 md:text-base">ریال</span>
                                        {{ $item?->fixed_amount != null && $item?->fixed_amount > 0 ? formatMoney($item?->fixed_amount) : formatMoney($item?->min_amount) }}
                                    </span>

                                </div>
                            @endif
                        </div>
                    </div>
                </a>
            </li>
        @empty

            <ul>
                <li class="flex gap-2">
                    <svg
                        class="h-6 w-6 text-gray-400"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
                        />
                    </svg>
                    <div>
                        <span class="text-sm text-gray-500">جستجوی شما نتیجه ی در بر نداشت!</span>
                    </div>
                </li>
            </ul>
        @endforelse

    </ul>
</div>
