<div class="relative mx-auto max-h-dvh w-full max-w-md overflow-y-auto rounded-xl bg-white shadow-xl">
    <!-- Header ثابت -->
    <div
        class="mb-3 flex justify-between p-5 dark:border-gray-800"
        wire:ignore
    >
        <div class="flex items-center gap-2">

            <p class="text-base font-bold text-gray-800 dark:font-normal dark:text-gray-100">
                ثبت برداشت جدید</p>
        </div>
        <button
            class="mr-auto inline-flex h-8 w-8 items-center rounded-lg bg-transparent p-1.5 text-sm transition-all hover:bg-gray-200 hover:text-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white"
            type="button"
            x-on:click="setShowPropertyTo(false)"
        >
            <svg
                class="h-5 w-5"
                aria-hidden="true"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    fill-rule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                ></path>
            </svg>
            <span class="sr-only">Close modal</span>
        </button>
    </div>

    <!-- محتوای اسکرول شونده -->
    <form
        class="grid grid-cols-1 gap-3 px-3 !pt-0 md:grid-cols-2 md:p-5"
        wire:submit="save"
    >

        <div class="md:col-span-2">
            <label
                class="mb-2 block text-right text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="amount"
            >مبلغ برداشتی (ریال):</label>
            <div class="relative">

                <input
                    class="price block w-full rounded-lg border-2 border-gray-300 bg-white p-2.5 text-center text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="amount"
                    type="tel"
                    onkeyup="javascript:this.value=Comma(this.value);"
                    placeholder="0"
                    wire:model="data.amount"
                    dir="ltr"
                >
            </div>
        </div>
        <div class="md:col-span-2">
            <label
                class="mb-2 block text-sm font-bold text-gray-700 dark:text-white"
                for="financialId"
            >برداشت از صندوق:</label>
            <select
                class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2.5 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="financialId"
                wire:model="data.financialId"
            >
                <option selected="">--انتخاب کنید--</option>
                @foreach (\App\Models\FinancialFund::where('status', 'active')->get() as $item)
                    <option value="{{ $item->id }}">{{ $item->title }}</option>
                @endforeach

            </select>
        </div>
        <div class="md:col-span-2">
            <label
                class="mb-2 block text-right text-sm font-bold text-gray-700 dark:font-normal dark:text-gray-100"
                for="site"
            >توضیحات (اختیاری):</label>
            <div class="relative">
                <textarea
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2.5 text-sm text-gray-900 focus:border-red-500 focus:ring-red-500 disabled:bg-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-500 dark:focus:ring-red-500"
                    id="description"
                    rows="6"
                    wire:model="data.description"
                ></textarea>
            </div>
        </div>
        <div class="md:col-span-2">
            <button
                class="mb-4 mt-6 rounded-lg bg-red-500 px-6 py-2.5 text-white transition-all hover:bg-red-600 disabled:bg-gray-100 disabled:text-gray-400"
                type="submit"
                wire:target="save"
                wire:loading.attr="disabled"
            >
                <span class="flex items-center gap-2">
                    <svg
                        class="inline h-4 w-4 animate-spin text-red-600 dark:text-red-500"
                        role="status"
                        aria-hidden="true"
                        wire:loading
                        wire:target="save"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                            fill="#E5E7EB"
                        />
                        <path
                            d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                            fill="currentColor"
                        />
                    </svg>
                    <span class="text-sm">ذخیره اطلاعات</span>
                </span>
            </button>
        </div>
    </form>
</div>
