<div>
    <div
        class="mb-3 flex items-center gap-3 overflow-x-auto md:justify-between"
        wire:ignore
    >
        <div class="flex items-center gap-3">
            <button
                class="shrink-0 rounded-lg bg-red-500 px-4 py-2 text-white transition-all hover:bg-red-600"
                type="button"
                wire:click="$dispatch('openModal', { component: 'dashboard.admin.reports.financial-fund.withdrawal.create-withdrawal',arguments:{ FinancialFundId:'{{ $financial->id }}'} })"
            >
                <div class="flex items-center gap-2">
                    <svg
                        class="h-5 w-5"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M12 4.5v15m7.5-7.5h-15"
                        ></path>
                    </svg>
                    <span class="text-sm">برداشت از صندوق</span>
                </div>
            </button>

            <button
                class="shrink-0 rounded-lg bg-green-600 px-4 py-2 text-white transition-all hover:bg-green-500"
                type="button"
                @click="getOrderExcelModal = true, lock = true"
            >
                <div class="flex items-center gap-2">
                    <svg
                        class="h-5 w-5"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"
                        ></path>
                    </svg>
                    <span class="text-sm">دریافت گزارشات بصورت اکسل</span>
                </div>
            </button>
        </div>
        <div class="flex items-center gap-3 rounded-lg bg-white px-6 py-2">
            <span class="text-gray-500">موجودی لحظه ی:</span> <span
                class="text-base font-bold">{{ formatMoney($financial->transactionsSum()) }}
                ریال</span>
        </div>
    </div>
    <div
        class="overflow-hidden rounded-t-xl"
        wire:ignore
        x-data="{ fillter: false }"
    >
        <div class="w-full rounded-t-xl bg-gray-900 px-3 py-1.5">
            <div class="flex items-center justify-between">
                <div>
                    @php
                        $count = \App\Models\FinancialTransaction::where('financial_fund_id', $financial->id)->count();
                    @endphp
                    <span class="block text-base text-white max-md:text-xs">تراکنش های {{ $financial->title }}
                        @if ($count > 0)
                            <span class="rounded-md bg-red-500 px-2 text-sm">{{ $count }}</span>
                    </span>
                    @endif
                </div>
                <div class="flex items-center gap-3">
                    <div
                        class="flex items-center gap-4"
                        x-data="playerState()"
                        x-init="checkState()"
                    >

                        <div x-show="isPlaying">
                            <span
                                class="text-sm text-gray-500"
                                x-text="countdown"
                            ></span>
                            <span class="text-sm text-gray-500">ثانیه</span>
                        </div>
                        <div
                            class="text-sm font-bold text-gray-100"
                            x-show="isPlaying"
                        >
                            زمان بروزرسانی </div>
                        <button
                            class="flex items-center gap-2 text-white"
                            @click="togglePlay"
                        >
                            <span :class="!isPlaying ? 'text-green-500' : 'text-gray-500'">
                                <svg
                                    class="size-6"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M15.75 5.25v13.5m-7.5-13.5v13.5"
                                    />
                                </svg>
                            </span>
                            <span :class="isPlaying ? 'text-green-500' : 'text-gray-500'">
                                <svg
                                    class="size-6"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"
                                    />
                                </svg>
                            </span>
                        </button>

                        <button
                            class="flex items-center gap-2 p-2"
                            @click="fillter = !fillter"
                        >
                            <svg
                                class="h-6 w-6 text-white"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"
                                />
                            </svg>
                            <span class="text-base text-white">فیلتر</span>
                        </button>

                    </div>

                </div>
            </div>

        </div>

    </div>
    <div class="relative overflow-x-auto">
        @include('layouts.tools.loading')
        <table class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right">
            <thead
                class="bg-gray-200 text-xs uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
                wire:ignore
            >
                <tr>
                    <th
                        class="border border-gray-300 px-6 py-3 text-center"
                        scope="col"
                    >
                        <span class="text-sm max-md:whitespace-nowrap">ملبغ تراکنش (ریال)</span>
                    </th>
                    <th
                        class="border border-gray-300 px-6 py-3 text-center"
                        scope="col"
                    >
                        <span class="text-sm max-md:whitespace-nowrap">برداشت / واریز</span>
                    </th>
                    <th
                        class="border border-gray-300 px-6 py-3 text-center"
                        scope="col"
                    >
                        <span class="text-sm max-md:whitespace-nowrap">توضیحات</span>
                    </th>
                    <th
                        class="border border-gray-300 px-6 py-3 text-center"
                        scope="col"
                    >
                        <span class="text-sm max-md:whitespace-nowrap">نام مشتری / برداشت کننده</span>
                    </th>
                    <th
                        class="border border-gray-300 px-6 py-3 text-center"
                        scope="col"
                    >
                        <span class="text-sm max-md:whitespace-nowrap">مربوط به سفارش</span>
                    </th>

                    <th
                        class="border border-gray-300 px-6 py-3 text-center"
                        scope="col"
                    >
                        <span class="text-sm max-md:whitespace-nowrap">تنظیمات</span>
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach ($transactions as $key => $item)
                    @php
                        $order = \App\Models\Sefaresh::whereId($item->order_id)->first();
                    @endphp
                    <tr
                        class="border-b bg-white text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-600"
                        wire:key="{{ $item->id }}"
                    >
                        <td class="border border-gray-200 px-6 py-4 text-center">
                            @if ($item->type == 'deposit')
                                <span
                                    class="text-sm font-bold text-green-500 max-md:whitespace-nowrap"
                                    dir="ltr"
                                >+ {{ formatMoney($item->amount) }}</span>
                            @else
                                <span
                                    class="text-sm font-bold text-red-500 max-md:whitespace-nowrap"
                                    dir="ltr"
                                >- {{ formatMoney($item->amount) }}</span>
                            @endif
                        </td>
                        <td class="border border-gray-200 px-6 py-4 text-center">
                            @if ($item->type == 'deposit')
                                <span class="text-sm font-bold text-green-500 max-md:whitespace-nowrap">واریز</span>
                            @else
                                <span class="text-sm font-bold text-red-500 max-md:whitespace-nowrap">برداشت</span>
                            @endif
                        </td>
                        <td class="border border-gray-200 px-6 py-4 text-center">
                            <span
                                class="text-sm font-bold text-gray-700 max-md:whitespace-nowrap">{{ $item->description }}</span>
                        </td>
                        <td class="border border-gray-200 px-6 py-4 text-center">
                            @if ($item->type == 'deposit')
                                @if ($order)
                                    <span
                                        class="text-sm font-bold max-md:whitespace-nowrap"
                                        dir="ltr"
                                    > {{ $order?->subscribe?->fullname ?? '-' }}</span>
                                @endif
                            @else
                                <span
                                    class="text-sm font-bold max-md:whitespace-nowrap"
                                    dir="ltr"
                                > {{ $item?->user?->fullname ?? '-' }}</span>
                            @endif
                        </td>
                        <td class="border border-gray-200 px-6 py-4 text-center">
                            @if ($order)
                                <a
                                    class="rounded-lg bg-gray-100 px-3 py-1.5 text-gray-700 transition-all hover:bg-red-500 hover:text-white"
                                    href="{{ route('admin-dashboard-show-order', ['orderId' => $order->id]) }}"
                                    target="_blank"
                                >
                                    {{ $order->code }}
                                </a>
                            @endif
                        </td>
                        <td
                            class="border border-gray-200 px-6 py-4 text-center"
                            wire:key="{{ $item->id }}"
                        >
                            @if ($item->type == 'deposit')
                                <button
                                    class="cursor-not-allowed rounded-md bg-gray-100 px-3 py-2 text-sm text-gray-400 transition-all hover:bg-gray-100"
                                    type="button"
                                >
                                    <span class="flex items-center gap-2">
                                        <svg
                                            class="size-4"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke-width="1.5"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                                            />
                                        </svg>
                                        <span class="text-xs max-md:whitespace-nowrap">ویرایش</span>
                                    </span>
                                </button>
                            @else
                                <button
                                    class="rounded-md bg-gray-800 px-3 py-2 text-sm text-white transition-all hover:bg-gray-700"
                                    type="button"
                                    x-on:click="$dispatch('openModal',{component:'dashboard.admin.reports.financial-fund.withdrawal.show-withdrawal',arguments:{ transactionId:'{{ $item->id }}'}})"
                                >
                                    <span class="flex items-center gap-2">
                                        <svg
                                            class="size-4"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke-width="1.5"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                                            />
                                        </svg>
                                        <span class="text-xs max-md:whitespace-nowrap">ویرایش</span>
                                    </span>
                                </button>
                            @endif
                        </td>

                    </tr>
                @endforeach

            </tbody>
        </table>
        <div class="bg-white p-5">
            {{ $transactions->links(data: ['dark' => false]) }}
        </div>
        @if ((isset($transactions) && $transactions->count() == 0) || empty($transactions))
            <div class="flex h-96 w-full items-center justify-center bg-white">
                <p class="text-base text-gray-500">هیچ تراکنشی ثبت نشده است!</p>
            </div>
        @endif
    </div>

</div>
