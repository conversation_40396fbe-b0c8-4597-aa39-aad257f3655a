<div class="relative overflow-y-auto">
    {{-- @include('layouts.tools.loading') --}}
    <div
        class="flex items-center justify-between"
        wire:ignore
    >
        <div class="grid grid-cols-1 gap-3 py-3 md:grid-cols-12">

            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-gray-100"
                    for="phone"
                >شماره تماس:</label>

                <div class="relative">
                    <input
                        class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none transition-all focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="phone"
                        type="tel"
                        wire:model.lazy="data.phone"
                    >
                    <span class="absolute left-2 top-2 z-10">
                        <svg
                            class="inline h-5 w-5 animate-spin text-red-600 dark:text-red-500"
                            role="status"
                            aria-hidden="true"
                            wire:loading
                            wire:target="data.phone"
                            viewBox="0 0 100 101"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                fill="#E5E7EB"
                            />
                            <path
                                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                fill="currentColor"
                            />
                        </svg>
                    </span>
                </div>
                @error('data.phone')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-4">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-gray-100"
                    for="title"
                >نام و نام خانوادگی:</label>
                <div class="relative">
                    <input
                        class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-sm font-bold text-gray-700 outline-none transition-all focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="title"
                        type="text"
                        wire:model="data.title"
                    >
                    <button
                        class="absolute left-2 top-1.5 rounded bg-gray-200 p-1 transition-all hover:bg-gray-800 hover:text-white dark:bg-gray-900 dark:text-gray-100 dark:hover:bg-gray-800"
                        type="button"
                        @click="getSubscribeModal = true, lock = true"
                    >
                        <svg
                            class="h-5 w-5"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
                            />
                        </svg>
                    </button>
                </div>
                @error('data.title')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2 dark:bg-gray-900"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-2">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-gray-100"
                    for="code"
                >کد مشتری:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="code"
                    type="text"
                    wire:model="data.code"
                >
                @error('data.code')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="md:col-span-3">
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-gray-100"
                    for="gold18k"
                >قیمت روز طلا (خودکار):</label>
                <div class="relative">

                    <input
                        class="block w-full rounded-lg border-2 border-yellow-600 bg-yellow-200 p-2 text-left text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 disabled:cursor-not-allowed disabled:border-gray-200 disabled:bg-gray-100 disabled:text-gray-400 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="gold18k"
                        type="text"
                        :disabled="!lockButtonGold18k"
                        onkeyup="javascript:this.value=Comma(this.value);"
                        wire:model="data.gold18k"
                    >
                    <button
                        class="absolute right-10 top-1.5 rounded bg-yellow-300 p-1 transition-all hover:bg-gray-800 hover:text-white dark:bg-gray-900 dark:text-gray-100 dark:hover:bg-gray-800"
                        type="button"
                        wire:loading.remove
                        wire:target="acceptGold"
                        wire:click="acceptGold"
                        x-show="lockButtonGold18k"
                    >

                        <svg
                            class="h-5 w-5"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="m4.5 12.75 6 6 9-13.5"
                            />
                        </svg>

                    </button>
                    <span class="absolute right-10 top-2">
                        <svg
                            class="inline h-5 w-5 animate-spin text-red-600 dark:text-red-500"
                            role="status"
                            aria-hidden="true"
                            wire:loading
                            wire:target="acceptGold"
                            viewBox="0 0 100 101"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                fill="#E5E7EB"
                            />
                            <path
                                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                fill="currentColor"
                            />
                        </svg>
                    </span>
                    <span class="absolute right-2 top-2">
                        <svg
                            class="inline h-5 w-5 animate-spin text-red-600 dark:text-red-500"
                            role="status"
                            aria-hidden="true"
                            wire:loading
                            wire:target="loadGold18k"
                            viewBox="0 0 100 101"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                fill="#E5E7EB"
                            />
                            <path
                                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                fill="currentColor"
                            />
                        </svg>
                    </span>
                    <button
                        class="absolute right-2 top-1.5 rounded bg-yellow-300 p-1 transition-all hover:bg-gray-800 hover:text-white dark:bg-gray-900 dark:text-gray-100 dark:hover:bg-gray-800"
                        type="button"
                        x-show="lockButtonGold18k"
                        wire:loading.remove
                        wire:target="loadGold18k"
                        wire:click="loadGold18k"
                    >

                        <svg
                            class="h-5 w-5"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M7.5 21 3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5"
                            />
                        </svg>
                    </button>
                    <button
                        class="absolute right-2 top-1.5 cursor-not-allowed rounded bg-gray-200 p-1 text-gray-400 transition-all dark:bg-gray-900 dark:text-gray-100 dark:hover:bg-gray-800"
                        type="button"
                        x-show="!lockButtonGold18k"
                    >

                        <svg
                            class="h-5 w-5"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M7.5 21 3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5"
                            />
                        </svg>
                    </button>
                    <button
                        class="absolute right-10 top-1.5 cursor-not-allowed rounded bg-gray-200 p-1 text-gray-400 transition-all dark:bg-gray-900 dark:text-gray-100 dark:hover:bg-gray-800"
                        type="button"
                        x-show="!lockButtonGold18k"
                    >

                        <svg
                            class="h-5 w-5"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="m4.5 12.75 6 6 9-13.5"
                            />
                        </svg>
                    </button>
                </div>

                @error('gold18k')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
        </div>
        <div class="flex items-center gap-2">
            <div>
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-gray-100"
                    for="factor_number"
                >شماره فاکتور:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 disabled:border-gray-200 disabled:bg-gray-100 disabled:text-gray-400 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="factor_number"
                    type="text"
                    readonly
                    :disabled="!lockButtonGold18k"
                    wire:model="data.factor_number"
                >
                @error('data.factor_number')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div>
                <label
                    class="mb-2 block text-sm font-bold text-gray-700 dark:text-gray-100"
                    for="factor_create_date"
                >تاریخ:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="factor_create_date"
                    type="text"
                    wire:model.lazy="data.factor_create_date"
                >
                @error('data.factor_create_date')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
        </div>
    </div>
    <div
        class="flex items-center gap-6 border-t border-gray-200 py-2 pt-3"
        x-data="{ showForm: $wire.entangle('data.gift') }"
    >

        <div class="flex items-center">

            <input
                class="peer hidden"
                id="custom-box"
                type="checkbox"
                wire:model.live.debounce.800ms="data.gift"
                @click="showForm = !showForm"
            >
            <label
                class="relative flex h-6 cursor-pointer select-none pr-8 text-sm font-bold text-gray-700 after:absolute after:right-0 after:flex after:h-6 after:w-6 after:items-center after:justify-center after:rounded after:border-2 after:border-gray-600 after:bg-white after:transition-[background-color] after:duration-300 after:ease-in after:content-[''] peer-checked:after:bg-green-300 peer-checked:after:font-bold peer-checked:after:text-green-800 peer-checked:after:transition-[background-color] peer-checked:after:duration-300 peer-checked:after:ease-in peer-checked:after:content-['x']"
                for="custom-box"
            >
                این فاکتور به نام شخص دیگری صادر شود
            </label>
        </div>
        <div
            class="flex items-center gap-3"
            x-show="showForm"
        >
            <div class="flex items-center gap-3">
                <label
                    class="mb-2 block whitespace-nowrap text-sm font-bold text-gray-700 dark:text-gray-100"
                    for="fullnameR"
                >نام و نام خانوادگی:</label>
                <div class="relative">
                    <input
                        class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-sm font-bold text-gray-700 outline-none transition-all focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="fullnameR"
                        type="text"
                        wire:model.live.debounce.500ms="data.fullnameR"
                    >
                </div>
                @error('data.fullnameR')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2 dark:bg-gray-900"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
            <div class="flex items-center gap-3">
                <label
                    class="mb-2 block whitespace-nowrap text-sm font-bold text-gray-700 dark:text-gray-100"
                    for="phoneR"
                >شماره تماس:</label>
                <input
                    class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                    id="phoneR"
                    type="text"
                    wire:model.live.debounce.500ms="data.phoneR"
                >
                @error('data.phoneR')
                    <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                class="text-sm font-bold text-red-600"
                            >{{ $message }}</span></span></div>
                @enderror
            </div>
        </div>
    </div>
    <div
        class="flex w-full items-center"
        wire:ignore
    >
        <div
            class="flex w-[3%] items-center justify-center rounded-tr-xl border border-gray-300 bg-gray-200 p-2 text-center text-gray-700 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-100">
            <span class="text-sm font-bold">#</span>
        </div>
        <div
            class="flex w-[8%] items-center justify-center border border-gray-300 bg-gray-200 p-2 text-center text-gray-700 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-100">
            <span class="text-sm font-bold">اتیکت</span>
        </div>
        <div
            class="flex w-[8%] items-center justify-center border border-gray-300 bg-gray-200 p-2 text-center text-gray-700 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-100">
            <span class="text-sm font-bold">کد سفارش</span>
        </div>
        <div
            class="flex w-[28%] items-center justify-center border border-gray-300 bg-gray-200 p-2 text-center text-gray-700 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-100 md:col-span-2">
            <span class="text-sm font-bold">نام کالا</span>
        </div>
        <div
            class="flex w-[5%] items-center justify-center border border-gray-300 bg-gray-200 p-2 text-center text-gray-700 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-100">
            <span class="text-sm font-bold">عیار</span>
        </div>
        <div
            class="flex w-[6%] items-center justify-center border border-gray-300 bg-gray-200 p-2 text-center text-gray-700 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-100">
            <span class="text-sm font-bold">وزن</span>
        </div>
        {{-- <div class="bg-gray-200 dark:bg-gray-900 text-gray-700  dark:text-gray-100 text-center p-2 flex items-center justify-center border border-gray-300 dark:border-gray-800 md:col-span-2">
            <span class="text-sm font-bold">قیمت روز طلا</span>
        </div> --}}
        <div
            class="flex w-[6%] items-center justify-center border border-gray-300 bg-gray-200 p-2 text-center text-gray-700 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-100">
            <span class="text-sm font-bold">اجرت</span>
        </div>

        <div
            class="flex w-[6%] items-center justify-center border border-gray-300 bg-gray-200 p-2 text-center text-gray-700 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-100">
            <span class="text-sm font-bold">سود</span>
        </div>
        <div
            class="flex w-[6%] items-center justify-center border border-gray-300 bg-gray-200 p-2 text-center text-gray-700 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-100">
            <span class="text-sm font-bold">مالیات</span>
        </div>
        <div
            class="flex w-[11%] items-center justify-center border border-gray-300 bg-gray-200 p-2 text-center text-gray-700 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-100">
            <div class="flex items-center justify-between gap-2">
                <div class="border-l border-gray-300 pl-2">
                    <span class="text-xs font-bold">بسته بندی</span>
                </div>
                <div>
                    <span class="text-xs font-bold">پست</span>
                </div>
            </div>
        </div>
        <div
            class="flex w-[15%] items-center justify-center rounded-tl-xl border border-gray-300 bg-gray-200 p-2 text-center text-gray-700 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-100 md:col-span-2">
            <span class="text-sm font-bold">جمع کل</span>
        </div>
    </div>
    <div>

        <div wire:ignore>

            @foreach ($items as $key => $item)
                <livewire:dashboard.admin.order.factor.item
                    :gold18k="$data['gold18k']"
                    :keyItem="$key"
                    :facorItemId="$item->id"
                    wire:key="item-{{ $key . Str::uuid() . now()->timestamp . rand(0, 9910999999) }}"
                />
            @endforeach

            <livewire:dashboard.admin.order.factor.sum
                :order="$order"
                wire:key="sum-{{ Str::uuid() }}"
            />
        </div>
    </div>

</div>
