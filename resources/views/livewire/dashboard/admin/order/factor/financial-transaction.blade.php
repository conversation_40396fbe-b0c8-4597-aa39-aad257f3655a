<div>
    <form
        class="relative"
        wire:submit="save"
    >
        @include('layouts.tools.loading-dot')
        <div class="">
            <div class="mb-3 border-b border-gray-200 pb-3">
                <h3 class="text-base font-bold text-gray-800">نحوه دریافت وجه سفارش</h3>
            </div>
            <form class="grid w-full grid-cols-2 gap-3">
                <div class="mb-2">
                    <label
                        class="mb-2 block text-sm font-bold text-gray-800"
                        for="total"
                    >مبلغ دریافتی (ریال):</label>
                    <div class="relative">
                        <input
                            class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm font-bold text-gray-800 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-900 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            id="total"
                            type="tel"
                            value="0"
                            wire:model="data.total"
                            onkeyup="javascript:this.value=Comma(this.value);"
                            placeholder="0"
                            dir="ltr"
                        >
                        <span class="absolute left-2 top-3">
                            <svg
                                class="inline h-5 w-5 animate-spin text-red-600 dark:text-red-500"
                                role="status"
                                aria-hidden="true"
                                wire:loading
                                viewBox="0 0 100 101"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                    fill="#E5E7EB"
                                />
                                <path
                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </span>
                    </div>
                    @error('total')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div
                    class=""
                    wire:ignore
                >
                    <label
                        class="mb-2 block text-sm font-bold text-gray-800"
                        for="financialId"
                    >انتخاب صندوق واریزی:</label>
                    <select
                        class="block w-full rounded-lg border-2 border-gray-300 bg-white p-2.5 text-sm text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                        id="financialId"
                        wire:model="data.financialId"
                    >
                        <option selected="">--انتخاب کنید--</option>
                        @foreach (\App\Models\FinancialFund::where('status', 'active')->get() as $item)
                            <option value="{{ $item->id }}">{{ $item->title }}</option>
                        @endforeach

                    </select>
                    @error('financialId')
                        <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                    class="text-sm text-red-600"
                                >{{ $message }}</span></span></div>
                    @enderror
                </div>
                <div class="flex items-center justify-between md:col-span-2">
                    <button
                        class="mt-2 rounded-xl bg-red-500 px-4 py-2 text-white transition-all hover:bg-red-600 disabled:bg-gray-200 disabled:text-gray-400"
                        type="submit"
                    >
                        <svg
                            class="inline h-6 w-6 animate-spin text-red-700"
                            role="status"
                            aria-hidden="true"
                            wire:loading
                            wire:target="save"
                            viewBox="0 0 100 101"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                fill="#E5E7EB"
                            ></path>
                            <path
                                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                fill="currentColor"
                            ></path>
                        </svg>
                        <span class="text-sm">ثبت در لیست</span>
                    </button>
                </div>
            </form>
            <div class="mt-3">
                <table class="w-full border-collapse border border-slate-400">
                    <thead>
                        <tr>
                            <th class="border border-slate-300 text-center">ردیف</th>
                            <th class="border border-slate-300 text-center">مبلغ دریافتی (ریال)</th>
                            <th class="border border-slate-300 text-center">صندوق</th>
                            <th class="border border-slate-300 text-center">...</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (isset($transactions) && $transactions != null)
                            @foreach ($transactions as $key => $item)
                                <tr wire:key="{{ $key }}">
                                    <td class="border border-slate-300 p-2 text-center">{{ $key + 1 }}
                                    </td>

                                    <td class="border border-slate-300 p-2 text-center">
                                        <span class="text-sm">{{ formatMoney($item->amount) }}</span>
                                    </td>
                                    <td class="border border-slate-300 p-2 text-center">
                                        <span class="text-sm">
                                            {{ $item->fund ? $item->fund->title : '—' }}
                                        </span>
                                    </td>
                                    <td class="border border-slate-300 p-2 text-center">
                                        <button
                                            class="text-gray-400 transition-all hover:text-red-500"
                                            type="button"
                                            wire:click="removeItem({{ $item->id }})"
                                        >
                                            <svg
                                                class="size-4"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                                />
                                            </svg>

                                        </button>
                                    </td>
                                </tr>
                            @endforeach
                        @endif
                    </tbody>
                </table>
                @if ($transactions->count() == 0)
                    <div
                        class="mt-3 flex items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-5">
                        <p class="text-base text-gray-500">هیچ تراکنشی ثبت نشده</p>
                    </div>
                @endif
            </div>
        </div>
    </form>

</div>
