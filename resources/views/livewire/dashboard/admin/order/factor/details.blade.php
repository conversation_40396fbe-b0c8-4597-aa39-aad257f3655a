<div class="pb-16">
    <div class="rounded-xl bg-white p-5 shadow-xl dark:bg-gray-900">
        <form
            class="relative"
            wire:submit="store"
        >
            @include('layouts.tools.loading-dot')
            <div class="">
                <div class="mb-3 border-b border-gray-200 pb-3">
                    <h3 class="text-base font-bold text-gray-800">جزئیات مالی سفارش</h3>
                </div>
                <div class="grid grid-cols-1 gap-3 max-md:grid-cols-2 md:grid-cols-2">
                    <div class="">
                        <label
                            class="mb-2 block text-sm font-bold text-gray-800"
                            for="total"
                        >مبلغ کل کالا (ریال):</label>
                        <div class="relative">
                            <input
                                class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm font-bold text-gray-800 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-900 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                                id="total"
                                type="tel"
                                value="0"
                                wire:model="total"
                                readonly
                                placeholder="0"
                                dir="ltr"
                            >
                            <span class="absolute left-2 top-3">
                                <svg
                                    class="inline h-5 w-5 animate-spin text-red-600 dark:text-red-500"
                                    role="status"
                                    aria-hidden="true"
                                    wire:loading
                                    viewBox="0 0 100 101"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                        fill="#E5E7EB"
                                    />
                                    <path
                                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                        fill="currentColor"
                                    />
                                </svg>
                            </span>
                        </div>
                        @error('total')
                            <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                        class="text-sm text-red-600"
                                    >{{ $message }}</span></span></div>
                        @enderror
                    </div>
                    <div
                        class=""
                        wire:ignore
                    >
                        <label
                            class="mb-2 block text-sm font-bold text-gray-800"
                            for="commission"
                        >پورسانت:</label>
                        <input
                            class="price block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm font-bold text-gray-800 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            id="commission"
                            type="tel"
                            onkeyup="javascript:this.value=Comma(this.value);"
                            placeholder="0"
                            wire:model.live.debounce.700ms="commission"
                            dir="ltr"
                        >
                        @error('commission')
                            <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                        class="text-sm text-red-600"
                                    >{{ $message }}</span></span></div>
                        @enderror
                    </div>
                    <div class="">
                        <label
                            class="mb-2 block text-sm font-bold text-gray-800"
                            for="countWeight"
                        >جمع گرم کالا:</label>
                        <input
                            class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm font-bold text-gray-800 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-900 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            id="countWeight"
                            type="tel"
                            value="0"
                            wire:model="countWeight"
                            placeholder="0"
                            dir="ltr"
                        >
                        @error('countWeight')
                            <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                        class="text-sm text-red-600"
                                    >{{ $message }}</span></span></div>
                        @enderror
                    </div>
                    <div class="">
                        <label
                            class="mb-2 block text-sm font-bold text-gray-800"
                            for="sender"
                        >جمع هزینه ارسال:</label>
                        <input
                            class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm font-bold text-gray-800 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-900 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            id="sender"
                            type="tel"
                            value="0"
                            wire:model="sender"
                            placeholder="0"
                            dir="ltr"
                        >
                        @error('sender')
                            <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                        class="text-sm text-red-600"
                                    >{{ $message }}</span></span></div>
                        @enderror
                    </div>
                    <div class="">
                        <label
                            class="mb-2 block text-sm font-bold text-gray-800"
                            for="packing"
                        >جمع هزینه بسته بندی:</label>
                        <input
                            class="block w-full rounded-lg border-2 border-gray-300 bg-gray-50 p-2.5 text-center text-sm font-bold text-gray-800 outline-none focus:border-red-600 focus:ring-red-600 disabled:bg-gray-900 dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            id="packing"
                            type="tel"
                            value="0"
                            wire:model="packing"
                            placeholder="0"
                            dir="ltr"
                        >
                        @error('packing')
                            <div class="pb-2 pt-3"><span class="mt-2 rounded-lg bg-red-50 p-1 px-2"><span
                                        class="text-sm text-red-600"
                                    >{{ $message }}</span></span></div>
                        @enderror
                    </div>

                </div>
                <div class="mt-3">
                    <table class="w-full border-collapse border border-slate-400">
                        <thead>
                            <tr>
                                <th class="border border-slate-300 text-center">ردیف</th>
                                <th class="border border-slate-300 text-center">قیمت آیتم</th>
                                <th class="border border-slate-300 text-center">بسته بندی</th>
                                <th class="border border-slate-300 text-center">پست</th>
                                <th class="border border-slate-300 text-center">جمع</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (isset($factors) && $factors != null)
                                @foreach ($factors as $key => $item)
                                    @if ($item->total != null && $item->total != '0')
                                        <tr wire:key="{{ $key }}">
                                            <td class="border border-slate-300 p-2 text-center">{{ $key + 1 }}</td>
                                            <td class="border border-slate-300 p-2 text-center">
                                                @php
                                                    $total = floatval(str_replace(',', '', $item->total));
                                                    $sender = floatval(str_replace(',', '', $item->sender));
                                                    $packing = floatval(str_replace(',', '', $item->packing));
                                                @endphp
                                                <span
                                                    class="text-sm">{{ formatMoney($total - $sender - $packing) }}</span>
                                            </td>
                                            <td class="border border-slate-300 p-2 text-center">
                                                <span class="text-sm">{{ formatMoney($item->packing) }}</span>
                                            </td>
                                            <td class="border border-slate-300 p-2 text-center">
                                                <span class="text-sm">{{ formatMoney($item->sender) }}</span>
                                            </td>
                                            <td class="border border-slate-300 p-2 text-center">
                                                <span class="text-sm">{{ formatMoney($item->total) }}</span>
                                            </td>
                                        </tr>
                                    @endif
                                @endforeach
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </form>
        <div
            class="relative mt-6 border-2 border-gray-100 p-3 dark:border-gray-800"
            wire:ignore
        >
            <span class="absolute -top-3 rounded-lg bg-gray-800 px-6 py-1 text-sm text-white">فیش واریزی / محصول</span>
            <div class="mt-4 grid grid-cols-1 gap-3 md:grid-cols-2">

                <livewire:dashboard.admin.order.upload-file
                    title="بیعانه 1"
                    border="border-dashed border-2 border-red-500"
                    :image="$order->image1"
                    :button=false
                >

                    <livewire:dashboard.admin.order.upload-file
                        title="محصول"
                        border="border-dashed border-2 border-gray-300"
                        :image="$order->image3"
                        :button=false
                    >
            </div>
        </div>
    </div>
    <div
        class="sticky top-3 mt-3 rounded-xl bg-white p-5 shadow-xl dark:bg-gray-900"
        wire:ignore
    >
        <livewire:dashboard.admin.order.factor.financial-transaction :orderId="$this->order->id" />
    </div>
</div>
