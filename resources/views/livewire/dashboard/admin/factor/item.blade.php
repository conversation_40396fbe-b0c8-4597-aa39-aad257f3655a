<div class="flex w-full items-center">
    <div
        class="flex w-[3%] items-center justify-center bg-gray-100 text-center dark:border-gray-600 dark:bg-gray-600"
        wire:ignore
    >
        <input
            class="block w-full border-2 border-gray-100 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
            id="id"
            type="text"
            value="{{ $keyItem + 1 }}"
            disabled
        >
    </div>
    <div
        class="flex w-[8%] items-center justify-center bg-gray-100 text-center dark:border-gray-600 dark:bg-gray-600"
        wire:ignore
    >
        <input
            class="block w-full border-2 border-gray-100 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
            id="eticket"
            type="text"
            dir="ltr"
            wire:model.live.debounce.700ms="data.eticket"
        >
    </div>
    <div
        class="flex w-[8%] items-center justify-center bg-gray-100 text-center dark:border-gray-600 dark:bg-gray-600"
        wire:ignore
    >
        <input
            class="block w-full border-2 border-gray-100 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
            id="code"
            type="text"
            dir="ltr"
            wire:model.live.debounce.700ms="data.code"
        >
    </div>
    <div
        class="flex w-[28%] items-center justify-center bg-gray-100 text-center dark:border-gray-600 dark:bg-gray-600 md:col-span-2"
        wire:ignore
    >
        <input
            class="block w-full border-2 border-gray-100 bg-white p-2 text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
            id="title"
            type="text"
            wire:model.live.debounce.700ms="data.title"
        >
    </div>
    <div
        class="flex w-[5%] items-center justify-center bg-gray-100 text-center dark:border-gray-600 dark:bg-gray-600"
        wire:ignore
    >
        <input
            class="block w-full border-2 border-gray-100 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
            id="cutie"
            type="text"
            dir="ltr"
            wire:model.live.debounce.700ms="data.cutie"
        >
    </div>
    <div
        class="flex w-[6%] items-center justify-center bg-gray-100 text-center dark:border-gray-600 dark:bg-gray-600"
        wire:ignore
    >
        <input
            class="block w-full border-2 border-gray-100 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
            id="weight"
            type="text"
            dir="ltr"
            wire:model.live.debounce.700ms="data.weight"
        >
    </div>
    {{-- <div  class="bg-gray-100 dark:bg-gray-600 text-center flex items-center justify-center  dark:border-gray-600 md:col-span-2">
        <input type="text" id="gold18k" wire:model.live.debounce.700ms="gold18k" onkeyup="javascript:this.value=Comma(this.value);"  class="bg-white outline-none border-2 border-gray-100 text-center  text-gray-700 text-sm  focus:ring-red-600 focus:border-red-600 block w-full p-2 dark:bg-gray-500 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-red-600 dark:focus:border-red-600">
    </div> --}}
    <div
        class="flex w-[6%] items-center justify-center bg-gray-100 text-center dark:border-gray-600 dark:bg-gray-600"
        wire:ignore
    >
        <div class="relative">
            <input
                class="block w-full border-2 border-gray-100 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="cutie"
                type="text"
                dir="ltr"
                wire:model.live.debounce.700ms="data.construction_wages"
            >
            <span class="absolute left-2 top-3 text-sm text-gray-400">%</span>
        </div>
    </div>
    <div
        class="flex w-[6%] items-center justify-center bg-gray-100 text-center dark:border-gray-600 dark:bg-gray-600"
        wire:ignore
    >
        <div class="relative">
            <input
                class="block w-full border-2 border-gray-100 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="cutie"
                type="text"
                dir="ltr"
                wire:model.live.debounce.700ms="data.profit"
            >
            <span class="absolute left-2 top-3 text-sm text-gray-400">%</span>
        </div>
    </div>
    <div
        class="flex w-[6%] items-center justify-center bg-gray-100 text-center dark:border-gray-600 dark:bg-gray-600"
        wire:ignore
    >
        <div class="relative">
            <input
                class="block w-full border-2 border-gray-100 bg-white p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="tax"
                type="text"
                dir="ltr"
                wire:model.live.debounce.700ms="data.tax"
            >
            <span class="absolute left-2 top-3 text-sm text-gray-400">%</span>
        </div>
    </div>
    <div class="flex w-[11%] items-center justify-center bg-white text-center dark:border-gray-600 dark:bg-gray-600">
        <div class="flex items-center gap-2">
            <div wire:key="box-remove-{{ Str::uuid() . now()->timestamp . rand(10, 991099) }}">
                @if ($data['packing'] != null && $data['packing'] != 0 && $data['packing'] != '0')
                    <button
                        class="flex h-6 w-6 items-center justify-center rounded-full bg-red-500 p-2 text-white transition-all hover:bg-red-500 hover:text-white"
                        type="button"
                        wire:click="clearType('packing')"
                        wire:key="packing-{{ Str::uuid() }}"
                    >
                        <span class="text-base">
                            x
                        </span>
                    </button>
                @else
                    <button
                        class="flex h-6 w-6 items-center justify-center rounded-full bg-gray-100 p-2 transition-all hover:bg-red-500 hover:text-white"
                        type="button"
                        wire:key="remove-{{ Str::uuid() }}"
                        wire:click="addItemAmount('هزینه بسته بندی', '500,000', 'packing')"
                    >
                        <span>
                            <svg
                                class="size-4"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M12 4.5v15m7.5-7.5h-15"
                                />
                            </svg>
                        </span>
                    </button>
                @endif
            </div>
            <div
                class=""
                wire:key="box-{{ Str::uuid() . now()->timestamp . rand(10, 991099) }}"
            >
                @if ($data['sender'] != null && $data['sender'] != 0 && $data['sender'] != '0')
                    <button
                        class="flex h-6 w-6 items-center justify-center rounded-full bg-red-500 p-2 text-white transition-all hover:bg-red-500 hover:text-white"
                        type="button"
                        wire:key="sender-{{ Str::uuid() . now()->timestamp . rand(20, 9999999) }}"
                        wire:click="clearType('post')"
                    >
                        <span class="text-base">
                            x
                        </span>
                    </button>
                @else
                    <button
                        class="flex h-6 w-6 items-center justify-center rounded-full bg-gray-100 p-2 transition-all hover:bg-red-500 hover:text-white"
                        type="button"
                        wire:key="remove-sender-{{ Str::uuid() . now()->timestamp . rand(1, 9911199) }}"
                        wire:click="addItemAmount('هزینه ارسال پست', '1,500,000', 'post')"
                    >
                        <span>
                            <svg
                                class="size-4"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M12 4.5v15m7.5-7.5h-15"
                                />
                            </svg>
                        </span>
                    </button>
                @endif

            </div>
        </div>
    </div>
    <div
        class="flex w-[15%] items-center justify-center bg-gray-100 text-center dark:border-gray-600 dark:bg-gray-600 md:col-span-2">
        <div class="relative w-full">
            <span class="absolute left-2 top-2">
                <svg
                    class="inline h-5 w-5 animate-spin text-red-600 dark:text-red-500"
                    role="status"
                    aria-hidden="true"
                    wire:loading
                    viewBox="0 0 100 101"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="#E5E7EB"
                    />
                    <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentColor"
                    />
                </svg>
            </span>
            <div
                class="relative"
                wire:ignore
                x-cloak
                x-data="{
                    calc: false,
                    totalCalc: '',
                    clearTotal() {
                        this.totalCalc = '';
                    },
                    calcTotal(item) {
                        if (this.totalCalc === '0') {
                            this.totalCalc = item;
                        } else {
                            this.totalCalc += item;
                        }
                        this.formatTotal();
                    },
                    formatTotal() {
                        this.totalCalc = this.Comma(this.totalCalc.replace(/,/g, ''));
                    },
                    deleteLast() {
                        if (this.totalCalc.length > 0) {
                            this.totalCalc = this.totalCalc.slice(0, -1);
                            this.formatTotal();
                        }
                    },
                    calculateResult() {
                        this.totalCalc = this.Comma(eval(this.totalCalc.replace(/,/g, '')).toString());
                    },
                    Comma(Num) {
                        Num = Num.toString().replace(/,/g, '');
                        if (isNaN(Num) || Num === '') {
                            return '';
                        }
                        let negative = Num[0] === '-' ? '-' : '';
                        Num = Num.replace('-', '');
                        let parts = Num.split('.');
                        let integerPart = parts[0];
                        let decimalPart = parts.length > 1 ? '.' + parts[1] : '';
                        let rgx = /(\d+)(\d{3})/;
                        while (rgx.test(integerPart)) {
                            integerPart = integerPart.replace(rgx, '$1' + ',' + '$2');
                        }
                        return negative + integerPart + decimalPart;
                    }
                }"
            >
                <button
                    class="absolute left-1 top-0 z-50 p-2 text-gray-400 transition-all hover:text-red-500"
                    type="button"
                    @click="calc = true"
                >
                    <svg
                        class="size-6"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M15.75 15.75V18m-7.5-6.75h.008v.008H8.25v-.008Zm0 2.25h.008v.008H8.25V13.5Zm0 2.25h.008v.008H8.25v-.008Zm0 2.25h.008v.008H8.25V18Zm2.498-6.75h.007v.008h-.007v-.008Zm0 2.25h.007v.008h-.007V13.5Zm0 2.25h.007v.008h-.007v-.008Zm0 2.25h.007v.008h-.007V18Zm2.504-6.75h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V13.5ZM8.25 6h7.5v2.25h-7.5V6ZM12 2.25c-1.892 0-3.758.11-5.593.322C5.307 2.7 4.5 3.65 4.5 4.757V19.5a2.25 2.25 0 0 0 2.25 2.25h10.5a2.25 2.25 0 0 0 2.25-2.25V4.757c0-1.108-.806-2.057-1.907-2.185A48.507 48.507 0 0 0 12 2.25Z"
                        />
                    </svg>
                </button>
                <div
                    class="absolute left-2 top-10 z-[999] mb-6 h-auto w-60 rounded bg-gray-900 p-4 shadow-xl"
                    x-show="calc"
                    @click.away="calc = false, total = ''"
                    x-transition:enter="transition ease-out duration-100"
                    x-transition:enter-start="opacity-0 scale-75"
                    x-transition:enter-end="opacity-100 scale-100"
                    x-transition:leave="transition ease-in duration-100"
                    x-transition:leave-start="opacity-100 scale-100"
                    x-transition:leave-end="opacity-0 scale-75"
                >
                    <div class="bg-gray-800 text-yellow-300">
                        <input
                            class="block w-full rounded-lg border-2 border-gray-700 bg-gray-800 p-2 text-center text-sm font-bold text-yellow-300 shadow-2xl outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                            type="text"
                            dir="ltr"
                            x-model="totalCalc"
                            onkeyup="javascript:this.value=Comma(this.value);"
                        >
                    </div>
                    <div class="mt-3 grid grid-cols-7 gap-2">
                        <div class="col-span-2 flex flex-col gap-2">
                            <button
                                class="flex h-8 w-full items-center justify-center rounded-md bg-yellow-300 p-2 transition-all hover:bg-yellow-400"
                                @click="deleteLast()"
                            >
                                <span class="flex items-center gap-2">
                                    <svg
                                        class="size-6"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M12 9.75 14.25 12m0 0 2.25 2.25M14.25 12l2.25-2.25M14.25 12 12 14.25m-2.58 4.92-6.374-6.375a1.125 1.125 0 0 1 0-1.59L9.42 4.83c.21-.211.497-.33.795-.33H19.5a2.25 2.25 0 0 1 2.25 2.25v10.5a2.25 2.25 0 0 1-2.25 2.25h-9.284c-.298 0-.585-.119-.795-.33Z"
                                        />
                                    </svg>
                                </span>
                            </button>
                            <button
                                class="flex h-8 w-full items-center justify-center rounded-md bg-yellow-300 p-2 transition-all hover:bg-yellow-400"
                                @click="clearTotal(), $wire.setCalc(totalCalc)"
                            >
                                <span class="flex items-center gap-2">
                                    <span class="text-base text-gray-700">AC</span>
                                </span>
                            </button>
                        </div>
                        <div class="col-span-5">
                            <div class="flex flex-col gap-2">
                                <div class="grid grid-cols-3 gap-2">
                                    <button
                                        class="flex h-8 w-full items-center justify-center rounded-md bg-gray-800 p-2 transition-all hover:bg-red-500"
                                        @click="calcTotal('1')"
                                    >
                                        <span class="text-base text-white">1</span>
                                    </button>
                                    <button
                                        class="flex h-8 w-full items-center justify-center rounded-md bg-gray-800 p-2 transition-all hover:bg-red-500"
                                        @click="calcTotal('2')"
                                    >
                                        <span class="text-base text-white">2</span>
                                    </button>
                                    <button
                                        class="flex h-8 w-full items-center justify-center rounded-md bg-gray-800 p-2 transition-all hover:bg-red-500"
                                        @click="calcTotal('3')"
                                    >
                                        <span class="text-base text-white">3</span>
                                    </button>

                                </div>
                                <div class="grid grid-cols-3 gap-2">
                                    <button
                                        class="flex h-8 w-full items-center justify-center rounded-md bg-gray-800 p-2 transition-all hover:bg-red-500"
                                        @click="calcTotal('4')"
                                    >
                                        <span class="text-base text-white">4</span>
                                    </button>
                                    <button
                                        class="flex h-8 w-full items-center justify-center rounded-md bg-gray-800 p-2 transition-all hover:bg-red-500"
                                        @click="calcTotal('5')"
                                    >
                                        <span class="text-base text-white">5</span>
                                    </button>
                                    <button
                                        class="flex h-8 w-full items-center justify-center rounded-md bg-gray-800 p-2 transition-all hover:bg-red-500"
                                        @click="calcTotal('6')"
                                    >
                                        <span class="text-base text-white">6</span>
                                    </button>

                                </div>
                                <div class="grid grid-cols-3 gap-2">
                                    <button
                                        class="flex h-8 w-full items-center justify-center rounded-md bg-gray-800 p-2 transition-all hover:bg-red-500"
                                        @click="calcTotal('7')"
                                    >
                                        <span class="text-base text-white">7</span>
                                    </button>
                                    <button
                                        class="flex h-8 w-full items-center justify-center rounded-md bg-gray-800 p-2 transition-all hover:bg-red-500"
                                        @click="calcTotal('8')"
                                    >
                                        <span class="text-base text-white">8</span>
                                    </button>
                                    <button
                                        class="flex h-8 w-full items-center justify-center rounded-md bg-gray-800 p-2 transition-all hover:bg-red-500"
                                        @click="calcTotal('9')"
                                    >
                                        <span class="text-base text-white">9</span>
                                    </button>

                                </div>
                                <div class="grid grid-cols-3 gap-2">
                                    <button
                                        class="flex h-8 w-full items-center justify-center rounded-md bg-gray-800 p-2 transition-all hover:bg-red-500"
                                        @click="calcTotal('0')"
                                    >
                                        <span class="text-base text-white">0</span>
                                    </button>
                                    <button
                                        class="flex h-8 w-full items-center justify-center rounded-md bg-gray-800 p-2 transition-all hover:bg-red-500"
                                        @click="calcTotal('000')"
                                    >
                                        <span class="text-base text-white">000</span>
                                    </button>
                                    <button
                                        class="flex h-8 w-full items-center justify-center rounded-md bg-gray-800 p-2 transition-all hover:bg-red-500"
                                        @click="calcTotal('.')"
                                    >
                                        <span class="text-base text-white">.</span>
                                    </button>
                                </div>

                            </div>
                        </div>
                    </div>

                    <button
                        class="mt-3 flex h-8 w-full items-center justify-center rounded-md bg-red-500 p-5 transition-all hover:bg-red-600"
                        type="button"
                        @click="$wire.setCalc(totalCalc)"
                    >

                        <span class="text-base text-white">ثبت اطلاعات</span>
                    </button>
                </div>
            </div>

            <input
                class="@if ($data['sender'] != null || $data['packing'] != null) bg-indigo-200 @elseif($data['post'] == 'yes') bg-yellow-300 @else bg-green-200 @endif block w-full border-2 border-gray-100 p-2 text-center text-sm font-bold text-gray-700 outline-none focus:border-red-600 focus:ring-red-600 dark:border-gray-600 dark:bg-gray-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-red-600 dark:focus:ring-red-600"
                id="total"
                type="text"
                dir="ltr"
                wire:model.live.debounce.700ms="data.total"
                onkeyup="javascript:this.value=Comma(this.value);"
            >
        </div>
    </div>
</div>
