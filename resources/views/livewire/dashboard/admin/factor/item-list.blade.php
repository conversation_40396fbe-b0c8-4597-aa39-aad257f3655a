<div>

    <div wire:ignore>

        @foreach ($items as $key => $item)
            <livewire:dashboard.admin.factor.item
                :gold18k="$data['gold18k']"
                :keyItem="$key"
                :facorItemId="$item->id"
                :factorId="$factorId"
                wire:key="item-{{ $key . Str::uuid() . now()->timestamp . rand(0, 9910999999) }}"
            />
        @endforeach

        <livewire:dashboard.admin.factor.sum
            :order="$order"
            wire:key="sum-{{ Str::uuid() }}"
        />
    </div>
</div>
