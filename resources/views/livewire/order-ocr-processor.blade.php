<div class="mx-auto max-w-xl space-y-4 rounded-lg bg-white p-6 shadow-md">
    <h2 class="text-xl font-semibold text-gray-800">وضعیت پردازش تصاویر</h2>

    <div class="text-gray-700">
        <span class="font-semibold">وضعیت فعلی:</span>
        <span>{{ $status }}</span>
    </div>

    <div class="text-gray-700">
        <span class="font-semibold">پیشرفت:</span>
        <span>{{ $progress }} از {{ $total }}</span>
    </div>

    <progress
        class="h-4 w-full overflow-hidden rounded-md"
        value="{{ $progress }}"
        max="{{ $total }}"
    ></progress>

    <button
        class="rounded bg-blue-600 px-4 py-2 font-bold text-white transition hover:bg-blue-700 disabled:opacity-50"
        wire:click="processOrders"
        wire:loading.attr="disabled"
    >
        شروع پردازش
    </button>

    <div
        class="flex items-center space-x-2 text-blue-600"
        wire:loading
    >
        <svg
            class="h-5 w-5 animate-spin text-blue-600"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
        >
            <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
            ></circle>
            <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
            ></path>
        </svg>
        <span>در حال پردازش ... لطفاً صبر کنید</span>
    </div>
</div>
