{{-- <div
    class="min-h-screen bg-[#0F1123] p-4 text-white"
    dir="rtl"
>

    <nav>
        <div class="mb-4 flex flex-row-reverse items-center justify-between">
            <div class="flex flex-row-reverse items-center gap-3 rounded-md bg-gray-800 p-1 px-3">
                <svg
                    class="size-5"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M6.75 2.994v2.25m10.5-2.25v2.25m-14.252 13.5V7.491a2.25 2.25 0 0 1 2.25-2.25h13.5a2.25 2.25 0 0 1 2.25 2.25v11.251m-18 0a2.25 2.25 0 0 0 2.25 2.25h13.5a2.25 2.25 0 0 0 2.25-2.25m-18 0v-7.5a2.25 2.25 0 0 1 2.25-2.25h13.5a2.25 2.25 0 0 1 2.25 2.25v7.5m-6.75-6h2.25m-9 2.25h4.5m.002-2.25h.005v.006H12v-.006Zm-.001 4.5h.006v.006h-.006v-.005Zm-2.25.001h.005v.006H9.75v-.006Zm-2.25 0h.005v.005h-.006v-.005Zm6.75-2.247h.005v.005h-.005v-.005Zm0 2.247h.006v.006h-.006v-.006Zm2.25-2.248h.006V15H16.5v-.005Z"
                    />
                </svg>

                <span class="text-sm text-gray-300">1404/02/19</span>
            </div>
            <div class="flex flex-row-reverse items-center gap-2">
                <h1 class="text-lg font-bold">تیدا نرخ</h1>
                <button>
                    <svg
                        class="size-8"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M3.75 6.75h16.5M3.75 12h16.5M12 17.25h8.25"
                        />
                    </svg>

                </button>
            </div>
        </div>
    </nav>

</div> --}}
<div class="container mx-auto flex items-center justify-center p-16">
    <div
        class="space-y-4"
        x-data="{
            stream: null,
            startCamera() {
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    alert('مرورگر شما از دسترسی به دوربین پشتیبانی نمی‌کند یا صفحه در context امن بارگذاری نشده است.');
                    return;
                }
        
                navigator.mediaDevices.getUserMedia({ video: true })
                    .then((stream) => {
                        this.stream = stream;
                        this.$refs.video.srcObject = stream;
                    })
                    .catch((error) => {
                        alert('دوربین فعال نشد: ' + error.message);
                    });
            },
            takeSnapshot() {
                const video = this.$refs.video;
                const canvas = this.$refs.canvas;
                const snapshot = this.$refs.snapshot;
        
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
        
                const context = canvas.getContext('2d');
                context.drawImage(video, 0, 0, canvas.width, canvas.height);
        
                const dataURL = canvas.toDataURL('image/png');
        
                snapshot.src = dataURL;
                snapshot.classList.remove('hidden');
            },
            sendToServer() {
                const dataURL = this.$refs.canvas.toDataURL('image/png');
                Livewire.dispatch('webcam-captured', {
                    image: dataURL
                });
            },
        
        }"
    >
        <!-- پیش‌نمایش ویدیو -->
        <video
            class="h-48 w-64 border"
            x-ref="video"
            autoplay
        ></video>

        <!-- پیش‌نمایش عکس گرفته شده -->
        <canvas
            class="hidden"
            x-ref="canvas"
        ></canvas>
        <img
            class="hidden h-48 w-64 border"
            x-ref="snapshot"
        />

        <!-- دکمه‌ها -->
        <div class="flex gap-4">
            <button
                class="rounded bg-blue-500 px-4 py-2 text-white"
                type="button"
                @click="startCamera()"
            >روشن کردن دوربین</button>
            <button
                class="rounded bg-green-500 px-4 py-2 text-white"
                type="button"
                @click="takeSnapshot()"
            >گرفتن شات</button>
            <button
                class="rounded bg-purple-500 px-4 py-2 text-white"
                type="button"
                @click="sendToServer()"
            >ارسال به سرور</button>
        </div>
    </div>
</div>
