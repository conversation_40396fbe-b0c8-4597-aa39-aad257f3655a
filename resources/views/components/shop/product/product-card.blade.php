@foreach ($details as $key => $item)
    @php
        if (!isset($item?->eticket)) {
            $count = $item->available_stock - $item?->pd_out_stock;
        } else {
            $count = $item->available_stock;
        }
        $count_out_stock = $item?->pd_out_stock ?? $item?->p_out_stock;

    @endphp

    <div
        class="cursor-pointer"
        wire:loading.class="md:hidden"
        {{-- wire:target="updateFilter, sortByMethod, changePriceBetween, search, ClearFillter" --}}
        wire:key="{{ $key }}.'-single-product'"
    >
        <div
            class="@if (isset($item?->eticket)) md:max-h-[31rem] @endif relative w-full overflow-hidden rounded-lg border border-gray-200 bg-white p-1 dark:border-gray-800 dark:bg-gray-900 md:h-[31rem] md:min-h-[31rem] md:border-none">
            <div
                class="relative overflow-hidden rounded-lg"
                {{-- @if (isset($item?->eticket)) @click="$store.showProductDetails = true; lock = true;$wire.dispatch('show-product-details', { productId : {{ $item->product_detail_id }} })"
                @else
                    @click="$store.showListProduct = true; lock = true; $wire.dispatch('show-products', { productId : {{ $item->product_id }} })" @endif --}}
                @php
$imageUrl = $image_server_url . $item->product_image_url ?? '/assets/images/woocommerce-placeholder-400x400.png';
$setting = Cache::remember('settings_card_info', 60 * 60, function () {
    return \App\Models\Setting::whereIn('type', [
        'card_number',
        'fullname_card_number',
        'sheba_card_number'
    ])->pluck('body', 'type')->toArray();
});
$APP_TIDAMODE_SHOP_URL = env('APP_TIDAMODE_SHOP_URL');

$productArray = [
        'setting' => $setting,
        'product_title_fa' => $item->product_title_fa ?? '',
        'count' => $count,
        'count_out_stock' => $item?->fixed_amount != null ? $item?->p_out_stock : $item?->pd_out_stock,
        'product_detail_id' => $item->product_detail_id ?? '',
        'product_id' => $item->product_id ?? '',
        'product_image_url' =>  $imageUrl,
        'eticket' => $item->eticket ?? '',
        'category_title_fa' => $item->category_title_fa ?? '',
        'chain_size' => $item->chain_size ?? '',
        'color' => $item->color ?? '',
        'amount' => $item->amount ?? '',
        'available_stock' => $item->available_stock,
        'fixed_amount' => $item?->fixed_amount != null ? formatMoney($item?->fixed_amount) : formatMoney($item->amount),
        'branch' => $item->branch ?? '',
        'weight' => $item->weight ?? '',
        'construction_wages' => $item->construction_wages ?? '',
        'profit' => $item->profit ?? '',
        'tax' => $item->tax ?? '',
        'gold18k' => $item->gold18k ?? '',
        'amount_after_offer' =>  $item->amount_after_offer ?? '',
        'for_value' => $item->for_value ?? '',
        'productUrl' => $APP_TIDAMODE_SHOP_URL.'p/'.$item->product_id.'/'.$item->product_detail_id.'/'.hashId(auth()->user()->id)
            ]; @endphp
                @if (!$group) data-product='@json($productArray)'
            @click="$store.product.open(JSON.parse($el.dataset.product)); lock = true"
                        @else
                            @click="$store.showListProduct = true; lock = true; $wire.dispatch('show-products', { productId : {{ $item->product_id }} })" @endif
            >
                @if ($item?->fixed_amount == null)
                    @if (isset($item?->eticket))
                        <div
                            class="absolute left-2 top-3 z-10 flex items-center gap-1 overflow-hidden overflow-x-auto whitespace-nowrap">
                            <span
                                class="ml-2 rounded-md bg-yellow-300 px-3 text-sm text-gray-800 opacity-0 transition-all hover:scale-110 hover:opacity-100 max-md:hidden max-md:text-xs"
                            >
                                {{ shamsiDate($item->updated_at) }}
                            </span>

                            <span class="rounded-md bg-white px-3 text-sm text-gray-800 max-md:text-xs">
                                {{ $item->weight }} <span class="text-sm font-bold max-md:text-xs">گرم</span>
                            </span>
                            @if (
                                (optional($item)->fixed_amount == 0 || optional($item)->fixed_amount == null) &&
                                    (optional($item)->construction_wages == null || optional($item)->construction_wages == 0))
                            @else
                                @if ($item->amount !== $item->amount_after_offer)
                                    @php
                                        $currentProfit = (float) ($item->profit ?? 0); // سود ثابت - مثلاً 7
                                        $settings = \App\Models\Setting::where('type', 'profit_offer')
                                            ->pluck('body', 'type')
                                            ->toArray();
                                        $originalProfit = (float) ($settings['profit_offer'] ?? 0); // سود متغیر - مثلاً 3.5

                                        $discountPercent = 0;
                                        if ($currentProfit > 0 && $originalProfit < $currentProfit) {
                                            $discountPercent =
                                                (($currentProfit - $originalProfit) / $currentProfit) * 100;
                                        }
                                    @endphp
                                    @if ($discountPercent > 0)
                                        <span class="rounded-md bg-red-500 px-3 text-sm font-bold text-white">
                                            {{ round($discountPercent) }}%
                                        </span>
                                    @endif
                                @endif
                            @endif
                        </div>
                    @endif
                @endif
                @if ($item->fixed_amount == 0 || $item->fixed_amount == null)
                    @if ($item->construction_wages == 0 || $item->construction_wages == null)
                        <span
                            class="absolute right-2 top-3 z-10 rounded-md bg-yellow-300 px-3 text-sm text-gray-700 max-md:text-xs"
                        >
                            <span class="text-sm font-bold max-md:text-xs">بدون اجرت</span>
                        </span>
                    @endif
                @endif
                <div
                    class="absolute bottom-3 right-2 z-10 flex flex-wrap gap-1.5 max-md:bottom-2 max-md:flex-col md:items-center">
                    @if ($item?->fixed_amount == null)
                        @if (isset($item?->eticket))
                            <span
                                class="rounded-md bg-gray-100 px-3 text-sm text-gray-800 dark:bg-gray-800 dark:text-gray-100 max-md:text-xs"
                            >
                                <span class="text-sm font-bold max-md:text-xs">اتیکت</span> {{ $item->eticket }}
                            </span>
                            <span
                                class="rounded-md bg-gray-100 px-3 text-sm text-gray-800 dark:bg-gray-800 dark:text-gray-100 max-md:text-xs"
                            >
                                <span class="text-sm font-bold max-md:text-xs">سایز زنجیر</span>
                                {{ $item?->chain_size }}
                            </span>
                            <span
                                class="flex items-center gap-2 rounded-md bg-gray-100 px-3 text-sm text-gray-800 dark:bg-gray-800 dark:text-gray-100 max-md:text-xs"
                            >
                                <span class="text-sm font-bold max-md:text-xs">{{ $item->available_stock }}</span>
                                <span class="text-sm font-normal max-md:text-xs">موجودی</span>
                            </span>
                        @else
                            <span
                                class="flex items-center gap-2 rounded-md bg-gray-100 px-3 text-sm text-gray-800 dark:bg-gray-800 dark:text-gray-100 max-md:text-xs"
                            >
                                <span class="text-sm font-bold max-md:text-xs">{{ $count }}</span>
                                <span class="text-sm font-normal max-md:text-xs">موجودی</span>
                            </span>
                            <span
                                class="flex items-center gap-2 rounded-md bg-gray-100 px-3 text-sm text-gray-800 dark:bg-gray-800 dark:text-gray-100 max-md:hidden max-md:text-xs"
                            >
                                <span class="text-sm font-bold max-md:text-xs">{{ $item->pd_out_stock }}</span>
                                <span class="text-sm font-normal max-md:text-xs">فروخته شده</span>
                            </span>
                        @endif
                    @endif

                    @if ($item?->fixed_amount != null)
                        @if (isset($item?->eticket))
                            <span
                                class="rounded-md bg-gray-100 px-3 text-sm text-gray-800 dark:bg-gray-800 dark:text-gray-100 max-md:text-xs"
                            >
                                <span class="text-sm font-bold max-md:text-xs">اتیکت</span>
                                {{ $item->eticket ?? ' - ' }}
                            </span>
                            <span
                                class="flex items-center gap-2 rounded-md bg-gray-100 px-3 text-sm text-gray-800 dark:bg-gray-800 dark:text-gray-100 max-md:text-xs"
                            >
                                <span class="text-sm font-bold max-md:text-xs">{{ $count }}</span>
                                <span class="text-sm font-normal max-md:text-xs">موجودی</span>
                            </span>
                        @else
                            <span
                                class="flex items-center gap-2 rounded-md bg-gray-100 px-3 text-sm text-gray-800 dark:bg-gray-800 dark:text-gray-100 max-md:text-xs"
                            >
                                <span class="text-sm font-bold max-md:text-xs">{{ $count }}</span>
                                <span class="text-sm font-normal max-md:text-xs">موجودی</span>
                            </span>
                            <span
                                class="flex items-center gap-2 rounded-md bg-gray-100 px-3 text-sm text-gray-800 dark:bg-gray-800 dark:text-gray-100 max-md:hidden max-md:hidden max-md:text-xs"
                            >
                                <span class="text-sm font-bold max-md:text-xs">{{ $item->pd_out_stock }}</span>
                                <span class="text-sm font-normal max-md:text-xs">فروخته شده</span>
                            </span>
                        @endif
                    @endif
                    @if (isset($item?->eticket))
                        <span
                            class="flex items-center gap-2 rounded-md bg-gray-100 px-3 text-sm text-gray-800 dark:bg-gray-800 dark:text-gray-100 max-md:text-xs"
                        >
                            <span class="text-sm font-bold max-md:text-xs">{{ getItemValue($item?->branch_id) }}</span>

                        </span>
                    @endif
                    <a
                        class="rounded-full bg-white p-1 text-gray-500 transition-all hover:bg-yellow-300 max-md:hidden"
                        href="{{ route('admin-dashboard-product-details-show', ['productId' => hashId($item->product_id)]) }}"
                        target="_blank"
                    >
                        <svg
                            class="size-4"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                            />
                        </svg>
                    </a>
                </div>

                <div class="relative h-72 max-h-72 w-full overflow-hidden max-md:h-44">

                    <img
                        class="h-full w-full object-cover"
                        src="{{ $imageUrl }}"
                        alt="{{ $item->product_title_fa }}"
                        target="_blank"
                        loading="lazy"
                    >
                    {{-- @if ($count <= 0)
                        <div
                            class="absolute left-0 top-0 flex h-full w-full items-center justify-center bg-gray-900/50 backdrop-blur-sm">
                            <span class="-rotate-12 text-base text-white md:text-xl">عدم موجودی محصول</span>
                        </div>
                    @endif --}}
                </div>
            </div>

            <div class="border-t border-gray-200 p-2">
                <p
                    class="mt-2 flex w-full cursor-pointer items-start text-right text-lg font-bold dark:text-gray-100 max-md:overflow-hidden max-md:truncate max-md:whitespace-nowrap max-md:text-sm md:h-16"
                    type="button"
                    title="{{ $item->product_title_fa }}"
                    @click="$store.showProductDetails = true; $store.lock = true;$wire.dispatch('show-product-details', { productId : {{ $item->product_detail_id }} })"
                >{{ $item->product_title_fa }}</p>

                <div class="w-full flex-row-reverse items-center">
                    {{-- @if (isset($item?->eticket)) --}}
                    @if (!$group)
                        <div class="flex w-full flex-col gap-2">
                            <div class="flex items-center">
                                <p class="mt-1 text-right text-sm text-gray-600 dark:text-gray-100 max-md:mb-2">دسته
                                    بندی:
                                    <span class="font-bold">{{ $item->category_title_fa }}</span>
                                </p>
                            </div>
                            @if (
                                (optional($item)->fixed_amount == 0 || optional($item)->fixed_amount == null) &&
                                    (optional($item)->construction_wages == null || optional($item)->construction_wages == 0))
                                <span
                                    class="dark:font-noraml mr-auto block text-lg font-semibold text-gray-800 dark:font-normal dark:text-gray-100 max-md:text-sm"
                                >
                                    <span class="dark:gray-300 pr-1 text-sm font-normal text-gray-500">ریال</span>
                                    {{ $item?->fixed_amount != null ? formatMoney($item?->fixed_amount) : formatMoney($item->amount) }}
                                </span>
                            @else
                                <span
                                    class="dark:font-noraml mr-auto block text-lg font-semibold text-gray-800 dark:font-normal dark:text-gray-100 max-md:text-sm"
                                >
                                    <span class="dark:gray-300 pr-1 text-sm font-normal text-gray-500">ریال</span>
                                    {{ $item?->fixed_amount != null ? formatMoney($item?->fixed_amount) : formatMoney($item->amount) }}
                                </span>
                            @endif
                        </div>
                    @else
                        <div class="w-full">
                            <div class="block">
                                <p
                                    class="dark:font-noraml text-sm text-gray-400 dark:font-normal dark:text-gray-100 max-md:text-sm">
                                    بازده قیمتی (ریال)</p>
                            </div>
                            <div class="flex w-full items-center gap-0 py-2">
                                <span
                                    class="dark:font-noraml w-full bg-red-100 px-3 py-1 text-center text-base font-semibold text-gray-800 dark:font-normal dark:text-gray-100 max-md:px-1 max-md:text-xs"
                                >

                                    {{ $item?->fixed_amount != null ? formatMoney($item?->fixed_amount) : formatMoney($item->max_amount) }}
                                </span>
                                <span
                                    class="dark:font-noraml w-full bg-green-100 px-3 py-1 text-center text-base font-semibold text-gray-800 dark:font-normal dark:text-gray-100 max-md:px-1 max-md:text-xs"
                                >

                                    {{ $item?->fixed_amount != null ? formatMoney($item?->fixed_amount) : formatMoney($item->min_amount) }}
                                </span>

                            </div>
                        </div>
                    @endif

                </div>
                @if (isset($count_out_stock) && $count_out_stock != null && $count == 0)
                    <span class="flex cursor-not-allowed rounded-md px-2 py-1.5 text-base text-red-600 max-md:px-1">
                        <span class="flex items-center gap-2">
                            <span class="text-sm md:text-base">عدم موجودی</span>
                        </span>
                    </span>
                @else
                    @if ($count > 0)
                        {{-- @if ((optional($item)->fixed_amount == 0 || optional($item)->fixed_amount == null) && (optional($item)->construction_wages == null || optional($item)->construction_wages == 0))
                            <button
                                class="w-full gap-2 bg-gray-100 py-1 text-center text-sm font-bold max-md:flex-col max-md:px-1 md:px-3 md:py-2"
                                type="button"
                                @click="copyPaymentInfo"
                                x-data="{
                                    copyPaymentInfo() {
                                        const cover = 'https://tdservice.ir/p/{{ hashId($item->product_detail_id) }}';
                                        const product = '{{ $item->product_title_fa ?? '' }}';
                                        const eticket = '{{ $item?->eticket ?? '' }}';
                                        const price = '{{ $item->amount ?? '-' }}';
                                        const weight = '{{ $item?->weight ?? '' }}';
                                        const construction_wages = '{{ $item?->construction_wages ?? '' }} درصد';
                                        const tax = '{{ $item?->tax ?? 0 }} درصد';
                                        const profit = '{{ $item?->profit ?? '' }} درصد';
                                        const branch = '{{ $item?->branch ?? '' }}';
                                        const card = '{{ $setting['card_number'] ?? '' }}';
                                        const name = '{{ $setting['fullname_card_number'] ?? '' }}';
                                        const sheba = '{{ $setting['sheba_card_number'] ?? '' }}';

                                        const ltr = '\u200E'; // Left-To-Right mark

                                        const text =
                                            `${product}\n` +
                                            `اتیکت محصول: ${ltr}${eticket}\n` +
                                            `وزن: ${ltr}${weight} گرم \n` +
                                            `اجرت: ${ltr}${construction_wages}\n` +
                                            `مالیات: ${ltr}${tax}\n` +
                                            `سود: ${ltr}${profit}\n` +
                                            `شعبه فروش: ${ltr}${branch}\n` +
                                            `قیمت محصول: ${ltr}${price} ریال \n\n` +
                                            `لینک محصول: ${ltr}${cover}\n\n` +
                                            `شماره کارت: ${ltr}${card}\n` +
                                            `بنام: ${name}\n` +
                                            `شماره شبا: ${ltr}${sheba}`;

                                        const textArea = document.createElement('textarea');
                                        textArea.value = text;
                                        document.body.appendChild(textArea);
                                        textArea.select();

                                        try {
                                            document.execCommand('copy');
                                            alert('اطلاعات با موفقیت کپی شد!');
                                        } catch (err) {
                                            alert('خطا در کپی اطلاعات');
                                        }

                                        document.body.removeChild(textArea);
                                    }
                                }"
                            >
                                <div class="flex items-center justify-between">
                                    <span class="max-md:text-[0.65rem]">{{ $setting['card_number'] ?? null }}</span>
                                    <svg
                                        class="size-6 text-gray-500"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"
                                        />
                                    </svg>
                                </div>
                            </button>
                        @else --}}
                        {{-- @if (isset($item?->eticket)) --}}
                        @if (!$group)
                            <div
                                class="flex items-center justify-between gap-3 max-md:mt-2 max-md:border-t max-md:border-gray-200 md:pt-2"
                                x-data
                            >
                                {{-- {{ 'pID:' . $item->product_id . ' PdID:' . $item->product_detail_id }} --}}
                                <div class="max-md:pt-2">
                                    <livewire:dashboard.admin.shop.add-cart
                                        wire:key="{{ now()->timestamp . '-' . $item->product_id . '_' . $item->product_detail_id }}"
                                        :productId="$item->product_id"
                                        :weightId="$item->product_detail_id"
                                        :maxCount="$count"
                                    />
                                </div>
                                @php
                                    $APP_TIDAMODE_SHOP_URL = env('APP_TIDAMODE_SHOP_URL');
                                @endphp
                                <input
                                    class="w-full rounded-md border-2 border-gray-100 p-2 pl-8 text-sm text-gray-500 outline-none ring-0 focus:bottom-0 focus:ring-0 max-md:hidden"
                                    type="hidden"
                                    value="{{ $APP_TIDAMODE_SHOP_URL }}p/{{ $item->product_id }}/{{ $item->product_detail_id }}/{{ hashId(auth()->user()->id) }}"
                                    readonly
                                    x-ref="copyInput"
                                >
                                <button
                                    class="text-gray-500 max-md:pt-2 md:hidden"
                                    type="button"
                                    @click="() => {
                                    let textArea = document.createElement('textarea');
                                    textArea.value = $refs.copyInput.value;
                                    document.body.appendChild(textArea);
                                    textArea.select();
                                    try {
                                        document.execCommand('copy');
                                        alert('آدرس با موفقیت کپی شد!');
                                    } catch (err) {
                                        alert('خطا در کپی کردن آدرس');
                                    }
                                    document.body.removeChild(textArea);
                                }"
                                >
                                    <svg
                                        class="size-6"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"
                                        />
                                    </svg>

                                </button>
                                <div
                                    class="relative w-full rounded-md border border-gray-100 max-md:hidden"
                                    x-data
                                >
                                    @php
                                        $APP_TIDAMODE_SHOP_URL = env('APP_TIDAMODE_SHOP_URL');
                                    @endphp
                                    <input
                                        class="w-full rounded-md border-2 border-gray-100 p-2 pl-8 text-sm text-gray-500 outline-none ring-0 focus:bottom-0 focus:ring-0 max-md:hidden"
                                        type="text"
                                        value="{{ $APP_TIDAMODE_SHOP_URL }}p/{{ $item->product_id }}/{{ $item->product_detail_id }}/{{ hashId(auth()->user()->id) }}"
                                        readonly
                                        x-ref="copyInput"
                                    >
                                    <button
                                        class="absolute left-1 top-2.5 text-gray-500"
                                        type="button"
                                        @click="() => {
                                        let textArea = document.createElement('textarea');
                                        textArea.value = $refs.copyInput.value;
                                        document.body.appendChild(textArea);
                                        textArea.select();
                                        try {
                                            document.execCommand('copy');
                                            alert('آدرس با موفقیت کپی شد!');
                                        } catch (err) {
                                            alert('خطا در کپی کردن آدرس');
                                        }
                                        document.body.removeChild(textArea);
                                    }"
                                    >
                                        <svg
                                            class="size-6"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke-width="1.5"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"
                                            />
                                        </svg>

                                    </button>
                                </div>
                            </div>
                        @else
                            <button
                                class="mt-1 rounded-lg bg-blue-600 px-6 py-1.5 text-white transition-all hover:bg-blue-700 max-md:py-1"
                                type="button"
                                wire:key="{{ $item->product_id . '-btn-' . $item->product_id }}"
                                @click="$store.showListProduct = true; lock = true; $wire.dispatch('show-products', { productId : {{ $item->product_id }} })"
                            >
                                <span class="text-sm max-md:text-xs">انتخاب از لیست</span>

                            </button>
                        @endif
                        {{-- @endif --}}
                    @else
                        <div class="mt-2 flex items-center gap-2">
                            <span
                                class="flex cursor-not-allowed rounded-md px-2 py-1.5 text-base text-red-600 max-md:px-1"
                            >
                                <span class="flex items-center gap-2">
                                    <span>
                                        <svg
                                            class="size-6 max-md:size-4"
                                            xmlns="http://www.w3.org/2000/svg"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke-width="1.5"
                                            stroke="currentColor"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                d="M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"
                                            />
                                        </svg>
                                    </span>
                                    <span class="text-sm max-md:text-xs">محصول رزرو شده</span>
                                </span>
                            </span>

                            @if (auth()->user()->level == 'admin')
                                <livewire:dashboard.admin.shop.cancel-reserve-product
                                    :productId="$item->product_id"
                                    :productDetailId="$item->product_detail_id"
                                    wire:key="camcel-{{ now()->timestamp . '-' . $item->product_id . '_' . $item->product_detail_id }}"
                                />
                            @endif
                        </div>
                    @endif
                @endif
            </div>
        </div>
    </div>
@endforeach
