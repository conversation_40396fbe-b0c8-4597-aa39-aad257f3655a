@persist('navigate')
    @php
        $settings = \App\Models\Setting::whereIn('type', ['alert', 'alert_message'])
            ->pluck('body', 'type')
            ->toArray();
    @endphp
    @if (isset($settings['alert']) && $settings['alert'] == true)
        <div class="bg-red-600 p-3 text-center text-white">
            <p class="text-base max-md:text-sm">{{ $settings['alert_message'] }}</p>
        </div>
    @endif
    <nav
        class="z-[999] hidden w-full bg-gray-800 py-3 dark:bg-gray-900 lg:block"
        :class="{ '': scrolled, 'sticky top-0 ': !scrolled }"
    >
        <div
            class="mx-auto lg:px-8"
            x-data="{ dropDownProfile: false }"
        >
            <div class="flex items-center justify-between">
                <a
                    class="flex items-center gap-2 overflow-hidden"
                    href="/"
                >
                    <img
                        class="w-20"
                        src="/assets/images/tidamodewhitelogo-300x108.png"
                        alt=""
                    >
                    <div>
                        <h1 class="text-lg font-bold text-white">فروشگاه طلا و جواهرات تیدامد</h1>
                        <p class="text-xs font-normal text-gray-400">سامانه مدیریت مشتریان و سفارشات</p>
                    </div>
                </a>
                <div class="relative flex items-center gap-2">
                    <div class="">
                        <button
                            class="ml-6 flex items-center gap-2 rounded-xl px-6 py-1.5 transition-all hover:bg-gray-700"
                            type="button"
                            @click="dropDownProfile = !dropDownProfile"
                        >
                            <div class="h-10 w-10 overflow-hidden rounded-full border-2 border-red-500 p-0.5">
                                <img
                                    class="w-10 shrink-0 rounded-full bg-white"
                                    src="{{ auth()->user()->avatar != null ? 'https://tidamode.iran.liara.run/' . auth()->user()->avatar : '/assets/images/avatar.jpg' }}"
                                    alt="{{ auth()->user()->fullname }}"
                                >
                            </div>
                            <div class="text-right">
                                <span class="block text-sm text-white">{{ auth()->user()->fullname }}</span>
                                <span class="block pt-1 text-xs text-gray-400">{{ auth()->user()->mobile }}</span>
                            </div>
                        </button>
                        <div
                            class="absolute right-0 top-12 z-[999] w-60 rounded-lg bg-white text-gray-700 shadow-lg dark:bg-gray-900 dark:text-gray-200"
                            x-cloak
                            x-transition:enter="transition ease-out duration-100"
                            x-transition:enter-start="opacity-0 scale-90"
                            x-transition:enter-end="opacity-100 scale-100"
                            x-transition:leave="transition ease-in duration-100"
                            x-transition:leave-start="opacity-100 scale-100"
                            x-transition:leave-end="opacity-0 scale-90"
                            x-show="dropDownProfile"
                            @click.away="dropDownProfile = false"
                        >
                            <div class="flex items-center p-2">
                                <ul class="w-full text-gray-700">
                                    <li
                                        class="mt-1 rounded-lg p-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-700 dark:text-gray-100 dark:hover:bg-gray-600">
                                        <a
                                            class="flex items-center"
                                            href="#"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z"
                                                />
                                            </svg>
                                            <span class="mr-2 text-sm">پروفایل من</span>
                                        </a>
                                    </li>
                                    <li
                                        class="mt-1 rounded-lg p-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-700 dark:text-gray-100 dark:hover:bg-gray-600">
                                        <a
                                            class="flex items-center"
                                            href="#"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M21 12a2.25 2.25 0 0 0-2.25-2.25H15a3 3 0 1 1-6 0H5.25A2.25 2.25 0 0 0 3 12m18 0v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6m18 0V9M3 12V9m18 0a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 9m18 0V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v3"
                                                />
                                            </svg>
                                            <span class="mr-2 text-sm">کیف پول من</span>
                                        </a>
                                    </li>
                                    <li
                                        class="mt-1 rounded-lg p-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-700 dark:text-gray-100 dark:hover:bg-gray-600">
                                        <a
                                            class="flex items-center justify-between"
                                            href="#"
                                        >
                                            <div class="flex items-center">
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">بوکمارک من</span>
                                            </div>
                                            <span
                                                class="flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-gray-200 p-1 text-xs text-gray-700"
                                            >3</span>
                                        </a>
                                    </li>
                                    <li
                                        class="mt-1 rounded-lg p-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-700 dark:text-gray-100 dark:hover:bg-gray-600">
                                        <a
                                            class="flex items-center justify-between"
                                            href="#"
                                        >
                                            <div class="flex items-center">
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">تیکت ها</span>
                                            </div>
                                            <span class="h-2 w-2 rounded-full bg-red-500 p-1"></span>
                                        </a>
                                    </li>
                                    <li
                                        class="mt-1 rounded-lg p-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-700 dark:text-gray-100 dark:hover:bg-gray-600">
                                        <a
                                            class="flex items-center justify-between"
                                            href="#"
                                        >
                                            <div class="flex items-center">
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">پیام های من</span>
                                            </div>
                                            <span class="h-2 w-2 rounded-full bg-red-500 p-1"></span>
                                        </a>
                                    </li>
                                    <li
                                        class="mt-1 rounded-lg p-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-700 dark:text-gray-100 dark:hover:bg-gray-600">
                                        <a
                                            class="flex items-center"
                                            href="{{ route('logout') }}"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15m3 0l3-3m0 0l-3-3m3 3H9"
                                                />
                                            </svg>
                                            <span class="mr-2 text-sm">خروج از سامانه</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <button
                        class="relative p-1.5"
                        data-tooltip-target="tooltip-notification"
                        type="button"
                    >
                        <span class="absolute -top-1 right-0 flex h-3 w-3">
                            <span
                                class="absolute inline-flex h-full w-full animate-ping rounded-full bg-red-500 opacity-75"
                            ></span>
                            <span class="relative inline-flex h-3 w-3 rounded-full bg-red-500"></span>
                        </span>
                        <svg
                            class="h-6 w-6 shrink-0 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"
                            />
                        </svg>
                        <div
                            class="tooltip invisible absolute z-10 flex items-center justify-center rounded-lg bg-white px-3 py-2 text-sm font-medium text-gray-700 opacity-0 shadow-sm transition-opacity duration-300 dark:bg-gray-700 dark:text-gray-200"
                            id="tooltip-notification"
                            role="tooltip"
                        >
                            نوتیفکیشن
                            <div
                                class="tooltip-arrow"
                                data-popper-arrow
                            ></div>
                        </div>
                    </button>
                    <div class="relative flex items-center justify-center">
                        <button type="button">
                            <input
                                class="checkbox-theme hidden"
                                id="checkbox-theme"
                                type="checkbox"
                                @click="theme = !theme , localStorage.setItem('theme', theme)"
                            >
                            <label
                                class="relative flex h-[1.8rem] w-16 cursor-pointer items-center justify-between gap-2 overflow-hidden rounded-full bg-gray-700 p-1"
                                for="checkbox-theme"
                            >
                                <svg
                                    class="fa-moon h-6 w-6 shrink-0 text-white"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"
                                    />
                                </svg>
                                <svg
                                    class="fa-sun h-6 w-6 shrink-0 text-white"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"
                                    />
                                </svg>
                                <span
                                    class="ball transform transition-transform duration-200"
                                    :class="theme ? ' translate-x-8' : ''"
                                ></span>
                            </label>

                        </button>
                        <div
                            class="tooltip invisible absolute z-10 flex w-28 items-center justify-center rounded-lg bg-white px-3 py-2 text-sm font-medium text-gray-700 opacity-0 shadow-sm transition-opacity duration-300 dark:bg-gray-700 dark:text-gray-200"
                            id="tooltip-lock-Mode"
                            role="tooltip"
                        >
                            Dark / Light Mode
                            <div
                                class="tooltip-arrow"
                                data-popper-arrow
                            ></div>
                        </div>
                    </div>
                    <button
                        class="relative p-1.5"
                        data-tooltip-target="tooltip-search"
                        @click="search = true, $refs.search.focus()"
                    >
                        <svg
                            class="h-6 w-6 shrink-0 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
                            />
                        </svg>
                        <div
                            class="tooltip invisible absolute z-10 flex w-40 items-center justify-center rounded-lg bg-white px-3 py-2 text-sm font-medium text-gray-700 opacity-0 shadow-sm transition-opacity duration-300 dark:bg-gray-700 dark:text-gray-200"
                            id="tooltip-search"
                            role="tooltip"
                        >
                            جستجو محصول
                            <div
                                class="tooltip-arrow"
                                data-popper-arrow
                            ></div>
                        </div>
                    </button>

                    <button
                        class="relative p-1.5"
                        data-tooltip-target="tooltip-setting"
                        @click="settingDrawerLeft = true;lock = true"
                    >
                        <svg
                            class="h-6 w-6 shrink-0 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"
                            />
                        </svg>

                        <div
                            class="tooltip invisible absolute z-10 flex w-28 items-center justify-center rounded-lg bg-white px-3 py-2 text-sm font-medium text-gray-700 opacity-0 shadow-sm transition-opacity duration-300 dark:bg-gray-700 dark:text-gray-200"
                            id="tooltip-setting"
                            role="tooltip"
                        >
                            تنظیمات سامانه
                            <div
                                class="tooltip-arrow"
                                data-popper-arrow
                            ></div>
                        </div>
                    </button>

                </div>
            </div>

        </div>
    </nav>
    <nav
        class="sticky z-[998] hidden w-full bg-white py-2 shadow-md dark:bg-gray-800 md:hidden lg:hidden xl:block"
        :class="{ ' top-0 ': scrolled, '': !scrolled }"
    >
        <div class="mx-auto lg:px-8">
            <div class="flex items-center justify-between">
                <ul class="flex items-center gap-0">
                    <li
                        class="relative"
                        x-data="{ profileDropDown: false }"
                    >
                        <button
                            class="flex items-center gap-4 p-2"
                            type="button"
                            @click="profileDropDown = !profileDropDown"
                        >
                            <div class="flex items-center gap-2">
                                <svg
                                    class="h-[1.6rem] w-[1.6rem] shrink-0 text-gray-600 dark:text-gray-200"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"
                                    />
                                </svg>
                                <span
                                    class="whitespace-nowrap text-sm font-semibold text-gray-700 dark:font-normal dark:text-gray-200"
                                >سفارشات</span>
                            </div>
                            <svg
                                class="h-3 w-3 shrink-0 text-gray-500"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                                />
                            </svg>
                        </button>
                        <div
                            class="absolute right-0 top-8 z-[999] w-60 rounded-lg bg-gray-900 shadow-lg"
                            x-cloak
                            x-transition:enter="transition ease-out duration-100"
                            x-transition:enter-start="opacity-0 scale-90"
                            x-transition:enter-end="opacity-100 scale-100"
                            x-transition:leave="transition ease-in duration-100"
                            x-transition:leave-start="opacity-100 scale-100"
                            x-transition:leave-end="opacity-0 scale-90"
                            x-show="profileDropDown"
                            @click.away="profileDropDown = false"
                        >
                            <div class="flex items-center p-2">
                                <ul class="w-full text-gray-700">

                                    <li
                                        class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                        <button
                                            class="flex items-center"
                                            type="button"
                                            @click="createOrderModal = true"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M12 4.5v15m7.5-7.5h-15"
                                                />
                                            </svg>
                                            <span class="mr-2 text-sm">ایجاد سفارش جدید</span>
                                        </button>
                                    </li>

                                    @can('show-order-list')
                                        <li
                                            class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex items-center"
                                                href="{{ route('admin-dashboard-orders') }}"
                                            >
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">لیست سفارشات</span>
                                            </a>
                                        </li>
                                    @endcan
                                    <li
                                        class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                        <a
                                            class="flex items-center"
                                            href="{{ route('admin-dashboard-market') }}"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 0 1 0 3.75H5.625a1.875 1.875 0 0 1 0-3.75Z"
                                                />
                                            </svg>
                                            <span class="mr-2 text-sm">لیست بازاریاب</span>
                                        </a>
                                    </li>
                                    <li
                                        class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                        <a
                                            class="flex items-center"
                                            href="{{ route('admin-dashboard-factors') }}"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                                />
                                            </svg>
                                            <span class="mr-2 text-sm">لیست فاکتورها</span>
                                        </a>
                                    </li>
                                    <li
                                        class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                        <a
                                            class="flex items-center"
                                            href="{{ route('admin-dashboard-order-online') }}"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                                />
                                            </svg>
                                            <span class="mr-2 text-sm">لیست سفارشات آنلاین</span>
                                        </a>
                                    </li>
                                    @if (auth()->user()->level == 'admin')
                                        <li
                                            class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex items-center"
                                                href="{{ route('admin-dashboard-transactions') }}"
                                            >
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">لیست پرداختی ها</span>
                                            </a>
                                        </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </li>
                    <li
                        class="relative"
                        x-data="{ dropDownShop: false }"
                    >
                        <button
                            class="flex items-center gap-4 p-2"
                            type="button"
                            @click="dropDownShop = !dropDownShop"
                        >
                            <div class="flex items-center gap-2">
                                <svg
                                    class="h-6 w-6 shrink-0 text-gray-600 dark:text-gray-200"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"
                                    />
                                </svg>
                                <span
                                    class="whitespace-nowrap text-sm font-semibold text-gray-700 dark:font-normal dark:text-gray-200"
                                >فروشگاه</span>
                            </div>
                            <svg
                                class="h-3 w-3 shrink-0 text-gray-500"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                                />
                            </svg>
                        </button>
                        <div
                            class="absolute right-0 top-8 z-[999] w-64 rounded-lg bg-gray-900 shadow-lg"
                            x-cloak
                            x-transition:enter="transition ease-out duration-100"
                            x-transition:enter-start="opacity-0 scale-90"
                            x-transition:enter-end="opacity-100 scale-100"
                            x-transition:leave="transition ease-in duration-100"
                            x-transition:leave-start="opacity-100 scale-100"
                            x-transition:leave-end="opacity-0 scale-90"
                            x-show="dropDownShop"
                            @click.away="dropDownShop = false"
                        >
                            <div class="flex items-center p-2">
                                <ul class="w-full text-gray-700">
                                    <li
                                        class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                        <a
                                            class="flex items-center"
                                            href="{{ route('admin-dashboard-products') }}"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M12 4.5v15m7.5-7.5h-15"
                                                />
                                            </svg>
                                            <span class="mr-2 text-sm">ایجاد / ویرایش محصول</span>
                                        </a>
                                    </li>

                                    <li
                                        class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                        <a
                                            class="flex items-center"
                                            href="{{ route('admin-dashboard-shop') }}"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                                />
                                            </svg>
                                            <span class="mr-2 text-sm">لیست محصولات فروشگاه</span>
                                        </a>
                                    </li>
                                    <li
                                        class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                        <a
                                            class="flex items-center"
                                            href="{{ route('admin-dashboard-shop-transactions') }}"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 0 1 0 3.75H5.625a1.875 1.875 0 0 1 0-3.75Z"
                                                />
                                            </svg>
                                            <span class="mr-2 text-sm">لیست پرداختی و لینک ها</span>
                                        </a>
                                    </li>
                                    <li
                                        class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                        <a
                                            class="flex items-center"
                                            href="{{ route('admin-dashboard-shop-order-deposits') }}"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 0 1 0 3.75H5.625a1.875 1.875 0 0 1 0-3.75Z"
                                                />
                                            </svg>
                                            <span class="mr-2 text-sm">لیست فیش های واریزی سفارشات</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </li>
                    <li
                        class="relative"
                        x-data="{ dropDownShop: false }"
                    >
                        <button
                            class="flex items-center gap-4 p-2"
                            type="button"
                            @click="dropDownShop = !dropDownShop"
                        >
                            <div class="flex items-center gap-2">

                                <svg
                                    class="h-6 w-6 shrink-0 text-gray-600 dark:text-gray-200"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                    />
                                </svg>

                                <span
                                    class="whitespace-nowrap text-sm font-semibold text-gray-700 dark:font-normal dark:text-gray-200"
                                >محصولات بدون اجرت</span>
                            </div>
                            <svg
                                class="h-3 w-3 shrink-0 text-gray-500"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                                />
                            </svg>
                        </button>
                        <div
                            class="absolute right-0 top-8 z-[999] w-64 rounded-lg bg-gray-900 shadow-lg"
                            x-cloak
                            x-transition:enter="transition ease-out duration-100"
                            x-transition:enter-start="opacity-0 scale-90"
                            x-transition:enter-end="opacity-100 scale-100"
                            x-transition:leave="transition ease-in duration-100"
                            x-transition:leave-start="opacity-100 scale-100"
                            x-transition:leave-end="opacity-0 scale-90"
                            x-show="dropDownShop"
                            @click.away="dropDownShop = false"
                        >
                            <div class="flex items-center p-2">
                                <ul class="w-full text-gray-700">
                                    <li
                                        class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                        <a
                                            class="flex items-center"
                                            href="{{ route('admin-dashboard-product-without-fee-create') }}"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M12 4.5v15m7.5-7.5h-15"
                                                />
                                            </svg>
                                            <span class="mr-2 text-sm">ایجاد محصول جدید</span>
                                        </a>
                                    </li>
                                    <li
                                        class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                        <a
                                            class="flex items-center"
                                            href="{{ route('admin-dashboard-products-without-fee') }}"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                                />
                                            </svg>

                                            <span class="mr-2 text-sm">لیست محصولات بدون اجرت</span>
                                        </a>
                                    </li>

                                </ul>
                            </div>
                        </div>
                    </li>
                    @if (auth()->user()->level == 'admin')
                        <li
                            class="relative"
                            x-data="{ dropDownDeposit: false }"
                        >
                            <button
                                class="flex items-center gap-4 p-2"
                                type="button"
                                @click="dropDownDeposit = !dropDownDeposit"
                            >
                                <div class="flex items-center gap-2">
                                    <svg
                                        class="h-6 w-6 shrink-0 text-gray-600 dark:text-gray-200"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M3 7.5 7.5 3m0 0L12 7.5M7.5 3v13.5m13.5 0L16.5 21m0 0L12 16.5m4.5 4.5V7.5"
                                        />
                                    </svg>
                                    <span
                                        class="whitespace-nowrap text-sm font-semibold text-gray-700 dark:font-normal dark:text-gray-200"
                                    >واریزی
                                        مشتریان</span>
                                </div>
                                <svg
                                    class="h-3 w-3 shrink-0 text-gray-500"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="m19.5 8.25-7.5 7.5-7.5-7.5"
                                    />
                                </svg>
                            </button>
                            <div
                                class="absolute right-0 top-8 z-[999] w-60 rounded-lg bg-gray-900 shadow-lg"
                                x-cloak
                                x-transition:enter="transition ease-out duration-100"
                                x-transition:enter-start="opacity-0 scale-90"
                                x-transition:enter-end="opacity-100 scale-100"
                                x-transition:leave="transition ease-in duration-100"
                                x-transition:leave-start="opacity-100 scale-100"
                                x-transition:leave-end="opacity-0 scale-90"
                                x-show="dropDownDe.posit"
                                @click.away="dropDownDeposit = false"
                            >
                                <div class="flex items-center p-2">
                                    <ul class="w-full text-gray-700">
                                        <li
                                            class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex items-center"
                                                href="#"
                                            >
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M12 4.5v15m7.5-7.5h-15"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">ثبت واریزی جدید</span>
                                            </a>
                                        </li>
                                        <li
                                            class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex items-center"
                                                href="#"
                                            >
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">لیست واریزی ها</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </li>

                        <li
                            class="relative"
                            x-data="{ dropDownUsers: false }"
                        >
                            <button
                                class="flex items-center gap-4 p-2"
                                type="button"
                                @click="dropDownUsers = !dropDownUsers"
                            >
                                <div class="flex items-center gap-2">
                                    <svg
                                        class="h-6 w-6 shrink-0 text-gray-600 dark:text-gray-200"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                                        />
                                    </svg>
                                    <span
                                        class="whitespace-nowrap text-sm font-semibold text-gray-700 dark:font-normal dark:text-gray-200"
                                    >مشترکین
                                        و کاربران</span>
                                </div>
                                <svg
                                    class="h-3 w-3 shrink-0 text-gray-500"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="m19.5 8.25-7.5 7.5-7.5-7.5"
                                    />
                                </svg>
                            </button>

                            <div
                                class="absolute right-0 top-8 z-[999] w-60 rounded-lg bg-gray-900 shadow-lg"
                                x-cloak
                                x-transition:enter="transition ease-out duration-100"
                                x-transition:enter-start="opacity-0 scale-90"
                                x-transition:enter-end="opacity-100 scale-100"
                                x-transition:leave="transition ease-in duration-100"
                                x-transition:leave-start="opacity-100 scale-100"
                                x-transition:leave-end="opacity-0 scale-90"
                                x-show="dropDownUsers"
                                @click.away="dropDownUsers = false"
                            >
                                <div class="flex items-center p-2">
                                    <ul class="w-full text-gray-700">
                                        <li
                                            class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex items-center"
                                                href="{{ route('admin-dashboard-users') }}"
                                            >
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">لیست کاربران</span>
                                            </a>
                                        </li>
                                        <li
                                            class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex items-center"
                                                href="{{ route('admin-dashboard-role-and-permission') }}"
                                            >
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">لیست نقش و دسترسی ها</span>
                                            </a>
                                        </li>
                                        <li
                                            class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex items-center"
                                                href="{{ route('admin-dashboard-subscribers') }}"
                                            >
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">لیست مشترکین</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </li>

                        <li
                            class="relative"
                            x-data="{ dropDownReports: false }"
                        >
                            <button
                                class="flex items-center gap-4 p-2"
                                type="button"
                                @click="dropDownReports = !dropDownReports"
                            >
                                <div class="flex items-center gap-2">
                                    <svg
                                        class="h-[1.6rem] w-[1.6rem] shrink-0 text-gray-600 dark:text-gray-200"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"
                                        />
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"
                                        />
                                    </svg>
                                    <span
                                        class="whitespace-nowrap text-sm font-semibold text-gray-700 dark:font-normal dark:text-gray-200"
                                    >گزارشات
                                        مالی و نظرسنجی</span>
                                </div>
                                <svg
                                    class="h-3 w-3 shrink-0 text-gray-500 dark:text-gray-200"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="m19.5 8.25-7.5 7.5-7.5-7.5"
                                    />
                                </svg>
                            </button>
                            <div
                                class="absolute right-0 top-8 z-[999] w-60 rounded-lg bg-gray-900 shadow-lg"
                                x-cloak
                                x-transition:enter="transition ease-out duration-100"
                                x-transition:enter-start="opacity-0 scale-90"
                                x-transition:enter-end="opacity-100 scale-100"
                                x-transition:leave="transition ease-in duration-100"
                                x-transition:leave-start="opacity-100 scale-100"
                                x-transition:leave-end="opacity-0 scale-90"
                                x-show="dropDownReports"
                                @click.away="dropDownReports = false"
                            >
                                <div class="flex items-center p-2">
                                    <ul class="w-full text-gray-700">
                                        <li
                                            class="mt-1 cursor-not-allowed rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex cursor-not-allowed items-center"
                                                href="#"
                                            >
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z"
                                                    />
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">خلاصه گزارشات مشتریان</span>
                                            </a>
                                        </li>
                                        <li
                                            class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex items-center"
                                                href="{{ route('admin-dashboard-report-commission') }}"
                                            >
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">خلاصه گزارش پورسانت کاربران</span>
                                            </a>
                                        </li>
                                        <li
                                            class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex items-center"
                                                href="{{ route('admin-dashboard-report-financial-funds') }}"
                                            >

                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"
                                                    />
                                                </svg>

                                                <span class="mr-2 text-sm">صندوق های مالی</span>
                                            </a>
                                        </li>
                                        <li
                                            class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex items-center"
                                                href="{{ route('admin-dashboard-report-sms') }}"
                                            >
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">گزارشات پیامک ها</span>
                                            </a>
                                        </li>
                                        <li
                                            class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex items-center"
                                                href="{{ route('admin-dashboard-report-survey') }}"
                                            >
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"
                                                    />
                                                </svg>

                                                <span class="mr-2 text-sm">نظرسنجی مشتریان</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                    @endif
                    <li
                        class="relative"
                        x-data="{ dropDownTicket: false }"
                    >
                        <button
                            class="flex items-center gap-4 p-2"
                            type="button"
                            @click="dropDownTicket = !dropDownTicket"
                        >
                            <div class="flex items-center gap-2">
                                <svg
                                    class="h-6 w-6 shrink-0 text-gray-600 dark:text-gray-200"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z"
                                    />
                                </svg>
                                <span
                                    class="text-sm font-semibold text-gray-700 dark:font-normal dark:text-gray-200">پشتیبانی</span>
                            </div>
                            <svg
                                class="h-3 w-3 shrink-0 text-gray-500"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="1.5"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="m19.5 8.25-7.5 7.5-7.5-7.5"
                                />
                            </svg>
                        </button>
                        <div
                            class="absolute right-0 top-8 z-[999] w-60 rounded-lg bg-gray-900 shadow-lg"
                            x-cloak
                            x-transition:enter="transition ease-out duration-100"
                            x-transition:enter-start="opacity-0 scale-90"
                            x-transition:enter-end="opacity-100 scale-100"
                            x-transition:leave="transition ease-in duration-100"
                            x-transition:leave-start="opacity-100 scale-100"
                            x-transition:leave-end="opacity-0 scale-90"
                            x-show="dropDownTicket"
                            @click.away="dropDownTicket = false"
                        >
                            <div class="flex items-center p-2">
                                <ul class="w-full text-gray-700">
                                    <li
                                        class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                        <a
                                            class="flex items-center"
                                            href="#"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M12 4.5v15m7.5-7.5h-15"
                                                />
                                            </svg>
                                            <span class="mr-2 text-sm">ایجاد تیکت جدید</span>
                                        </a>
                                    </li>
                                    <li
                                        class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                        <a
                                            class="flex items-center"
                                            href="#"
                                        >
                                            <svg
                                                class="h-6 w-6 shrink-0"
                                                xmlns="http://www.w3.org/2000/svg"
                                                fill="none"
                                                viewBox="0 0 24 24"
                                                stroke-width="1.5"
                                                stroke="currentColor"
                                            >
                                                <path
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"
                                                />
                                            </svg>
                                            <span class="mr-2 text-sm">لیست تیکت ها</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </li>
                    {{-- @if (auth()->user()->level == 'admin')
                        <li
                            class="relative"
                            x-data="{ dropDownSettings: false }"
                        >
                            <button
                                class="flex items-center gap-4 p-2"
                                type="button"
                                @click="dropDownSettings = !dropDownSettings"
                            >
                                <div class="flex items-center gap-2">
                                    <svg
                                        class="h-6 w-6 shrink-0 text-gray-600 dark:text-gray-200"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"
                                        />
                                    </svg>
                                    <span
                                        class="text-sm font-semibold text-gray-700 dark:font-normal dark:text-gray-200">تنظیمات</span>
                                </div>
                                <svg
                                    class="h-3 w-3 shrink-0 text-gray-500"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="m19.5 8.25-7.5 7.5-7.5-7.5"
                                    />
                                </svg>
                            </button>
                            <div
                                class="absolute right-0 top-8 z-[999] w-60 rounded-lg bg-gray-900 shadow-lg"
                                x-cloak
                                x-transition:enter="transition ease-out duration-100"
                                x-transition:enter-start="opacity-0 scale-90"
                                x-transition:enter-end="opacity-100 scale-100"
                                x-transition:leave="transition ease-in duration-100"
                                x-transition:leave-start="opacity-100 scale-100"
                                x-transition:leave-end="opacity-0 scale-90"
                                x-show="dropDownSettings"
                                @click.away="dropDownSettings = false"
                            >
                                <div class="flex items-center p-2">
                                    <ul class="w-full text-gray-700">
                                        <li
                                            class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex items-center"
                                                href="{{ route('admin.setting.index') }}"
                                            >
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M4.098 19.902a3.75 3.75 0 0 0 5.304 0l6.401-6.402M6.75 21A3.75 3.75 0 0 1 3 17.25V4.125C3 3.504 3.504 3 4.125 3h5.25c.621 0 1.125.504 1.125 1.125v4.072M6.75 21a3.75 3.75 0 0 0 3.75-3.75V8.197M6.75 21h13.125c.621 0 1.125-.504 1.125-1.125v-5.25c0-.621-.504-1.125-1.125-1.125h-4.072M10.5 8.197l2.88-2.88c.438-.439 1.15-.439 1.59 0l3.712 3.713c.44.44.44 1.152 0 1.59l-2.879 2.88M6.75 17.25h.008v.008H6.75v-.008Z"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">تنظیمات اولیه</span>
                                            </a>
                                        </li>
                                        <li
                                            class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex items-center"
                                                href="{{ route('admin.setting.document-api') }}"
                                            >
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"
                                                    />
                                                </svg>

                                                <span class="mr-2 text-sm">داکیومنت پروژه فروشگاه</span>
                                            </a>
                                        </li>
                                        <li
                                            class="mt-1 rounded-lg p-2 text-white transition-all hover:bg-gray-600 hover:text-gray-100">
                                            <a
                                                class="flex items-center"
                                                href="#"
                                            >
                                                <svg
                                                    class="h-6 w-6 shrink-0"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                    stroke-width="1.5"
                                                    stroke="currentColor"
                                                >
                                                    <path
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                        d="M17.25 6.75 22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3-4.5 16.5"
                                                    />
                                                </svg>
                                                <span class="mr-2 text-sm">بات تلگرام فروشگاه</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                        </li>
                    @endif --}}
                </ul>
                <div class="flex items-center gap-2 max-2xl:hidden">
                    <div
                        class="relative rounded-xl bg-gray-100 px-6 py-1.5 text-center"
                        :class="{ 'flex items-center flex-row-reverse gap-1': scrolled, 'max-h-14 md:h-14': !scrolled }"
                    >
                        <div
                            x-data="{
                                countdown: '',
                                scrolled: false,
                                currentTime: '{{ now()->format('H:i:s') }}',
                                remainingTime: 0,
                                timer: null,
                            
                                init() {
                                    this.setRemainingTime();
                                    this.startCountdown();
                                },
                            
                                setRemainingTime() {
                                    let [currentHour, currentMinute, currentSecond] = this.currentTime.split(':').map(Number);
                                    const currentTimeInSeconds = currentHour * 3600 + currentMinute * 60 + currentSecond;
                                    const nextHour = (currentHour + 1) % 24;
                                    const nextTimeInSeconds = nextHour * 3600;
                            
                                    this.remainingTime = nextTimeInSeconds - currentTimeInSeconds;
                                    if (this.remainingTime <= 0) {
                                        this.remainingTime = 3600; // اگر زمان صفر شد، یک ساعت دیگر اضافه شود
                                    }
                                },
                            
                                startCountdown() {
                                    if (this.timer) clearInterval(this.timer); // از اجرای چندباره جلوگیری شود
                            
                                    this.timer = setInterval(() => {
                                        if (this.remainingTime > 0) {
                                            this.remainingTime--;
                                        } else {
                                            this.countdown = 'قیمت ها بروز شد';
                                            setTimeout(() => {
                                                this.setRemainingTime();
                                            }, 1000);
                                        }
                            
                                        const hours = Math.floor(this.remainingTime / 3600);
                                        const minutes = Math.floor((this.remainingTime % 3600) / 60);
                                        const secondsLeft = this.remainingTime % 60;
                            
                                        this.countdown = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secondsLeft).padStart(2, '0')}`;
                                    }, 1000);
                                }
                            }"
                            x-init="init()"
                        >
                            <span
                                class="text-xs text-gray-700"
                                :class="{ 'hidden': scrolled, 'block': !scrolled }"
                            >
                                تا لحظه بروزرسانی
                            </span>
                            <livewire:dashboard.admin.product.products-update-button />
                            <span
                                class="pt-2 text-sm text-red-500"
                                x-text="countdown"
                            ></span>
                        </div>

                    </div>
                    <div
                        class="bgGold rounded-xl bg-gray-100 px-6 py-1.5 text-center"
                        :class="{ 'flex items-center flex-row-reverse gap-1': scrolled, 'max-h-14 md:h-14': !scrolled }"
                    >

                        <svg
                            class="h-6 w-6 shrink-0 text-gray-400"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            :class="{ 'block': scrolled, 'hidden': !scrolled }"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"
                            />
                        </svg>
                        <div>
                            <span class="flex items-center gap-1">
                                <span class="priceGold block text-sm font-bold"></span>
                                <span class="text-xs font-normal text-gray-500"></span>
                                <svg
                                    class="size-4 text-gray-500"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"
                                    />
                                </svg>

                            </span>
                            <span class="flex items-center gap-1">
                                <span
                                    class="block pt-1 text-sm font-bold">{{ formatMoney(\App\Models\ProductDetail::select('gold18k')->first()->gold18k) }}</span>
                                <span class="text-xs font-normal text-gray-500"></span>
                                <svg
                                    class="size-4 text-gray-500"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"
                                    />
                                </svg>

                            </span>
                        </div>
                    </div>
                    <div
                        class="rounded-xl bg-gray-100 px-6 py-1.5 text-center text-gray-700 dark:bg-gray-600 dark:text-gray-200 md:hidden"
                        :class="{ 'flex items-center flex-row-reverse gap-1': scrolled, 'max-h-14 md:h-14': !scrolled }"
                    >
                        <span
                            class="block text-xs dark:text-gray-400"
                            :class="{ 'hidden': scrolled, 'block': !scrolled }"
                        >موجودی کیف پول</span>
                        <svg
                            class="h-6 w-6 shrink-0 text-gray-400"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            :class="{ 'block': scrolled, 'hidden': !scrolled }"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M21 12a2.25 2.25 0 0 0-2.25-2.25H15a3 3 0 1 1-6 0H5.25A2.25 2.25 0 0 0 3 12m18 0v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6m18 0V9M3 12V9m18 0a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 9m18 0V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v3"
                            />
                        </svg>

                        <span class="block pt-1 text-sm font-bold dark:font-normal">0 <span
                                class="text-xs font-normal text-gray-500 dark:text-gray-400"
                            >ریال</span></span>
                    </div>

                    <div
                        class="rounded-xl bg-gray-100 px-6 py-1.5 text-center text-gray-700 dark:bg-gray-600 dark:text-gray-200"
                        :class="{ 'flex items-center flex-row-reverse gap-1': scrolled, 'max-h-14 md:h-14': !scrolled }"
                    >
                        <span
                            class="block text-xs dark:text-gray-400"
                            :class="{ 'hidden': scrolled, 'block': !scrolled }"
                        >تاریخ امروز</span>
                        <svg
                            class="h-6 w-6 shrink-0 text-gray-400"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            :class="{ 'block': scrolled, 'hidden': !scrolled }"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
                            />
                        </svg>
                        <span class="block pt-1 text-sm font-bold dark:font-normal">{{ todays() }}</span>

                    </div>
                </div>
            </div>
        </div>
    </nav>
    <nav
        class="z-[999] bg-white px-2 pb-3 pt-3 text-gray-700 backdrop-blur-3xl dark:bg-gray-800/85 dark:text-gray-200 lg:hidden">
        <div
            class="flex items-center justify-between"
            :class="{ 'sticky top-0': scrolled, '': !scrolled }"
        >
            <div class="flex items-center gap-3">
                <button
                    class=""
                    @click="DrawerSidebar = true"
                >
                    <svg
                        class="h-8 w-8"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M3.75 6.75h16.5M3.75 12h16.5M12 17.25h8.25"
                        />
                    </svg>
                </button>
                <div>
                    <a
                        class="text-base font-bold dark:font-normal dark:text-white"
                        href="/"
                    >سامانه تیدامد</a>
                    <p class="text-xs dark:text-white">مدیریت مشتریان و سفارشات</p>
                </div>
            </div>
            <div class="flex items-center gap-1 text-gray-700 dark:text-white">
                <button
                    class="relative p-1.5"
                    type="button"
                >
                    <svg
                        class="h-6 w-6 shrink-0"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"
                        />
                    </svg>
                </button>

                {{-- <button
                    class="relative"
                    data-tooltip-target="tooltip-setting"
                    @click="settingDrawerLeft = true;lock = true"
                >
                    <svg
                        class="h-6 w-6 shrink-0"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"
                        />
                    </svg>

                </button> --}}

                <button
                    class="relative p-1.5"
                    type="button"
                    @click="settingDrawerLeft = true;lock = true"
                >
                    <svg
                        class="h-6 w-6"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"
                        />
                    </svg>
                </button>
            </div>
        </div>
        <div
            class="flex flex-row-reverse items-center gap-2 pt-3"
            :class="{ '': scrolled, '': !scrolled }"
        >
            <div
                class="flex h-8 flex-row-reverse items-center justify-between gap-2 rounded-xl bg-gray-200 px-6 py-1.5 text-center dark:bg-gray-600 dark:text-gray-200">
                <svg
                    class="h-6 w-6 shrink-0"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M21 12a2.25 2.25 0 0 0-2.25-2.25H15a3 3 0 1 1-6 0H5.25A2.25 2.25 0 0 0 3 12m18 0v6a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 18v-6m18 0V9M3 12V9m18 0a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 9m18 0V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v3"
                    />
                </svg>
                <span class="flex items-center gap-1">
                    <span class="block text-sm font-bold dark:font-normal">0 <span
                            class="text-xs font-normal text-gray-500 dark:text-gray-400"
                        >ریال</span></span>
                </span>
            </div>
            <div class="bgGold h-8 rounded-xl bg-gray-100 px-6 py-1.5 text-center">
                <span class="flex items-center gap-1">
                    <span class="priceGold block text-sm font-bold text-gray-700"></span>
                    <span class="text-xs font-normal text-gray-500">ریال</span>
                </span>
            </div>

        </div>
    </nav>
@endpersist
