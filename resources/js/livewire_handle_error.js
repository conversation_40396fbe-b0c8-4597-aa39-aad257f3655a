document.addEventListener('livewire:init', () => {
    Livewire.hook('request', ({ fail }) => {
    fail(({ status, content, preventDefault }) => {
            if (typeof isDebugMode == 'undefined' || !isDebugMode) {
                if (status >= 400) {
                    openCustomModal(status, content);
                    preventDefault();
                }
            }
        });
    });
});

function openCustomModal(errorType, content) {
    // Create modal
    const modal = document.createElement('div');
    modal.id = 'custom-modal';
    modal.className = 'fixed inset-0 flex items-center justify-center bg-black bg-opacity-50';
    modal.style.zIndex = '20000000'; // Set z-index

    const modalContent = document.createElement('div');
    modalContent.className = 'bg-white p-8 rounded-xl shadow-md text-gray-700';

    // Create error header
    const errorHeader = document.createElement('h3');
    errorHeader.style.fontSize = '24px'; // Set header font size
    errorHeader.style.fontWeight = 'bold'; // Make header bold
    errorHeader.className = 'mb-4'; // Add margin below header

    // Create an object to hold error messages
    const errorMessages = {
        '404': 'صفحه مورد نظر یافت نشد.',
        '500': 'خطای داخلی سرور. لطفاً بعداً دوباره تلاش کنید.',
        '403': 'دسترسی ممنوع است. شما مجوز مشاهده این صفحه را ندارید.',
        '401': 'دسترسی غیرمجاز. لطفاً وارد شوید تا ادامه دهید.',
        '400': 'درخواست نامعتبر. لطفاً ورودی خود را بررسی کرده و دوباره تلاش کنید.',
        // در صورت نیاز، کدهای خطای بیشتری را اضافه کنید
    };

    // Create message element
    const message = document.createElement('p');

    // Set error number and message based on errorType
    if (errorMessages[errorType]) {
        errorHeader.innerText = `خطا ${errorType}`; // Error number
        // message.innerText = content; // Corresponding error message
        message.innerText = errorMessages[errorType]; // Corresponding error message
    } else {
        errorHeader.innerText = errorType; // Default error title for unknown errors
        message.innerText = serverMessage || 'An unexpected error occurred. Please try again.'; // Default or server message
        // message.innerText = content; // Default error message
    }

    const closeButton = document.createElement('button');
    closeButton.innerText = 'بستن'; // English message
    closeButton.className = 'mt-6 block w-full cursor-pointer rounded-full bg-red-500 p-1.5 text-base font-semibold text-white transition-all hover:bg-red-600 hover:transition-all';
    closeButton.addEventListener('click', () => {
        modal.remove(); // Remove modal
    });

    // Add content to modal
    modalContent.appendChild(errorHeader); // Add error header
    modalContent.appendChild(message); // Add error message
    modalContent.appendChild(closeButton);
    modal.appendChild(modalContent);

    // Add modal to body
    document.body.appendChild(modal);
}
